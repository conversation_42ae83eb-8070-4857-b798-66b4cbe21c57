<template>
  <ndb-page ref="pageRef">
    <el-scrollbar height="100%">
      <div class="header-box">
        <div class="header-left">
          <img v-if="avatar.length" class="avater" :src="avatar[0].fileUrl" />
          <img v-else class="avater" src="@/assets/images/avatar.png" />
          <div class="left-info">
            <span class="name">
              <span style="position: relative; padding-right: 30px">
                <encrypt
                  :field="tableDatas.costOutSignupVo.signupName"
                  :fieldEntry="tableDatas.costOutSignupVo.signupNameEncrypt"
                />
              </span>
              <!-- {{ tableDatas.costOutSignupVo.signupName }} -->
              <el-tag type="success" style="margin-left: 5px">
                {{ tableDatas.costOutSignupVo.tradeFlagName }}</el-tag
              >
            </span>
            <p class="num-box">
              <span>
                入金金额（元）：<span class="num">{{
                  tableDatas.costOutSignupVo.inAmount
                }}</span>
              </span>
              <span style="margin-left: 25px">
                出金金额（元）：<span class="num">{{
                  tableDatas.costOutSignupVo.outAmount
                }}</span>
              </span>
              <span style="margin-left: 25px">
                余额（元）：<span class="num">{{
                  tableDatas.costOutSignupVo.balanceAmount
                }}</span>
              </span>
            </p>
          </div>
        </div>
        <div>
          <nd-button
            authKey="cost:out:projectinfo"
            @click="linkToDetail"
            icon="Document"
            type="primary"
            >项目详情</nd-button
          >
        </div>
      </div>
      <div class="line"></div>
      <div class="table-content">
        <div class="table-item" v-if="tableDatas.bmList.length">
          <div class="title">材料费</div>
          <nd-table :data="tableDatas.bmList" style="width: 100%">
            <el-table-column
              prop="needPayAmount"
              align="center"
              label="材料费（元）"
              min-width="140"
            />
            <el-table-column
              prop="latePayTime"
              align="center"
              label="缴款截止日期"
              width="180"
            />
            <el-table-column
              prop="payTime"
              align="center"
              label="缴款时间"
              width="180"
            />
            <el-table-column
              prop="payStatusName"
              align="center"
              label="缴款状态"
              min-width="140"
            />
            <el-table-column
              prop="incomeExamineStatusName"
              align="center"
              label="确认收款"
              min-width="140"
            />
            <el-table-column
              prop="outAmount"
              align="center"
              label="出金金额（元）"
              min-width="140"
            />
            <el-table-column
              prop="dealTime"
              align="center"
              label="出金时间"
              width="180"
            />
            <el-table-column
              prop="balanceAmount"
              align="center"
              label="余额（元）"
              min-width="140"
            />
            <el-table-column
              prop="cleanStatusName"
              align="center"
              label="出金状态"
              min-width="140"
            />
            <el-table-column
              prop="expendExamineStatusName"
              align="center"
              label="出金申请"
              min-width="140"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="140"
            >
              <template #default="{ row }">
                <!-- <nd-button style="font-size: 13px" :key="index" v-for="(item, index) in tableBtn(row)" link type="primary" @click="item.fun(row)">
                                {{ item.text }}
                            </nd-button> -->
                <span
                  :key="index"
                  v-for="(item, index) in tableBtn(row)"
                  @click="item.fun(row)"
                  class="operateBtn"
                >
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金申请'" src="@/assets/images/chujinshneqing.png" title="出金申请" /> -->
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金审核'" src="@/assets/images/chujinshenhe.png" title="出金审核" /> -->
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金处理'" src="@/assets/images/chuli.png" title="出金处理" /> -->
                  <!-- <img :style="operaTdStyle" v-if="item.text == '查看'" src="@/assets/images/chakan.png" title="查看" /> -->
                  <nd-button
                    authKey="cost:out:apply"
                    type="chujinshneqing"
                    :style="operaTdStyle"
                    v-if="item.text == '出金申请'"
                  />
                  <nd-button
                    authKey="cost:out:examine"
                    type="chujinshenhe"
                    :style="operaTdStyle"
                    v-if="item.text == '出金审核'"
                  />
                  <nd-button
                    authKey="cost:out:deal"
                    type="chuli"
                    :style="operaTdStyle"
                    v-if="item.text == '出金处理'"
                  />
                  <nd-button
                    authKey="cost:out:list"
                    type="chakan"
                    :style="operaTdStyle"
                    v-if="item.text == '查看'"
                  />
                </span>
              </template>
            </el-table-column>
          </nd-table>
        </div>
        <div class="table-item" v-if="tableDatas.fwList.length">
          <div class="title">服务费</div>
          <nd-table :data="tableDatas.fwList" style="width: 100%">
            <el-table-column
              prop="needPayAmount"
              align="center"
              label="服务费（元）"
              min-width="140"
            />
            <el-table-column
              prop="latePayTime"
              align="center"
              label="缴款截止日期"
              width="180"
            />
            <el-table-column
              prop="payTime"
              align="center"
              label="缴款时间"
              width="180"
            />
            <el-table-column
              prop="payStatusName"
              align="center"
              label="缴款状态"
              min-width="140"
            />
            <el-table-column
              prop="incomeExamineStatusName"
              align="center"
              label="确认收款"
            />
            <el-table-column
              prop="outAmount"
              align="center"
              label="出金金额（元）"
              min-width="140"
            />
            <el-table-column
              prop="dealTime"
              align="center"
              label="出金时间"
              width="180"
            />
            <el-table-column
              prop="balanceAmount"
              align="center"
              label="余额（元）"
              min-width="140"
            />
            <el-table-column
              prop="cleanStatusName"
              align="center"
              label="出金状态"
              min-width="140"
            />
            <el-table-column
              prop="expendExamineStatusName"
              align="center"
              label="出金申请"
              min-width="140"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="140"
            >
              <template #default="{ row }">
                <!-- <nd-button style="font-size: 13px" :key="index" v-for="(item, index) in tableBtn(row)" link type="primary" @click="item.fun(row)">
                                {{ item.text }}
                            </nd-button> -->
                <span
                  :key="index"
                  v-for="(item, index) in tableBtn(row)"
                  @click="item.fun(row)"
                  class="operateBtn"
                >
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金申请'" src="@/assets/images/chujinshneqing.png" title="出金申请" />
                                <img :style="operaTdStyle" v-if="item.text == '出金审核'" src="@/assets/images/chujinshenhe.png" title="出金审核" />
                                <img :style="operaTdStyle" v-if="item.text == '出金处理'" src="@/assets/images/chuli.png" title="出金处理" />
                                <img :style="operaTdStyle" v-if="item.text == '查看'" src="@/assets/images/chakan.png" title="查看" /> -->
                  <nd-button
                    authKey="cost:out:apply"
                    type="chujinshneqing"
                    :style="operaTdStyle"
                    v-if="item.text == '出金申请'"
                  />
                  <nd-button
                    authKey="cost:out:examine"
                    type="chujinshenhe"
                    :style="operaTdStyle"
                    v-if="item.text == '出金审核'"
                  />
                  <nd-button
                    authKey="cost:out:deal"
                    type="chuli"
                    :style="operaTdStyle"
                    v-if="item.text == '出金处理'"
                  />
                  <nd-button
                    authKey="cost:out:list"
                    type="chakan"
                    :style="operaTdStyle"
                    v-if="item.text == '查看'"
                  />
                </span>
              </template>
            </el-table-column>
          </nd-table>
        </div>
        <div class="table-item" v-if="tableDatas.tbList.length">
          <div class="title">投标保证金</div>
          <nd-table :data="tableDatas.tbList" style="width: 100%">
            <el-table-column
              prop="needPayAmount"
              align="center"
              label="投标保证金（元）"
              min-width="140"
            />
            <el-table-column
              prop="latePayTime"
              align="center"
              label="缴款截止日期"
              width="180"
            />
            <el-table-column
              prop="payTime"
              align="center"
              label="缴款时间"
              width="180"
            />
            <el-table-column
              prop="payStatusName"
              align="center"
              label="缴款状态"
              min-width="140"
            />
            <el-table-column
              prop="incomeExamineStatusName"
              align="center"
              label="确认收款"
              min-width="140"
            />
            <el-table-column
              prop="outAmount"
              align="center"
              label="出金金额（元）"
              min-width="140"
            />
            <el-table-column
              prop="dealTime"
              align="center"
              label="出金时间"
              width="180"
            />
            <el-table-column
              prop="balanceAmount"
              align="center"
              label="余额（元）"
              min-width="140"
            />
            <el-table-column
              prop="cleanStatusName"
              align="center"
              label="出金状态"
              min-width="140"
            />
            <el-table-column
              prop="expendExamineStatusName"
              align="center"
              label="出金申请"
              min-width="140"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="140"
            >
              <template #default="{ row }">
                <!-- <nd-button style="font-size: 13px" :key="index" v-for="(item, index) in tableBtn(row)" link type="primary" @click="item.fun(row)">
                                {{ item.text }}
                            </nd-button> -->
                <span
                  :key="index"
                  v-for="(item, index) in tableBtn(row)"
                  @click="item.fun(row)"
                  class="operateBtn"
                >
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金申请'" src="@/assets/images/chujinshneqing.png" title="出金申请" />
                                <img :style="operaTdStyle" v-if="item.text == '出金审核'" src="@/assets/images/chujinshenhe.png" title="出金审核" />
                                <img :style="operaTdStyle" v-if="item.text == '出金处理'" src="@/assets/images/chuli.png" title="出金处理" />
                                <img :style="operaTdStyle" v-if="item.text == '查看'" src="@/assets/images/chakan.png" title="查看" /> -->
                  <nd-button
                    authKey="cost:out:apply"
                    type="chujinshneqing"
                    :style="operaTdStyle"
                    v-if="item.text == '出金申请'"
                  />
                  <nd-button
                    authKey="cost:out:examine"
                    type="chujinshenhe"
                    :style="operaTdStyle"
                    v-if="item.text == '出金审核'"
                  />
                  <nd-button
                    authKey="cost:out:deal"
                    type="chuli"
                    :style="operaTdStyle"
                    v-if="item.text == '出金处理'"
                  />
                  <nd-button
                    authKey="cost:out:list"
                    type="chakan"
                    :style="operaTdStyle"
                    v-if="item.text == '查看'"
                  />
                </span>
              </template>
            </el-table-column>
          </nd-table>
        </div>
        <div class="table-item" v-if="tableDatas.sqList.length">
          <div class="title">合同首期款</div>
          <nd-table :data="tableDatas.sqList" style="width: 100%">
            <el-table-column
              prop="needPayAmount"
              align="center"
              label="合同首期款（元）"
              min-width="140"
            />
            <el-table-column
              prop="latePayTime"
              align="center"
              label="缴款截止日期"
              width="180"
            />
            <el-table-column
              prop="payTime"
              align="center"
              label="缴款时间"
              width="180"
            />
            <el-table-column
              prop="payStatusName"
              align="center"
              label="缴款状态"
              min-width="140"
            />
            <el-table-column
              prop="incomeExamineStatusName"
              align="center"
              label="确认收款"
              min-width="140"
            />
            <el-table-column
              prop="outAmount"
              align="center"
              label="出金金额（元）"
            />
            <el-table-column
              prop="dealTime"
              align="center"
              label="出金时间"
              width="180"
            />
            <el-table-column
              prop="balanceAmount"
              align="center"
              label="余额（元）"
              min-width="140"
            />
            <el-table-column
              prop="cleanStatusName"
              align="center"
              label="出金状态"
              min-width="140"
            />
            <el-table-column
              prop="expendExamineStatusName"
              align="center"
              label="出金申请"
              min-width="140"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="140"
            >
              <template #default="{ row }">
                <span
                  :key="index"
                  v-for="(item, index) in tableBtn(row)"
                  @click="item.fun(row)"
                  class="operateBtn"
                >
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金申请'" src="@/assets/images/chujinshneqing.png" title="出金申请" />
                                <img :style="operaTdStyle" v-if="item.text == '出金审核'" src="@/assets/images/chujinshenhe.png" title="出金审核" />
                                <img :style="operaTdStyle" v-if="item.text == '出金处理'" src="@/assets/images/chuli.png" title="出金处理" />
                                <img :style="operaTdStyle" v-if="item.text == '查看'" src="@/assets/images/chakan.png" title="查看" /> -->
                  <nd-button
                    authKey="cost:out:apply"
                    type="chujinshneqing"
                    :style="operaTdStyle"
                    v-if="item.text == '出金申请'"
                  />
                  <nd-button
                    authKey="cost:out:examine"
                    type="chujinshenhe"
                    :style="operaTdStyle"
                    v-if="item.text == '出金审核'"
                  />
                  <nd-button
                    authKey="cost:out:deal"
                    type="chuli"
                    :style="operaTdStyle"
                    v-if="item.text == '出金处理'"
                  />
                  <nd-button
                    authKey="cost:out:list"
                    type="chakan"
                    :style="operaTdStyle"
                    v-if="item.text == '查看'"
                  />
                </span>
              </template>
            </el-table-column>
          </nd-table>
        </div>
        <div class="table-item" v-if="tableDatas.lyList.length">
          <div class="title">履约保证金</div>
          <nd-table :data="tableDatas.lyList" style="width: 100%">
            <el-table-column
              prop="needPayAmount"
              align="center"
              label="履约保证金（元）"
              min-width="140"
            />
            <el-table-column
              prop="latePayTime"
              align="center"
              label="缴款截止日期"
              width="180"
            />
            <el-table-column
              prop="payTime"
              align="center"
              label="缴款时间"
              width="180"
            />
            <el-table-column
              prop="payStatusName"
              align="center"
              label="缴款状态"
              min-width="140"
            />
            <el-table-column
              prop="incomeExamineStatusName"
              align="center"
              label="确认收款"
              min-width="140"
            />
            <el-table-column
              prop="outAmount"
              align="center"
              label="出金金额（元）"
              min-width="140"
            />
            <el-table-column
              prop="dealTime"
              align="center"
              label="出金时间"
              width="180"
            />
            <el-table-column
              prop="balanceAmount"
              align="center"
              label="余额（元）"
              min-width="140"
            />
            <el-table-column
              prop="cleanStatusName"
              align="center"
              label="出金状态"
              min-width="140"
            />
            <el-table-column
              prop="expendExamineStatusName"
              align="center"
              label="出金申请"
              min-width="140"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="140"
            >
              <template #default="{ row }">
                <!-- <nd-button style="font-size: 13px" :key="index" v-for="(item, index) in tableBtn(row)" link type="primary" @click="item.fun(row)">
                                {{ item.text }}
                            </nd-button> -->
                <span
                  :key="index"
                  v-for="(item, index) in tableBtn(row)"
                  @click="item.fun(row)"
                  class="operateBtn"
                >
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金申请'" src="@/assets/images/chujinshneqing.png" title="出金申请" />
                                <img :style="operaTdStyle" v-if="item.text == '出金审核'" src="@/assets/images/chujinshenhe.png" title="出金审核" />
                                <img :style="operaTdStyle" v-if="item.text == '出金处理'" src="@/assets/images/chuli.png" title="出金处理" />
                                <img :style="operaTdStyle" v-if="item.text == '查看'" src="@/assets/images/chakan.png" title="查看" /> -->
                  <nd-button
                    authKey="cost:out:apply"
                    type="chujinshneqing"
                    :style="operaTdStyle"
                    v-if="item.text == '出金申请'"
                  />
                  <nd-button
                    authKey="cost:out:examine"
                    type="chujinshenhe"
                    :style="operaTdStyle"
                    v-if="item.text == '出金审核'"
                  />
                  <nd-button
                    authKey="cost:out:deal"
                    type="chuli"
                    :style="operaTdStyle"
                    v-if="item.text == '出金处理'"
                  />
                  <nd-button
                    authKey="cost:out:list"
                    type="chakan"
                    :style="operaTdStyle"
                    v-if="item.text == '查看'"
                  />
                </span>
              </template>
            </el-table-column>
          </nd-table>
        </div>
        <div class="table-item" v-if="tableDatas.wkList.length">
          <div class="title">
            <span>合同尾款</span>
          </div>
          <nd-table
            :data="tableDatas.wkList"
            style="width: 100%"
            :header-cell-style="headerStyle"
          >
            <el-table-column type="index" align="center" label="序号" />
            <el-table-column
              prop="needPayAmount"
              align="center"
              label="合同尾款（元）"
              width="150"
            />
            <el-table-column
              prop="payAmount"
              align="center"
              label="已缴金额（元）"
              min-width="140"
            />
            <el-table-column
              prop="outAmount"
              align="center"
              label="已清算金额"
              min-width="140"
            />
            <el-table-column
              prop="balanceAmount"
              align="center"
              label="待清算金额"
              min-width="140"
            />
            <el-table-column
              prop="cleanStatusName"
              align="center"
              label="出金状态"
              min-width="140"
            />
            <el-table-column
              prop="expendExamineStatusName"
              align="center"
              label="出金申请"
              min-width="140"
            />
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              min-width="140"
            >
              <template #default="{ row }">
                <span
                  :key="index"
                  v-for="(item, index) in tableBtn(row)"
                  @click="item.fun(row)"
                  class="operateBtn"
                >
                  <!-- <img :style="operaTdStyle" v-if="item.text == '出金申请'" src="@/assets/images/chujinshneqing.png" title="出金申请" />
                                <img :style="operaTdStyle" v-if="item.text == '出金审核'" src="@/assets/images/chujinshenhe.png" title="出金审核" />
                                <img :style="operaTdStyle" v-if="item.text == '出金处理'" src="@/assets/images/chuli.png" title="出金处理" />
                                <img :style="operaTdStyle" v-if="item.text == '查看'" src="@/assets/images/chakan.png" title="查看" /> -->
                  <nd-button
                    authKey="cost:out:apply"
                    type="chujinshneqing"
                    :style="operaTdStyle"
                    v-if="item.text == '出金申请'"
                  />
                  <nd-button
                    authKey="cost:out:examine"
                    type="chujinshenhe"
                    :style="operaTdStyle"
                    v-if="item.text == '出金审核'"
                  />
                  <nd-button
                    authKey="cost:out:deal"
                    type="chuli"
                    :style="operaTdStyle"
                    v-if="item.text == '出金处理'"
                  />
                  <nd-button
                    authKey="cost:out:list"
                    type="chakan"
                    :style="operaTdStyle"
                    v-if="item.text == '查看'"
                  />
                </span>
              </template>
            </el-table-column>
          </nd-table>
        </div>
      </div>
    </el-scrollbar>
    <projectDetailDialog ref="projectDetailDialogRef" />
    <fundsOutApplicationDialog
      @refreshData="getDetail"
      ref="fundsOutApplicationDialogRef"
    />
    <fundsOutAuditDialog
      @refreshData="getDetail"
      ref="fundsOutAuditDialogRef"
    />
    <fundsOutHandleDialog
      @refreshData="getDetail"
      ref="fundsOutHandleDialogRef"
    />
  </ndb-page>
</template>

<script setup>
import { onMounted, reactive, ref, inject, watch, h, nextTick } from "vue";
import ndbPage from "@/components/business/ndbPage2/index.vue";
import ndTable from "@/components/ndTable.vue";
import ndButton from "@/components/ndButton.vue";
import { Back } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";

import projectDetailDialog from "@/views/fundsEntryManagementView/components/projectDetailDialog.vue";
import fundsOutApplicationDialog from "./fundsOutApplicationDialog.vue";
import fundsOutAuditDialog from "./fundsOutAuditDialog.vue";
import fundsOutHandleDialog from "./fundsOutHandleDialog.vue";

import encrypt from "@/views/fundsEntryManagementView/components/encrypt.vue";

import { ElMessage, ElMessageBox } from "element-plus";
const $axios = inject("$axios");
const $router = useRouter();

const emits = defineEmits(["setComponents"]);
const props = defineProps({
   params: {
    type: Object,
    default: {},
  },
});

// const operaTdStyle = { width: '16px', height: '16px', 'margin-right': '8px' }
const operaTdStyle = { "margin-right": "8px" };

let preParams = {};

const tableDatas = reactive({
  bmList: [],
  costOutSignupVo: {},
  costInTailPayVo: {},
  fwList: [],
  lyList: [],
  sqList: [],
  tbList: [],
  wkList: [],
});

// 页面挂载完触发的事件
onMounted(() => {
  getDetail();
});

function getDetail() {
  // console.log(props.toggleParams, 'props.toggleParams')
  // return
  let { payUserType, signupId, signupName, tenderId, tradeFlag, proId } =
    props.params.toggleParams;
  // return
  let params = {
    payUserType: payUserType - 0,
    signupId,
    signupName,
    tenderId,
    tradeFlag,
    proId,
  };

  // console.log(params)
  // return
  preParams = params;
  $axios({
    url: "/bankExpend/getSignupTenderDetail",
    method: "get",
    data: params,
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    let {
      bmList,
      costOutSignupVo,
      costInTailPayVo,
      fwList,
      lyList,
      sqList,
      tbList,
      wkList,
    } = res.data.data || {};

    tableDatas.bmList = processArray(bmList);
    tableDatas.costOutSignupVo = costOutSignupVo;
    tableDatas.costInTailPayVo = costInTailPayVo;
    tableDatas.fwList = processArray(fwList);
    tableDatas.lyList = processArray(lyList);
    tableDatas.sqList = processArray(sqList);
    tableDatas.tbList = processArray(tbList);
    tableDatas.wkList = processArray(wkList);

    // 获取头像
    getAvatar();
  });
}

/**
 * @description: 给数组中对象属性设置默认值'--'
 * @param {*} array
 * @return {array}
 */
function processArray(array) {
  let includesKey = [
    "needPayAmount",
    "latePayTime",
    "payTime",
    "payStatusName",
    "payTime",
    "incomeExamineStatusName",
    "outAmount",
    "dealTime",
    "balanceAmount",
    "cleanStatusName",
    "expendExamineStatusName",
  ];
  for (let i = 0; i < array.length; i++) {
    const obj = array[i];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (includesKey.includes(key)) {
          if (!obj[key] && obj[key] !== 0) {
            obj[key] = "--";
          }
          if (obj[key].includes("无需出金")) {
            obj[key] = "--";
          }
        }
      }
    }
  }
  return array;
}

let projectDetailDialogRef = ref({ open: "dsadsa" });
function linkToDetail() {
  projectDetailDialogRef.value.open(props.params.toggleParams.proId);
}

const payZzhkDialogRef = ref();
const yjkDialogRef = ref();

/**
 * @description: 操作按钮展示
 * @param {*} row
 * @return {*}
 */
function tableBtn(row) {
  if (row.opreateAuth == 1)
    return [
      { text: "出金申请", fun: applicationHandle },
      { text: "查看", fun: handleView },
    ];
  if (row.opreateAuth == 2) return [{ text: "出金审核", fun: auditHandle }];
  if (row.opreateAuth == 3) return [{ text: "出金处理", fun: processHanlde }];
  if (row.opreateAuth == 4) return [{ text: "查看", fun: handleView }];

  return [{ text: "-", fun: () => {} }];
  // return [{ text: '查看', fun: handleView }]
}

const fundsOutApplicationDialogRef = ref();
const fundsOutAuditDialogRef = ref();
const fundsOutHandleDialogRef = ref();

// 出金申请
function applicationHandle(row) {
  fundsOutApplicationDialogRef.value.open(row);
}

// 出金审核
function auditHandle(row) {
  fundsOutAuditDialogRef.value.open(row);
}

// 出金处理
function processHanlde(row) {
  fundsOutHandleDialogRef.value.open(row);
}

// 查看
function handleView(row) {
  fundsOutHandleDialogRef.value.open(row, 2);
}

let avatar = ref("");
// 获取头像
function getAvatar() {
  if (!tableDatas.costOutSignupVo.vipId) return;
  $axios({
    url: "/file/getFileCommon",
    method: "get",
    data: {
      busId: tableDatas.costOutSignupVo.vipId,
      configFileId: 7,
    },
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    avatar.value = res.data.data || [];
  });
}

function handleBack() {
  emits("setComponents", 1);
}
</script>
<style scoped lang="scss">
:deep(.el-table .el-table__cell) {
  padding: 2px 0;
}
.header-box {
  height: 78px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;

  .avater {
    width: 63px;
    height: 63px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .left-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 50px;
    font-size: 14px;

    color: #909399;

    // align-items: flex-end;
    .name {
      font-size: 16px;
      font-weight: 550;
      display: flex;
      align-items: center;
      color: #444444;
    }

    .num-box {
      display: flex;
      align-items: center;
    }

    .num {
      color: #444444;
      margin-right: 10px;
    }
  }
}

.line {
  height: 10px;
  background: #f7f7f7;
}

.table-content {
  padding: 15px;

  .title {
    color: #0b8df1;
    font-weight: 550;
    font-size: 16px;
    padding-bottom: 5px;
    padding-top: 8px;
  }
}

.back-btn {
  position: fixed;
  top: 74px;
  right: 20px;
}

.operateBtn {
  color: #0b8df1;
  cursor: pointer;
  display: inline-block;
  // padding-top: 8px;
}
</style>
