<template>
  <iframe :src="src" frameborder="0" allowfullscreen></iframe>
</template>

<script setup>
import { onMounted, ref } from "vue";
// 引入子组件
const src = ref("");
const ndbPageRef = ref("");

// 定义ref
onMounted(() => {
    let data = getRequestParams();
    var path='contractingApprovalView';
  if (process.env.NODE_ENV === "development") {
    src.value = `http://localhost:5188/#/${path}?ac=`+ localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue&deptType=" + localStorage.getItem("deptType") + "&account=" + localStorage.getItem("cqcpToken") + "&t=" + new Date().getTime()+"&data="+encodeURIComponent(data.params)+"&type="+data.type;
  } else {
      src.value = 'http://*************:9754/cq-village2/#/' + `${path}?ac=` + localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue&deptType=" + localStorage.getItem("deptType") + "&account=" + localStorage.getItem("cqcpToken") + "&t=" + new Date().getTime()+"&data="+encodeURIComponent(data.params)+"&type="+data.type;
  }
});
function getRequestParams() {
    let url = location.href;
    let requestParams = {};
    if (url.indexOf("?") !== -1) {
        let str = url.substring(url.indexOf("?") + 1);
        let strs = str.split("&");
        for (let i = 0; i < strs.length; i++) {
            requestParams[strs[i].split("=")[0]] = decodeURI(strs[i].split("=")[1]);
        }
    }
    return requestParams;
}
</script>

<style lang="scss" scoped>
iframe {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
</style>
