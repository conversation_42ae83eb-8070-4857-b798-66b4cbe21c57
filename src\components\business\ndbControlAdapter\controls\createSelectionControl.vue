<template>
    <!-- 复选框 -->
    <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 20">
        <!-- <nd-select v-model="value"  filterable allow-create default-first-option :reserve-keyword="false"
            placeholder="Choose tags for your article" >
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </nd-select> -->
        <el-autocomplete  :disabled="disabled" :maxLength="maxLength" width="90%"
            value-key="mortgageeName" :fetch-suggestions="querySearchAsync3" @select="handleSelect3" v-model="inputValue"
            :placeholder="disabled == true ? '' : placeholder" clearable>
            <!-- <template #suffix>
                <span style="font-size:12px;cursor:pointer;color:#0B8DF1;" @click="handleIconClick2">请选择</span>
            </template> -->
        </el-autocomplete>
    </nd-search-more-item>
    <!-- 复选框 -->

</template>

<script setup>
// 导入公共组件
import ndSearchMoreItem from '@/components/business/ndbControlAdapter/controlItem.vue';
import ndSelect from "@/components/ndSelect.vue";

import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref, inject, watch, getCurrentInstance, nextTick, computed } from 'vue';
const $axios = inject("$axios");
const currentInstance = getCurrentInstance()
const props = defineProps({
    type: {// 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
        type: Number,
        default: "default",
    },
    width: {//宽度
        type: String,
        default: "",
    },
    title: {//标题
        type: String,
        default: "",
    },
    disabled: { // 只读
        type: Boolean,
        default: false,
    },
    requireFlag: { // 是否必填
        type: Number,
        default: 1,//1是0否
    },
    modelValue: {//双向绑定
        type: String,
    },
    jsonData: {
        type: Array,
        default: [],
    },
    dictType: {
        type: String,
        default: "default",
    },
    paramKey: {
        type: String,
        default: "default",
    },
    showFlag: {
        type: Boolean,
        default: true,
    },
    jibie: {
        type: Boolean,
        default: true,
    },
    jibiecun: {
        type: Boolean,
        default: true,
    },
    dataType: {
        type: Number,
        default: 1,//1文本 2整数 3小数 4日期
    },
    maxLength: {//输入长度
        type: String,
        default: '100',
    },
    paramUnit: {
        type: String,
        default: '',
    },
    unitId: {
        type: String,
        default: '',
    },
    isFlag: {
        type: Boolean,
        default: false,
    },
    LZFS: {
        type: String,
        default: '',
    }, objVal: {
        type: Object,
        default: {},
    }, objVal2: {
        type: Object,
        default: {},
    }, projectType: {
        type: Number,
        default: '',
    }, childObj: {
        type: Object,
        default: {},
    }
})
const emits = defineEmits(["update:modelValue"])//定义方法名
const inputValue = computed({//监听
    get() {
        return props.modelValue
    },
    set(value) {
        emits("update:modelValue", value)
    }
})
const querySearchAsync3 = async (queryString, cb) => {
    var results = []
    if (queryString==''||queryString==null||queryString==undefined) {
        cb(results);
    } else{
        try {
            let result = []
            $axios({
                method: "get",
                url: "/mortgageMortgagee/listPage",
                    data:{
                        mortgageeName:queryString
                    }
            })
                .then((response) => {
                    if (response.data.code == 200) {
                        result = response.data.data.records
                        if (result) {
                            cb(result);
                        } else {
                            result = []
                            cb(results);
                        }
                    } else {
                        ElMessage.error(response.data.msg)
                        cb(results);
                    }
                })
                .catch(() => {

                });

        } catch (error) {
            console.log(error);
        }
    }
   

}

onMounted(() => {


})
</script>

<style lang='scss' scoped>
.set-up-class {
    :deep(.left-box) {
        span::before {
            content: "*";
            color: red;
        }
    }
}

:deep(.left-box) {
    text-align: right;
    min-width: 150px !important;
}

// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
    width: 100%;
}

:deep(.el-select) {
    width: 100%;
}

:deep(.el-autocomplete) {
    width: 100%;
}

:deep(.el-cascader) {
    width: 100%;
}

.nd-input-box :deep(.el-input) {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
    width: 100%;
    // padding-left: 10px;
    padding-right: 10px;
}

:deep(.el-input.is-disabled) {
    width: 100%;
}

:deep(.el-date-editor.el-input) {
    width: 100%;
}
</style>