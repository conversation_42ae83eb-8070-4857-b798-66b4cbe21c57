<template>
    <div>优先权</div>
</template>

<script setup>
// 导入公共组件
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref, inject, watch, getCurrentInstance, nextTick, computed } from 'vue';
const $axios = inject("$axios");
const currentInstance = getCurrentInstance()
const props = defineProps({
    type: {// 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
        type: Number,
        default: "default",
    },
})

onMounted(() => {

})

const querySearchAsync2 = async (queryString, cb) => {
    $axios({
        url: "/transferor/getDept/" + props.unitId,
        method: "get",
    }).then((res) => {
        if (res.data.code === 200) {
            const restaurants = res.data.data;
            cb(restaurants)
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}


</script>

<style lang='scss' scoped></style>