---
description: 
globs: 
alwaysApply: true
---
# 树形组件规范

## 基础组件说明

### ndTree 组件
- **位置**: `src/components/ndTree.vue`
- **功能**: 普通树形控件，用于展示层级结构数据
- **特点**: 基于 el-tree 封装，支持自定义节点内容和操作

### ndTreeSelect 组件
- **位置**: `src/components/ndTreeSelect.vue`
- **功能**: 下拉选择形式的树形控件
- **特点**: 基于 el-tree-select 封装，支持单选/多选模式

## 组件使用场景

### 地区树（区域选择）
- 用于展示和选择行政区域数据
- 常用于数据筛选、统计分析等场景
- 支持懒加载模式，按需加载子节点

### 组织机构树
- 用于展示和选择组织机构数据
- 常用于人员管理、权限分配等场景
- 支持节点勾选和禁用功能

## ndTree 使用方式

### 基本使用
```vue
<template>
  <ndTree
    :data="treeData"
    :props="defaultProps"
    highlight-current
    :expand-on-click-node="false"
    @node-click="handleNodeClick"
  />
</template>

<script setup>
import ndTree from "@/components/ndTree.vue";
import { ref } from "vue";

const treeData = ref([
  {
    id: '1',
    label: '节点1',
    children: [
      { id: '1-1', label: '子节点1-1' }
    ]
  }
]);

const defaultProps = {
  children: 'children',
  label: 'label',
};

const handleNodeClick = (data) => {
  console.log(data);
};
</script>
```

### 自定义节点
```vue
<ndTree :data="treeData" :props="defaultProps">
  <template #default="{ node, data }">
    <span>{{ node.label }}</span>
    <span v-if="data.extraInfo">({{ data.extraInfo }})</span>
  </template>
</ndTree>
```

### 使用实例方法
```vue
<template>
  <ndTree ref="treeRef" :data="treeData" :props="defaultProps" show-checkbox />
  <nd-button @click="getCheckedNodes">获取选中节点</nd-button>
</template>

<script setup>
import ndTree from "@/components/ndTree.vue";
import { ref } from "vue";

const treeRef = ref();
const treeData = ref([/* 数据 */]);

const getCheckedNodes = () => {
  const nodes = treeRef.value.getCheckedNodes();
  console.log(nodes);
};
</script>
```

## ndTreeSelect 使用方式

### 基本使用
```vue
<template>
  <ndTreeSelect
    v-model="selectedValue"
    :data="treeData"
    :props="defaultProps"
    placeholder="请选择"
  />
</template>

<script setup>
import ndTreeSelect from "@/components/ndTreeSelect.vue";
import { ref } from "vue";

const selectedValue = ref('');
const treeData = ref([/* 数据 */]);
const defaultProps = {
  value: 'id',
  label: 'label',
  children: 'children',
};
</script>
```

### 懒加载模式（常用于地区树）
```vue
<template>
  <ndTreeSelect
    v-model="selectedValue"
    lazy
    :load="loadNode"
    :props="lazyProps"
    placeholder="请选择地区"
  />
</template>

<script setup>
import ndTreeSelect from "@/components/ndTreeSelect.vue";
import { ref, inject } from "vue";

const $axios = inject('$axios');
const selectedValue = ref('');

const lazyProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  isLeaf: 'leaf'
};

// 懒加载节点
const loadNode = (node, resolve) => {
  if (node.level === 0) {
    // 加载根节点
    loadAreaData('0', resolve);
  } else {
    // 加载子节点
    loadAreaData(node.data.id, resolve);
  }
};

// 获取地区数据
const loadAreaData = (parentId, resolve) => {
  $axios({
    url: `/area/getChildrenByParentId?parentId=${parentId}`,
    method: 'get',
  }).then((res) => {
    if (res.data.code === 200) {
      resolve(res.data.data || []);
    } else {
      resolve([]);
    }
  });
};
</script>
```

### 多选模式
```vue
<ndTreeSelect
  v-model="selectedValues"
  :data="treeData"
  :props="defaultProps"
  multiple
  show-checkbox
  placeholder="请选择多个选项"
/>
```

## 地区树应用场景

### 列表筛选
- 用于数据列表的区域筛选
- 通常与其他筛选条件组合使用
- 支持选择任意层级的地区节点

### 表单录入
- 用于表单中地区字段的选择
- 可配置为必选或可选
- 通常需要记录完整的地区路径

### 统计分析
- 用于数据统计的维度选择
- 支持按地区层级展示统计结果
- 常配合图表组件使用

## 组织树应用场景

### 权限管理
- 用于分配角色或权限
- 通常以勾选方式进行多选
- 支持按组织结构层级选择

### 人员选择
- 用于按组织架构选择人员
- 可展示组织内的人员列表
- 支持组织筛选和搜索

## 数据格式规范

### 基本数据结构
```js
// 标准树形数据结构
[
  {
    id: '1',           // 唯一标识
    label: '节点名称',  // 显示文本
    children: [        // 子节点
      {
        id: '1-1',
        label: '子节点名称',
        children: []
      }
    ]
  }
]

// 地区树数据结构
[
  {
    id: '110000',        // 地区编码
    name: '北京市',      // 地区名称
    parentId: '0',       // 父级编码
    children: [          // 子地区
      {
        id: '110100',
        name: '北京市',
        parentId: '110000'
      }
    ]
  }
]
```

### 懒加载数据结构
```js
// 懒加载节点结构
{
  id: '110000',        // 唯一标识
  name: '北京市',      // 显示名称
  leaf: false,         // 是否叶子节点
  parentId: '0'        // 父节点ID
}
```

## 性能优化建议

### 大数据量处理
- 使用懒加载模式，避免一次性加载大量数据
- 适当限制展开层级，防止过多节点渲染
- 考虑使用虚拟滚动，提高长列表性能

### 搜索优化
- 实现前端过滤或后端搜索功能
- 添加节点缓存机制，避免重复请求
- 适当使用防抖，减少频繁请求

## 注意事项
- 树形控件容器高度需要设置，避免样式问题
- 懒加载模式需要处理加载失败的情况
- 在表单中使用时，注意初始值和重置逻辑
- 树节点层级过深时，考虑使用搜索或其他优化方式

