---
description: 
globs: 
alwaysApply: true
---
# API 接口开发规范

## 基本原则

### RESTful 设计
- 使用标准 HTTP 方法：GET、POST、PUT、DELETE
- URI 设计使用名词而非动词
- 使用复数名词来表示资源集合
- 使用状态码表达操作状态

### 错误处理
- 使用标准 HTTP 状态码
- 提供统一的错误响应格式
- 详细说明错误原因
- 包含唯一错误标识便于追踪

## 接口设计规范

### 接口命名
- URI 使用小写字母
- 使用连字符（-）代替下划线（_）
- 避免使用文件扩展名
- 保持命名简洁明了

### 请求方法
- GET：获取资源
- POST：创建资源
- PUT/PATCH：更新资源
- DELETE：删除资源

### 路径设计
- `/api/v1/resources`：资源列表
- `/api/v1/resources/{id}`：单个资源
- `/api/v1/resources/{id}/sub-resources`：子资源列表
- `/api/v1/resources/{id}/sub-resources/{sub-id}`：单个子资源

## 数据交互规范

### 请求参数
- 查询参数：使用 URL 参数，如 `/api/users?page=1&size=10`
- 路径参数：使用路径变量，如 `/api/users/{id}`
- 请求体参数：使用 JSON 格式，如 `{ "name": "张三" }`

### 响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 业务数据
  }
}
```

### 错误响应格式
```json
{
  "code": 400,
  "msg": "参数错误",
  "data": null,
  "error": {
    "field": "username",
    "message": "用户名不能为空"
  }
}
```

### 分页格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "list": [],
    "total": 100,
    "page": 1,
    "size": 10,
    "pages": 10
  }
}
```

## 业务状态码规范

### 状态码范围
- 200-299：成功
- 400-499：客户端错误
- 500-599：服务器错误
- 600-699：业务错误
- 800-899：系统错误（如802表示licence过期）

### 业务状态码定义
- 200：操作成功
- 401：未授权（token无效或过期）
- 403：权限不足
- 802：licence过期
- 600：参数验证失败
- 601：业务规则验证失败

## 接口安全规范

### 身份认证
- 使用 token 进行身份认证
- 敏感接口使用 HTTPS
- 登录接口实现防暴力破解机制

### 数据安全
- 敏感数据传输加密
- 敏感数据存储加密
- 参数验证防止注入攻击

## HTTP 请求与响应规范

### 请求头规范
- `Content-Type`: 对于 POST 和 PUT 请求使用 `application/json`
- `Authorization`: 使用 `Bearer {token}` 格式
- `Accept-Language`: 指定响应语言

### 响应头规范
- `Content-Type`: 使用 `application/json; charset=utf-8`
- `Cache-Control`: 控制缓存行为
- `X-Request-Id`: 唯一请求标识，用于日志追踪

## 接口响应规范

### 响应状态码
- 200：操作成功
- 401：未授权，跳转登录页
- 403：权限不足
- 802：license过期（跳转到系统管理页面）

### 请求拦截器
- 自动添加 token 到请求头
- 处理请求地址前缀，根据配置区分环境
- 取消重复请求的处理

### 响应拦截器
- 处理未授权状态(401)，自动跳转到登录页
- 处理权限不足(403)，显示警告信息
- 处理 license 过期(802)，跳转到系统管理页面

