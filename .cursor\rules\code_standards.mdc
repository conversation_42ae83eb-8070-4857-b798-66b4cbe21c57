---
description: 
globs: 
alwaysApply: true
---
# 新产权管理端项目代码规范

## 项目概述
- 项目使用 Vue 3 + Vite 构建
- 使用 Element Plus 作为 UI 组件库
- 使用 Vue Router 进行路由管理
- Axios 用于处理 HTTP 请求
- 使用 SCSS 进行样式管理

## 项目结构
```
src/                # 源代码目录
├── assets/         # 静态资源，包含图片、CSS、字体等
├── components/     # 公共组件
│   └── business/   # 业务组件
├── directive/      # 自定义指令
├── http/           # 网络请求相关
├── router/         # 路由配置
├── views/          # 页面视图
├── views2/         # 扩展页面视图
└── viewsWx/        # 微信端页面视图
```

## 代码风格与约定

### 组件命名约定
- 通用组件以 `nd` 前缀命名，如 `ndButton.vue`
- 业务组件以 `ndb` 前缀命名，如 `ndbButtonSearch.vue`
- 页面组件以 `View` 后缀命名，如 `welcomeView`

### Vue 组件结构
- 使用 `<script setup>` 语法
- 按 template > script > style 顺序组织文件
- `props` 定义使用 `defineProps`，事件使用 `defineEmits`

### HTTP 请求约定
- 使用 axios 拦截器处理请求和响应
- 请求拦截器中自动添加 token
- 响应拦截器处理错误码和跳转逻辑
- 接口响应码：
  - 401：未授权，跳转登录页
  - 403：权限不足
  - 802：license过期（跳转到系统管理页面）

### 样式约定
- 使用 SCSS 预处理器
- 样式采用 scoped 隔离
- 颜色以主题蓝 #0b8df1 为主

### 自定义指令
- `v-thousands`：数字千分位格式化
- `v-empty`：空值显示为 "--"

### 状态管理
- 主要使用组件内部状态管理
- localStorage 用于跨页面状态存储
- 常用状态：token、权限、用户信息等

### 命名规范
- 变量命名：驼峰式 (camelCase)
- 组件文件名：驼峰式 (PascalCase)
- CSS 类名：短横线分隔 (kebab-case)

### 注释规范
- 组件顶部添加组件说明注释
- 复杂逻辑添加注释说明
- 函数需要添加功能说明注释


