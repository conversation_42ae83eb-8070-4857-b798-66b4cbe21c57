---
description: 
globs: 
alwaysApply: true
---
# 组件开发规范

## 组件设计原则

### 单一职责
- 每个组件应只负责单一功能
- 组件过大时应考虑拆分成多个子组件

### 可复用性
- 业务组件设计时应考虑复用场景
- 通用功能应抽象为公共组件

### 可测试性
- 组件设计应便于单元测试
- 避免过于复杂的组件逻辑

## 组件使用规则
- 通用组件以 `nd-*` 开头，是 Element Plus 组件的二次封装，样式微调，用法与原组件基本一致
- 业务组件以 `ndb-*` 开头
- 开发时优先考虑使用项目内的组件，不要直接使用 Element Plus 原生组件
- 如需引入新组件库，需评估与现有组件的兼容性

## 组件文件结构

```vue
<template>
  <!-- 组件模板结构 -->
</template>

<script setup>
// 导入 Vue API
import { ref, watch, onMounted } from "vue";

// 定义 props
const props = defineProps({
  propName: {
    type: String,
    default: '',
  },
});

// 定义 emits
const emits = defineEmits(["eventName"]);

// 组件状态
const componentState = ref(null);

// 生命周期钩子
onMounted(() => {
  // 初始化逻辑
});

// 方法定义
function handleEvent() {
  emits("eventName", componentState.value);
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
.component-class {
  display: flex;
}
</style>
```

## 组件命名规范

### 文件名
- 通用组件：`nd` + 组件名，如 `ndButton.vue`
- 业务组件：`ndb` + 组件名，如 `ndbButtonSearch.vue`
- 页面组件：组件名 + `View`，如 `welcomeView.vue`
- 子组件：放置在父组件同级的 `components` 目录下

### 组件名
- 使用 PascalCase 命名：`NdbButtonSearch`
- 在模板中使用连字符形式：`<ndb-button-search>`

## Props 定义规范

### 必要属性
- `name`：属性名
- `type`：属性类型
- `required`：是否必须
- `default`：默认值（非必须属性）
- `validator`：验证函数（可选）

### 示例
```js
defineProps({
  title: {
    type: String,
    default: "",
  },
});
```

## 事件处理规范

### 命名规范
- 事件名使用 kebab-case：`item-click`
- 处理函数使用 camelCase 并添加 `handle` 前缀：`handleItemClick`

### 示例
```vue
<template>
  <el-button @click="handleClick">点击</el-button>
</template>

<script setup>
const emits = defineEmits(["item-click"]);

function handleClick(event) {
  emits("item-click", { id: 1, name: "测试" });
}
</script>
```

## 组件通信方式

1. **Props/Events**：父子组件通信首选
2. **localStorage/sessionStorage**：跨组件状态持久化

## 多端开发注意事项

### 微信小程序兼容
- 在开发可能用于微信小程序的组件时，需考虑H5和微信小程序的兼容性
- 避免使用小程序不支持的API和样式
- viewsWx目录下的组件需要特别注意兼容性问题
