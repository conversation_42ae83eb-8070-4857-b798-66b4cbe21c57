---
description: 
globs: 
alwaysApply: true
---
# 路由组织规范

## 路由配置

### 基础路由配置
```js
const routes = [
  {
    path: '/welcomeView',           // 路径
    name: 'welcomeView',            // 名称
    component: () => import('@/views/welcomeView/index.vue'),  // 懒加载组件
  }
]
```

## 路由守卫

### 全局前置守卫
```js
router.beforeEach((to, from) => {
  // 取消之前的请求
  window.axiosPromiseArr.forEach((ele, index) => {
    ele.cancel();
  })
  window.axiosPromiseArr = [];
  window.axiosPromiseArrName = [];

  // 登录检查逻辑根据实际需求开启
  // if (to.path === '/' || to.path === '/loginView' || to.path === '/registerView') {
  //     // 什么也不做
  // } else {
  //     if (!localStorage.getItem("cqcpyhToken")) {
  //         // 将用户重定向到登录页面
  //         return { path: 'loginView' }
  //     }
  // }
})
```

### 路由模式
- Hash 模式：`createWebHashHistory()`
