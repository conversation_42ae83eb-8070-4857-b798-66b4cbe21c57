<template>
    <div class="nd-date-picker-box" :style="{ width: width }">
        <template v-if="!['daterange', 'datetimerange'].includes(props.type)">
            <el-date-picker
                v-model="dateData.time"
                ref="datePicker"
                v-bind="$attrs"
                :teleported="teleported"
                @change="selectTime"
                :type="dateData.type"
            ></el-date-picker>
        </template>
        <template v-else>
            <div style="display: flex; align-items: center">
                <el-date-picker
                    v-model="dateData.startTime"
                    ref="datePicker"
                    v-bind="$attrs"
                    :teleported="teleported"
                    @change="selectDatePicker"
                    :type="dateData.type"
                ></el-date-picker>
                <span style="margin: 5px">-</span>
                <el-date-picker
                    v-model="dateData.endTime"
                    ref="datePicker"
                    v-bind="$attrs"
                    :teleported="teleported"
                    @change="selectDatePicker"
                    :type="dateData.type"
                ></el-date-picker>
            </div>
        </template>
    </div>
</template>

<script setup>
import { reactive, watch } from "vue";

// emit
const emit = defineEmits(["update:modelValue"]);

let props = defineProps({
    // 宽度
    width: {
        type: String,
        default: "230px",
    },
    // 是否插入至body元素
    teleported: {
        type: Boolean,
        default: false,
    },
    type: {
        type: String,
        default: "",
    },
    modelValue: {
        default: "",
    },
});

const dateData = reactive({
    startTime: "",
    endTime: "",
    time: "",
    type: props.type,
});

// 监听modelValue 用于范围时间选择器的回显
watch(
    () => props.modelValue,
    () => {
        // 判断是否是范围选择器
        if (["daterange", "datetimerange"].includes(props.type)) {
            // 处理拆分后的单个选择器的类型
            if (props.type === "datetimerange") {
                dateData.type = "datetime";
            }

            if (props.type === "daterange") {
                dateData.type = "date";
            }

            // 判断v-model绑定的是否为数组并进行回显赋值
            if (
                Object.prototype.toString
                    .call(props.modelValue)
                    .includes("Array")
            ) {
                dateData.startTime = props.modelValue[0] ?? "";
                dateData.endTime = props.modelValue[1] ?? "";
            }
        } else {
            // 单个时间选择器的赋值
            dateData.time = props.modelValue;
        }
    },
    { immediate: true, immediate: true }
);

/**
 * 选中时间 范围选择器
 *
 * @returns {void}
 */
function selectDatePicker() {
    emit("update:modelValue", [dateData.startTime, dateData.endTime]);
}

/**
 * 选中事件 单选
 *
 * @returns {void}
 */
function selectTime() {
    emit("update:modelValue", dateData.time);
}
</script>

<style lang="scss" scoped>
.nd-date-picker-box {
    height: auto;

    :deep(.el-range-editor.el-input__wrapper) {
        width: 100%;
    }
    :deep(.el-range-editor.is-disabled .el-textarea__inner) {
        background-color: #fafafa;
    }
    :deep(.el-range-editor.is-disabled .el-input__wrapper) {
        background-color: #fafafa;
    }
    :deep(.el-range-editor.is-disabled input) {
        background-color: #fafafa;
    }
    // :deep(.el-input__inner) {
    //   width: 100%;
    //   height: 30px;
    //   line-height: 30px;
    //   font-size: 14px;
    // }

    // :deep(.el-date-editor.el-input) {
    //   width: 100%;
    // }

    // :deep(.el-range-input) {
    //   font-size: 14px;
    // }

    // :deep(.el-input__icon) {
    //   line-height: 100%;
    // }

    // :deep(.el-range-separator) {
    //   width: auto;
    //   height: auto;
    //   line-height: normal;
    //   font-size: 14px;
    // }

    :deep(.el-input__wrapper) {
        height: 30px;
    }

    :deep(.el-range-editor.is-disabled input) {
        background: transparent;
    }

    :deep(.el-input__wrapper) {
        // box-shadow: 0 0 0 0.5px #dcdfe6 inset;
        border: 1px solid #dcdfe6;
        box-shadow: 0 0 0 0px #dcdfe6 inset;
    }

    :deep(.el-input__wrapper:hover) {
        // box-shadow: 0 0 0 0.5px #dcdfe6 inset;
        border: 1px solid #dcdfe6;
        box-shadow: 0 0 0 0px #dcdfe6 inset;
    }
}
</style>
