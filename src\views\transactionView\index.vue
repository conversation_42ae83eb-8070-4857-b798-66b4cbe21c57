<template>
  <ndb-page ref="ndbPageRef" backgroundColor="#f7f8fa">
  <iframe :src="src" frameborder="0" allowfullscreen></iframe>
  </ndb-page>
</template>

<script setup>
import { onMounted, ref, inject } from "vue";
const $axios = inject("$axios");
import { useRouter } from 'vue-router';
import ndbPage from "@/components/business/ndbPage/index.vue";
 
const router = useRouter();
// 引入子组件
const src = ref("");
// 监听 iframe 内部消息
const handleMessage = (event) => {
  // 安全校验（必须检查 origin！）
  // if (event.origin !== "http://your-iframe-domain.com") return;
//  let projectType = router.currentRoute.value.query.projectType;
  if (event.data.type === "CLOSE_IFRAME"&&getRequestParams().type==='3') {
    // router.go(-1);
    router.push({path:"../transactionManageView",query:{projectType:getRequestParams().projectType}})
  }
};
const ndbPageRef = ref("");

// 定义ref
onMounted(() => {
  //主持人roleType=3
    let data = getRequestParams();
    console.log(data,"data")
    var path='';
    if(data.type==='1'){
        path='contractingApplicationView'
    }else if(data.type==='2'){
        path='contractingApprovalView'
    }else if(data.type==='3'){
        ndbPageRef.value.addBreadcrumb("项目登记");
        path='contractingInputView'
    }
  if (process.env.NODE_ENV === "development") {
    src.value = `http://localhost:5188/#/${path}?ac=`+ localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue&deptType=" + localStorage.getItem("deptType") + "&account=" + localStorage.getItem("cqcpToken") + "&t=" + new Date().getTime()+"&data="+encodeURIComponent(data.params)+"&type="+data.type;
  } else {
      src.value = 'http://*************:9754/cq-village2/#/' + `${path}?ac=` + localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue&deptType=" + localStorage.getItem("deptType") + "&account=" + localStorage.getItem("cqcpToken") + "&t=" + new Date().getTime()+"&data="+encodeURIComponent(data.params)+"&type="+data.type;
    // if (window.ipConfig && window.ipConfig.ztbUrl && window.ipConfig.ztbUrl.tenderUrl2) {
    //   src.value = 'http://*************:9754/cq-gl/#/' + `${path}?ac=` + localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue&deptType=" + localStorage.getItem("deptType") + "&account=" + localStorage.getItem("cqcpToken") + "&t=" + new Date().getTime()+"&data="+encodeURIComponent(data.params)+"&type="+data.type;
    // }
  }
window.addEventListener("message", handleMessage);
  // setTimeout(function(){
  //   router.go(-1);
  // },2000)
  // if (process.env.NODE_ENV === "development") {
  //   src.value = "http://localhost:10810/#/managerView?account=" + localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue";
  // } else {
  //   if (window.ipConfig && window.ipConfig.ztbUrl && window.ipConfig.ztbUrl.tenderUrl) {
  //     src.value = window.ipConfig.ztbUrl.tenderUrl + "managerView?account=" + localStorage.getItem("cqcpToken") + "&roleType=3&entryType=new&theme=blue";
  //   }
  // }
});
function getRequestParams() {
    let url = location.href;
    let requestParams = {};
    if (url.indexOf("?") !== -1) {
        let str = url.substring(url.indexOf("?") + 1);
        let strs = str.split("&");
        for (let i = 0; i < strs.length; i++) {
            requestParams[strs[i].split("=")[0]] = decodeURI(strs[i].split("=")[1]);
        }
    }
    return requestParams;
}
</script>

<style lang="scss" scoped>
iframe {
  width: 100%;
  height: 100%;
  vertical-align: top;
}
</style>
