<template>
  <!-- 乡镇街道 -->
  <!-- <div
    v-if="type == 7 && dataList.length != 0"
    style="display: flex; align-items: center; height: 100%; width: 100%"
  > -->
    <div
    v-if="type == 7"
    style="display: flex; align-items: center; height: 100%; width: 100%"
  >
    <div :style="matterFlag==2?'display: flex; flex-direction: row; width: 100%':'display: flex; flex-direction: row; width: 50%'">
      <nd-search-more-item
        :class="requireFlag == 1 ? 'set-up-class' : ''"
        :title="title"
        style="width: 100%"
        v-if="matterFlag==2"  :requires="requireFlag"
    :itemValue="objVal2.defaultValue" :saveOperate="saveOperate"
      >
        <!-- style="flex:1"> -->
        <el-tree-select
          v-model="objVal2.defaultValue" :accordion="true"
          :disabled="disabled || props.jibie == false"
          width="90%"
          
          lazy
          :load="treeLoad" value-key="id"
          ref="treeRef"
          :render-after-expand="false"  :default-expanded-keys="defaultExpandedKeys"  :default-checked-keys="defaultExpandedKeys"
          :props="defaultProps" node-key="id" 
        />
      </nd-search-more-item>
      <nd-search-more-item
      v-else
        :class="requireFlag == 1 ? 'set-up-class' : ''"
        :title="title"
        style="width: 50%" :requires="requireFlag"
    :itemValue="objVal2.defaultValue" :saveOperate="saveOperate"
      >
        <!-- style="flex:1"> -->
        <el-tree-select :accordion="true"
          v-model="objVal2.defaultValue"  value-key="id" 
          :disabled="disabled || props.jibie == false" :default-expanded-keys="defaultExpandedKeys"  :default-checked-keys="defaultExpandedKeys"
          width="90%"
          
          lazy
          :load="treeLoad"
          ref="treeRef"
          :render-after-expand="false"
          :props="defaultProps" node-key="id" 
          @node-click="handleNodeClick" @node-expand="handleNodeClick"
        />
      </nd-search-more-item>
      <!-- 村 -->
      <nd-search-more-item :requires="objVal2.fieldList[0].requireFlag"
    :itemValue="objVal2.fieldList[0].defaultValue" :saveOperate="saveOperate"
        v-if="
          objVal2 &&
          objVal2.fieldList &&
          objVal2.fieldList[0].fieldKey == 'villageId' 
        "
        :class="objVal2.fieldList[0].requireFlag == 1 ? 'set-up-class' : ''"
        :title="title?'村':''"
        style="width: 47%"
        class="zb"
      >
        <!-- :class="requireFlag == 1 ? 'set-up-class' : ''" title="村" :style="{ width: width }" style="flex:1"> -->
        <el-tree-select
          :disabled="disabled || props.jibiecun == false"
          v-model="objVal2.fieldList[0].defaultValue"
          width="90%"  clearable @clear="clear()"
          :data="dataList2"
          ref="treeRef2"
          :placeholder="disabled == true ? ' ' : '请选择'"
          :render-after-expand="false"
          :props="{ value: 'id', label: 'name' }"
          @node-click="handleNodeClick2"
        />
      </nd-search-more-item>
    </div>
    <div style="width: 50%" v-if="matterFlag!=2">
      <!-- 组别 -->
      <nd-search-more-item  :requires="objVal2.fieldList[1].requireFlag"
        v-if="
          objVal2 &&
          objVal2.fieldList &&
          objVal2.fieldList[1].fieldKey == 'groupCode'
        "
        :class="objVal2.fieldList[1].requireFlag == 1 ? 'set-up-class' : ''"
        :title="title?'组别':''"
        style="width: 100%"
      >
        <!-- :class="requireFlag == 1 ? 'set-up-class' : ''"  title="组别" :style="{ width: width }" style="flex:1"> -->
        <nd-input
          v-model="objVal2.fieldList[1].defaultValue"
          :disabled="disabled" :maxLength="objVal2.fieldList[1].dataLength"
          :placeholder="disabled == true ? '' : placeholder"
          width="90%"
          clearable
        ></nd-input>
      </nd-search-more-item>
    </div>
  </div>
  <div
    v-if="type == 7 && items.matterFlag == 1"
    style="
      display: flex;
      align-items: center;
      height: 100%;
      width: 98%;
      flex-direction: column; margin-bottom: 15px
    "
  >
  
    <!-- 标的物组件 -->
    <!-- <nd-search-more-item
    title="交易标的物" style="width:100%"
     width="100%"
  > -->
    <!-- <div  class="set-up-class2"
      style="
        width: 100%;
        font-size: 14px;
        color: #555555;
        padding-bottom: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      "
    >
      <span>交易标的物</span>
      <div v-if="!disabled">
        <nd-button icon="Plus" @click="addForm(1)">新增标的物</nd-button>
        <nd-button icon="Plus" @click="addForm(2)">从标的物库新增</nd-button>
      </div>
    </div> -->
    <div  
      style="
        width: 100%;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        padding: 10px 10px;
        border: 1px solid #eeeeee;
        border-bottom: none;
      "
    >
    <div  class="set-up-class2"
      style="
        width: 100%;
        font-size: 14px;
        color: #555555;
        padding-bottom: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
      "
    >
      <span>交易标的物</span>
      <div v-if="!disabled">
        <i @click="addForm(1)"  style="font-size: 14px; color: #0B8DF1;cursor:pointer;font-style: normal;margin-left: 10px;">新增标的物</i>
        <i @click="addForm(2)" style="font-size: 14px; color: #0B8DF1;cursor:pointer;font-style: normal;margin-left: 10px;">从标的物库新增</i>
        <!-- <nd-button icon="Plus" @click="addForm(1)">新增标的物</nd-button>
        <nd-button icon="Plus" @click="addForm(2)">从标的物库新增</nd-button> -->
      </div>
    </div>
    <div style="
        width: 100%;
        font-size: 14px;
        color: #555555;
        padding-bottom: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
      ">
      <span style="margin-right: 10px">本次交易标的数量合计:</span>
      <nd-input disabled placeholder="自动计算" style="width: 100px" v-model="tenderData.length" clearable></nd-input>
      <span style="margin-left: 10px">个</span>
      <span class="set-class" style="margin-right: 10px; margin-left: 20px"
        >面积/数量单位:</span
      >
      <nd-select :disabled="disabled" style="width: 100px" v-model="matterAreaUnit" >
        <el-option v-for="item in optionList2" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue" />
      </nd-select>
      <span style="margin-right: 10px; margin-left: 20px"
        >交易面积/数量合计:</span
      >
      <nd-input :disabled="disabled" placeholder="自动计算支持修改" v-model="matterArea" clearable :formatter="priceFormat2" :parser="priceFormat2"></nd-input>
    </div>
      <!-- <span style="margin-right: 10px">本次交易标的数量合计:</span>
      <nd-input disabled placeholder="自动计算" v-model="tenderData.length" clearable></nd-input>
      <span style="margin-left: 10px">个</span>
      <span class="set-class" style="margin-right: 10px; margin-left: 20px"
        >面积/数量单位:</span
      > -->
      
      <!-- <nd-select :disabled="disabled" style="margin-left: 10px; width: 100px" v-model="matterAreaUnit" >
        <el-option v-for="item in optionList2" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue" />
      </nd-select> -->
    </div>
    <!-- <nd-table  :class="disabled ? 'butClick' : ''"
      style="height: 350px; width: 100%; margin-bottom: 15px"
      :data="tenderData"
    > -->
    <nd-table  
      style="width: 100%;" 
      :data="pagedData"
    >
      <el-table-column
        align="center"
        :index="indexMethod"
        type="index"
        label="序号"
        width="80"
      />
      <el-table-column align="center" prop="description" label="* 标的物名称">
        <template #header>
          <span class="set-class">标的物名称</span>
        </template>
        <template #default="{ row }">
          <el-tooltip effect="light" :content="row.name">
          <nd-input  v-if="row.assetId==''||row.assetId==null" :disabled="disabled"
            style="width: 100%" maxLength="50"
            placeholder="请输入"
            clearable
            v-model="row.name"
          ></nd-input>
          <span v-else>{{ row.name }}</span>
        </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="标的物编号">
        <template #header>
          <span>标的物编号</span>
        </template>
        <template #default="{ row }">
          <span style="color:#C0C4CC;">{{ row.code || "无需填写" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="标的物类型">
        <template #default="{ row }">
          <el-select v-model="row.type" :disabled="disabled" placeholder="请选择" v-if="row.assetId==''||row.assetId==null" >
            <el-option
            v-for="item in optionList1" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue">
            </el-option>
          </el-select>
          <span v-else style="text-align:left;display:inline-block;width:100%;">{{ row.type }}</span>
        </template>
        <!-- <template #default="{ row }">
          <nd-select @change="changeType" style="width: 100%" v-model="row.value3"> 
            <el-option v-for="item in optionList1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
          </nd-select>
        </template> -->
      </el-table-column>
      <el-table-column align="right" prop="description" label="*交易面积/数量">
        <template #header>
          <span class="set-class">交易面积/数量</span>
        </template>
        <template #default="{ row }">
          <el-tooltip effect="light" :content="row.matterArea">
            <nd-input class="nd-input-bdw" :disabled="disabled"
            style="width: 100%;" @blur="blur" @input="input"  :formatter="priceFormat" :parser="priceFormat"
            placeholder="请输入"
            clearable
            v-model="row.matterArea"
          ></nd-input>
        </el-tooltip>
        <!-- <span style="font-size:12px;color:red;">交易面积/数量不能为空</span> -->
          
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="*单位">
        <template #header>
          <span class="set-class">单位</span>
        </template>
        <template #default="{ row }">
          <el-select  :disabled="disabled" v-if="row.assetId==''||row.assetId==null" v-model="row.matterAreaUnit" placeholder="请选择"  style="width: 100%">
            <el-option
            v-for="item in optionList2" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue">
            </el-option>
          </el-select>
          <span v-else  style="text-align:left;display:inline-block;width:100%;">{{ row.matterAreaUnit }}</span>
          <!-- <nd-select @change="changeUnit" style="width: 100%"  v-model="row.value5">
            <el-option v-for="item in optionList1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
          </nd-select> -->
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="description"
        label="交易底价"
      >
      <!-- <el-table-column
        align="center"
        prop="description"
        label="预期价格（元）"
      > -->
        <template #header>
          <span>交易底价</span>
        </template>
        <template #default="{ row }">
          <el-tooltip effect="light" :content="row.matterPrice">
            <nd-input
            style="width: 100%" :disabled="disabled"  @blur="blur"   :formatter="priceFormat1" :parser="priceFormat1"
            placeholder="请输入"
            clearable
            v-model="row.matterPrice"
          ></nd-input>
        </el-tooltip>
         
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="价格单位">
        <template #default="{ row }">
          <el-select v-model="row.matterPriceUnit" clearable :disabled="disabled" placeholder="请选择"  >
          <!-- <el-select v-model="row.matterPriceUnit" :disabled="disabled" placeholder="请选择" v-if="row.assetId==''||row.assetId==null" > -->
            <el-option
            v-for="item in optionList3" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey">
            </el-option>
          </el-select>
          <!-- <span v-else style="text-align:left;display:inline-block;width:100%;">{{ row.matterPriceUnit }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        align="center"
        prop="status"
        width="80"
        label="操作"
      >
        <template #default="scoped">
          <span :class="disabled ? 'butClick' : ''" style="cursor: pointer;color:#0b8df1;"  @click.stop="deleteTender(scoped.row, scoped.$index)">删除</span>
          <!-- <el-tooltip effect="light" content="删除">
            <el-button
              link
              @click.stop="deleteTender(scoped.row, scoped.$index)"
            >
              <img
                src="@/assets/images/transactionManageImage/delete.png"
                alt=""
              />
            </el-button>
          </el-tooltip> -->
        </template>
      </el-table-column>
    </nd-table>

    <div
              class="pagination-box"
              style="width: 100%;
                padding: 10px 15px;
                background: #fff;
                border: 1px solid #eeeeee;
                border-top: none;
              " v-if="page.total2>=10"
            >
              <nd-pagination
                v-model:total="page.total2"
                v-model:current-page="proForm.pageNo2"
                v-model:page-size="proForm.pageSize2"
                @size-change="handleSizeChange2"
                @current-change="handleCurrentChange2"
              >
              </nd-pagination>
            </div>
    <!-- </nd-search-more-item> -->
    <!-- 标的物组件 -->
  </div>
  <ndDialog
    ref="dialog"
   
    title="从标的物库新增"
    style="overflow: hidden;padding: 0px 10px"
  >
    <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center">
      <ndButton
        @click="toSure"
        style="width:80px;"
        type="primary"
        color="#0b8df1"
        >确认</ndButton
      >
      <div style="display: flex;flex-direction: row;justify-content: space-between;">
        <nd-input
            style="width: 100%;margin-right: 10px" v-model.trim="proForm.assetName" maxLength="50"
            placeholder="资产/资源名称"
            clearable
          ></nd-input>
          <ndButton
        @click="toSearch()"
        style="width:80px;"
        type="primary"
        color="#0b8df1"
        >查询</ndButton
      >
      </div>
    </div>
    <nd-table
      style="height: 360px; width: 100%; margin-bottom: 15px;margin-top: 10px;" ref="tableRef"
      :data="tenderData2"  row-key="id" @selection-change="handleSelectionChange"
    >
    <el-table-column
      type="selection" label="选择"
      width="55">
    </el-table-column>
      <el-table-column align="center" prop="assetName" label="资产/资源名称">
      </el-table-column>
      <el-table-column align="center" prop="description" label="所属组织">
      </el-table-column>
      <el-table-column align="center" prop="assetCode" label="资产/资源编号">
      </el-table-column>
      <!-- <el-table-column align="center" prop="amount" label="面积/数量">
      </el-table-column> -->
      <el-table-column align="center" prop="amount" label="面积/数量">
        <template #default="{ row }">
          <span>{{ row.amount }}</span><span>{{ row.unit }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" prop="overplusAmount" label="可申请面积/数量">
      </el-table-column> -->
      <el-table-column align="center" prop="overplusAmount" label="可申请面积/数量">
        <template #default="{ row }">
          <span>{{ row.overplusAmount }}</span><span>{{ row.unit }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="assetType" label="类型">
      </el-table-column>
    </nd-table>
    <template #footer>
      <div style="width:100%;margin:0 auto;display:flex;flex-direction:row;">
      <nd-pagination v-model:total="page.total" v-model:current-page="proForm.pageNo" :pager-count="3"
          v-model:page-size="proForm.pageSize" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
      </nd-pagination>
    </div>
    </template>
  </ndDialog>
</template>

<script setup>
// 导入公共组件
import ndTreeSelect from "@/components/ndTreeSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndSearchMoreItem from "@/components/business/ndbControlAdapter/controlItem.vue";
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndPagination from "@/components/ndPagination.vue";

import { ElMessage, ElMessageBox } from "element-plus";

import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
const $axios = inject("$axios");
const currentInstance = getCurrentInstance();
const props = defineProps({
  type: {
    // 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
    type: Number,
    default: "default",
  },
  width: {
    //宽度
    type: String,
    default: "",
  },
  title: {
    //标题
    type: String,
    default: "",
  },
  disabled: {
    // 只读
    type: Boolean,
    default: false,
  },
  requireFlag: {
    // 是否必填
    type: Number,
    default: 1, //1是0否
  },
  modelValue: {
    //双向绑定
    type: String || Object,
  },
  jsonData: {
    type: Array,
    default: [],
  },
  dictType: {
    type: String,
    default: "default",
  },
  paramKey: {
    type: String,
    default: "default",
  },
  showFlag: {
    type: Boolean,
    default: true,
  },
  jibie: {
    type: Boolean,
    default: true,
  },
  jibiecun: {
    type: Boolean,
    default: true,
  },
  dataType: {
    type: Number,
    default: 1, //1文本 2整数 3小数 4日期
  },
  maxLength: {
    //输入长度
    type: String,
    default: "100",
  },
  paramUnit: {
    type: String,
    default: "",
  },
  unitId: {
    type: String,
    default: "",
  },
  isFlag: {
    type: Boolean,
    default: false,
  },
  LZFS: {
    type: String,
    default: "",
  },
  objVal: {
    type: Object,
    default: {},
  },
  projectType: {
    type: Number,
    default: "",
  },
  childObj: {
    type: Object,
    default: {},
  },
  objVal2: {
    type: Object,
    default: {},
  },
  items: {
    type: Object,
    default: {},
  },
  contractFlags: {
        type: Boolean,
        default: false,
    },
    matterFlag: {
    type: Number,
    default: 0,
  },
  saveOperate: {//保存触发
      type: Boolean,
      default: false,
    },
});
const emits = defineEmits([
  "update:modelValue",
  "changeTree",
  "getTenderData",
  "changeYXQXQ",
  "changeYXQGZ",
  "changeFWFSQGZ",
  "changeLYSQGZ",
  "changeZJDJ",
  "changeShowZj2",
  "changeShowZj",
  "mapClick",
  "changeNcp",
  "changePriceUpper",
  "regPhone",
  "changeCertNo",
  "changeOrg",
]); //定义方法名    
const defaultProps = {  //tree-prop
  children: 'children',
  label: 'name',
  value: "id",
  isLeaf: "isLeaf"
}
let checkList = ref([]);
let dataList = ref([]); //乡镇
let dataList2 = ref([]); //村社区
let flags=ref(false)
const proForm = reactive({
    pageNo: 1,
    pageSize: 10,
    pageNo2: 1,
    pageSize2: 10,
    assetName:''
})
const page = reactive({
    total: '',
    total2: '',
})
const dialog = ref(null);
let tenderData = ref([
  {
    id: "",
    code: "",//编号
    serialNo: "",//序号
    name: "",//名称
    matterArea: "",//交易面积/数量
    matterAreaUnit: "",//交易面积/数量单位
    assetId:"",
    matterPrice: "",//"预期价格（分）"
    matterPrice: "",//标的物类型
    matterPriceUnit: "",//标的物类型
    type:""//标的物类型
  }
]);
let pagedData=ref([])
let tenderData2 = ref([])
// let matterArea=ref('')
let optionList1 = ref([])//标的物类型下拉
let optionList2 = ref([])//单位下拉
let optionList3 = ref([])//单位下拉
let matterArea=ref('')//标的物数量合计
let matterAreaUnit=ref('')//标的物数量合计单位
let lastSelection=ref([])
let cancelledIds=ref([])

const priceFormat = (value, int = 6) => {//输入框正则
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const priceFormat1 = (value, int = 9) => {//输入框正则
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const priceFormat2 = (value, int = 9) => {//输入框正则
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const indexMethod = (index) => {//序号
  return (proForm.pageNo2 - 1) * proForm.pageSize2 + index + 1
  // if (index == 0) {
  //   return 1;
  // } else {
  //   return index + 1;
  // }
};
const tableRef = ref(null);

let addForm = (th) => {//新增
  if (th == 1) {
    //新增
    tenderData.value.push({
      id: "",
      code: "",//编号
      serialNo: "",//序号
      name: "",//名称
      matterArea: "",//交易面积/数量
      assetId:"",
      matterAreaUnit: "",//交易面积/数量单位
      matterPrice: "",//"预期价格（分）"
      matterPriceUnit: "",//"预期价格（分）"
      type:""//标的物类型
    });
  } else {
    arrys.value=[];
    dialog.value.open();
    console.log(tenderData2.value)
    console.log(tenderData.value)
    setTimeout(function () {
    nextTick(() => {
      tenderData2.value.forEach((ele) => {
        if (tenderData.value.some((item) => item.assetId == ele.id)) {
          console.log(ele);
          tableRef.value.toggleRowSelection(ele, true);
        }
      });
    });
  }, 1000);
  }
};
let deleteTender = (rows, index) => {//删除
  // 当前标的物关联的权属和评估信息将一并删除，是否删除？
  ElMessageBox.confirm(
    '当前标的物关联的权属和评估信息将一并删除，是否删除？',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }
  )
    .then(() => {
      if (tenderData.value.length == 1) {
        ElMessage.error('至少保留一条数据')
        return;
      }
      flags.value=false;
      tenderData.value.forEach((item, i) => {
        if (index == i) {
          tenderData.value.splice(index + (proForm.pageNo2 - 1) * proForm.pageSize2, 1);
          // tenderData.value.splice(i, 1);
        }
      });
    })
    .catch(() => {
      
    })
 
};
const getProjectMatterList = () => {//获取标的物类型
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=BDWLX",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList1.value = response.data.data; 
        console.log(optionList1.value)
      } else {
      }
    })
    .catch(() => {});
};
const getUnit = () => {//获取标的物单位
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=BDWDW",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList2.value = response.data.data; 
      } else {
      }
    })
    .catch(() => {});
};
const getUnit2 = () => {//获取价格单位
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=DJDW",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList3.value = response.data.data; 
      } else {
      }
    })
    .catch(() => {});
};
const getbdwList = () => {//获取资源列表
  $axios({
    method: "get",
    url: "/tradeAsset/listPage",
    data: {
            overPlusLeastAmount:0,
            assetName: proForm.assetName,
            pageSize: proForm.pageSize,
            pageNo: proForm.pageNo
        }
  })
    .then((response) => {
      if (response.data.code == 200) {
        tenderData2.value = response.data.data.records;
        proForm.pageNo = response.data.data.current;
        page.total = response.data.data.total
        setTimeout(function () {
          nextTick(() => {
            tenderData2.value.forEach((ele) => {
              if (tenderData.value.some((item) => item.assetId == ele.id)) {
                console.log(ele);
                tableRef.value.toggleRowSelection(ele, true);
              }
            });
          });
        }, 1000);
      } else {
      }
    })
    .catch(() => {});
};
let defaultExpandedKeys = ref([])
// let defaultExpandedKeys = ref(["3781d2cf21cb11ec8850f48e38bf4326","3781d2cf21cb11ec8850f48e38bf4326","3781d3ca21cb11ec8850f48e38bf4326","3782b00a21cb11ec8850f48e38bf4326"])
// let defaultExpandedKeys = ref(["3781d2cf21cb11ec8850f48e38bf4326","3781d2cf21cb11ec8850f48e38bf4326","3781d3ca21cb11ec8850f48e38bf4326","3782b00a21cb11ec8850f48e38bf4326"])
// let defaultExpandedKeys = ref("666")

const getChildren = (val) => { 
  let params = {
    areaId: val,
    minLevel: "5",
    // type: "1",
    useToken: "1",
  };
  $axios({
    method: "get",
    url: "/area/lazyLoadRegionTree",
    data: params,
  })
    .then((response) => {
      if (response.data.code == 200) {
        dataList2.value = response.data.data;
      } else {
      }
    })
    .catch(() => {});
};
const toSearch=()=>{
  getbdwList()
}
const toSure = () => {
  // if(arrys.value.length==0){
  //   ElMessage.error("请选择要操作的记录");
  //   return
  // }
  tenderData.value.map(item=>{ 
    arrys.value = arrys.value.filter(item2 => item2.id != item.assetId);
})
console.log(arrys.value)

  arrys.value.forEach(item=>{
    let obj={};
    obj.id='';
    obj.code=item.assetCode;
    obj.assetId=item.id;
    obj.name=item.assetName;
    obj.matterArea=item.overplusAmount;
    obj.matterAreaUnit=item.unit;
    obj.matterPrice='';
    obj.matterPriceUnit='';
    obj.type=item.assetType;
    // obj.type=item.type;
    console.log(obj,'---')
    tenderData.value.push(obj)
  })

  cancelledIds.value.map(item=>{ 
    tenderData.value = tenderData.value.filter(item2 => item2.assetId != item);
  })
    // tenderData.value.forEach((item,i)=>{
    //   cancelledIds.value.forEach((item2,j)=>{
    //       if(item.code==item2){
    //         tenderData.value.splice(i,1)
    //       }
    //   })
    // })
  
  dialog.value.close();
 
};
const getTenderData = inject("getTenderData");
function handleCurrentChange(val) {
    proForm.pageNo = val
    tenderData2.value = []
    getbdwList()
}
let arrys = ref([]);
function handleInput(value){
  // console.log(value)
  // // 只允许在末尾输入点
  // const regex = /^.*[.]?$/;
  //     if (!regex.test(value)) {
  //       // 如果输入的不符合规则，则将值设置为上一个合法的值
  //      return value.substring(0, value.length - 1)
  //     }

}
function blur(e){
  console.log(tenderData.value,'tenderData.value')
  tenderData.value.forEach(item=>{
    if(item.matterArea!=''){
      item.matterArea=Number(item.matterArea).toFixed(4)
    // item.matterPrice=Number(item.matterPrice).toFixed(2)
    }
    if(item.matterPrice){
    // if(item.matterPrice!=''){
      // item.matterArea=Number(item.matterArea).toFixed(4)
    item.matterPrice=Number(item.matterPrice).toFixed(2)
    }
  })

  // sumTotal(tenderData.value)
  // console.log(e.target.value)
  // let index = e.target.value.indexOf('.')
  // console.log(index)

}
function input(e){
  flags.value=false;

  // sumTotal(tenderData.value)
  // console.log(e.target.value)
  // let index = e.target.value.indexOf('.')
  // console.log(index)

}
// 选中选项触发
const handleSelectionChange = (selection) => {
  cancelledIds.value = lastSelection.value.filter(item => !selection.some(sel => sel.id === item.id)).map(item => item.id);
  lastSelection.value=selection
  arrys.value = selection;
};
function handleSizeChange(val) {
    proForm.pageSize = val
    tenderData2.value = []
    getbdwList()
}
function handleSizeChange2(val) {
    proForm.pageSize2 = val
    handleCurrentChange2(1)
    // tenderData2.value = []
    // getbdwList()
}
function handleCurrentChange2(val) {
    proForm.pageNo2 = val
    const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
    const endIndex = startIndex + proForm.pageSize2;
    pagedData.value = tenderData.value.slice(startIndex, endIndex);
    // tenderData2.value = []
    // getbdwList()
}
function sumTotal(val){
  const sortedButtonList = tenderData.value.map(function (value, index) {
          if(matterAreaUnit.value=='平方米'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?value.matterArea:value.matterAreaUnit=='亩'?(value.matterArea/0.0015).toFixed(4):value.matterAreaUnit=='公顷'?(value.matterArea*10000).toFixed(4):value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else if(matterAreaUnit.value=='亩'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?(value.matterArea*0.0015).toFixed(4):value.matterAreaUnit=='亩'?value.matterArea:value.matterAreaUnit=='公顷'?(value.matterArea*15).toFixed(4):value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else if(matterAreaUnit.value=='公顷'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?(value.matterArea/10000).toFixed(4):value.matterAreaUnit=='亩'?(value.matterArea/15).toFixed(4):value.matterAreaUnit=='公顷'?value.matterArea:value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else{
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }
          });
        let sum=0;
        for (let i=0;i<sortedButtonList.length;i++){
          if(matterAreaUnit.value=='平方米'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            if(matterAreaUnit.value=='亩'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            if(matterAreaUnit.value=='公顷'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            // if(matterAreaUnit.value=='个'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='本'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='台'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='棵'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }


            
            // if(matterAreaUnit.value=='个'==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='本'==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='台'==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // matterArea.value=sum
            matterArea.value=sum.toFixed(4)
        }
    // let sum=0;
    // for (let i=0;i<newValue.length;i++){
    //     sum+=Number(newValue[i].matterArea);
    //     // matterArea.value=sum
    //     matterArea.value=sum.toFixed(4)
    // }
    page.total2 = tenderData.value.length
    const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
    const endIndex = startIndex + proForm.pageSize2;
    pagedData.value = tenderData.value.slice(startIndex, endIndex);
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
    getTenderData(val,matterArea.value,matterAreaUnit.value)
    }
    }
}
watch(
  () => tenderData.value,
  (newValue, oldValue) => {
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      const sortedButtonList = tenderData.value.map(function (value, index) {
          if(matterAreaUnit.value=='平方米'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?value.matterArea:value.matterAreaUnit=='亩'?(value.matterArea/0.0015).toFixed(4):value.matterAreaUnit=='公顷'?(value.matterArea*10000).toFixed(4):value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else if(matterAreaUnit.value=='亩'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?(value.matterArea*0.0015).toFixed(4):value.matterAreaUnit=='亩'?value.matterArea:value.matterAreaUnit=='公顷'?(value.matterArea*15).toFixed(4):value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else if(matterAreaUnit.value=='公顷'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?(value.matterArea/10000).toFixed(4):value.matterAreaUnit=='亩'?(value.matterArea/15).toFixed(4):value.matterAreaUnit=='公顷'?value.matterArea:value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else{
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }
          });
        let sum=0;
        for (let i=0;i<sortedButtonList.length;i++){
          if(matterAreaUnit.value=='平方米'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            if(matterAreaUnit.value=='亩'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            if(matterAreaUnit.value=='公顷'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            // if(matterAreaUnit.value=='个'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='本'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='台'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='棵'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            // if(matterAreaUnit.value=='个'==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='本'==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='台'==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // matterArea.value=sum
            if(flags.value==true){
              matterArea.value=JSON.parse(JSON.stringify(props.items.matterVo.matterArea))
            }else{
              matterArea.value=sum.toFixed(4)
              
            }
            setTimeout(function(){
                flags.value=false;
              },1000)
            // if(props.items.matterVo.matterArea){
            //   matterArea.value=JSON.parse(JSON.stringify(props.items.matterVo.matterArea))
            // }else{
            //   matterArea.value=sum.toFixed(4)
            // }
        }
    // let sum=0;
    // for (let i=0;i<newValue.length;i++){
    //     sum+=Number(newValue[i].matterArea);
    //     // matterArea.value=sum
    //     matterArea.value=sum.toFixed(4)
    // }
    page.total2 = tenderData.value.length
    const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
    const endIndex = startIndex + proForm.pageSize2;
    pagedData.value = tenderData.value.slice(startIndex, endIndex);
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
    getTenderData(newValue,matterArea.value,matterAreaUnit.value)
    }
    }
    }
   
   
  },
  { deep: true, immediate: true }
);
watch(
  () => matterArea.value,
  (newValue, oldValue) => {
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
      getTenderData(tenderData.value,newValue,matterAreaUnit.value)
    }
    }
   
  },
  { deep: true, immediate: true }
);
watch(
  () => matterAreaUnit.value,
  (newValue, oldValue) => {
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
        const sortedButtonList = tenderData.value.map(function (value, index) {
          if(matterAreaUnit.value=='平方米'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?value.matterArea:value.matterAreaUnit=='亩'?(value.matterArea/0.0015).toFixed(4):value.matterAreaUnit=='公顷'?(value.matterArea*10000).toFixed(4):value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else if(matterAreaUnit.value=='亩'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?(value.matterArea*0.0015).toFixed(4):value.matterAreaUnit=='亩'?value.matterArea:value.matterAreaUnit=='公顷'?(value.matterArea*15).toFixed(4):value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else if(matterAreaUnit.value=='公顷'){
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterAreaUnit=='平方米'?(value.matterArea/10000).toFixed(4):value.matterAreaUnit=='亩'?(value.matterArea/15).toFixed(4):value.matterAreaUnit=='公顷'?value.matterArea:value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }else{
            return { 
              assetId: value.assetId,
              code: value.code,
              id: value.id,
              matterArea: value.matterArea,
              matterAreaUnit: value.matterAreaUnit,
              matterPrice: value.matterPrice,
              name: value.name,
              projectId: value.projectId,
              serialNo: value.serialNo,
              type: value.type,
            };
           }
          });
        let sum=0;
        for (let i=0;i<sortedButtonList.length;i++){
            if(matterAreaUnit.value=='平方米'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            if(matterAreaUnit.value=='亩'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            if(matterAreaUnit.value=='公顷'&&(sortedButtonList[i].matterAreaUnit=='平方米'||sortedButtonList[i].matterAreaUnit=='亩'||sortedButtonList[i].matterAreaUnit=='公顷')){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            // if(matterAreaUnit.value=='个'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='本'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='台'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // if(matterAreaUnit.value=='棵'&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
            // if(matterAreaUnit.value==sortedButtonList[i].matterAreaUnit &&(matterAreaUnit.value!='平方米'||matterAreaUnit.value!='亩'||matterAreaUnit.value!='公顷') ){
            // sum+=Number(sortedButtonList[i].matterArea);
            // }
            // matterArea.value=sum
            // matterArea.value=sum.toFixed(4)
            if(flags.value==true){
              matterArea.value=JSON.parse(JSON.stringify(props.items.matterVo.matterArea))
            }else{
              matterArea.value=sum.toFixed(4)
              
            }
            setTimeout(function(){
                flags.value=false;
              },1000)
        }
      getTenderData(tenderData.value,matterArea.value,newValue)
    }
    }
    
  },
  { deep: true, immediate: true }
);
// watch(
//   () => props.objVal2,
//   (newValue, oldValue) => {
//     if (
//       props.objVal2.paramKey == "townId" ||
//       props.objVal2.fieldKey == "townId"
//     ) {
//       nextTick(() => {
//         asd();
//       });
//       console.log(
//         props.objVal2.defaultValue,
//         "props.objV2al22.defaultValue1222"
//       );
//       getChildren(props.objVal2.defaultValue);
//     }
//   },
//   { deep: true, immediate: true }
// );
const treeForm = reactive({
  minLevel: 4,
  useToken: 1,
  areaId: "",
})
// watch(() => props.objVal2.defaultValue, (newValue, oldValue) => {
//   console.log(props.objVal2.defaultValue,'props.objVal2.defaultValue')
//   // treeForm.areaId = props.objVal2.defaultValue
// })
const treeLoad = async (node, resolve) => {
  if (node.level != 0) treeForm.areaId = node.data.id
  resolve(await getAreaTree())
}
function getAreaTree() {
  return new Promise((resolve, reject) => {
    $axios({
      method: "get",
      data: treeForm,
      url: "/area/lazyLoadRegionTree"
    }).then(res => {
      if (res.data.code == 200) {
        // if (!treeForm.areaId) props.objVal2.defaultValue = defaultExpandedKeys.value = res.data.data[0].id;
        // props.objVal2.defaultValue='222'
        resolve(res.data.data)
      } else {
        reject("请求失败");
      }
    })
  })
}
// const load = (node, resolve) => {
//   if (node.isLeaf) return resolve([])
//   let params = {
//     areaId: props.unitId,
//     minLevel: "4",
//     // type: "1",
//     useToken: "1",
//   };
//   $axios({
//     method: "get",
//     url: "/area/lazyLoadRegionTree",
//     data: params,
//   })
//     .then((response) => {
//       if (response.data.code == 200) {
//         resolve(response.data.data)
//       } else {
//       }
//     })
//     .catch(() => {});

//   // setTimeout(() => {
//   //   resolve([
//   //     {
//   //       value: ++id,
//   //       label: `lazy load node${id}`,
//   //     },
//   //     {
//   //       value: ++id,
//   //       label: `lazy load node${id}`,
//   //       isLeaf: true,
//   //     },
//   //   ])
//   // }, 400)
// }
const asd = () => {
  let params = {
    areaId: props.unitId,
    minLevel: "4",
    // type: "1",
    useToken: "1",
  };
  $axios({
    method: "get",
    url: "/area/lazyLoadRegionTree",
    data: params,
  })
    .then((response) => {
      if (response.data.code == 200) {
        dataList.value = response.data.data;
      } else {
      }
    })
    .catch(() => {});
};

watch(
  () => props.items.matterVo,
  (newValue, oldValue) => {
    if (props.objVal2.paramKey == "townId"&&props.items.matterFlag==1) {
      if(props.items.matterVo){
        matterArea.value = props.items.matterVo.matterArea
      matterAreaUnit.value = props.items.matterVo.matterAreaUnit
      // tenderData.value = props.items.matterVo.tradeMatterVoList
      }
      
    }
    // if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
    //   console.log(newValue,'newValuenewValue')

    // }
  },
  { deep: true, immediate: true }
);

const nodeName = ref(); //回显值
const nodeName2 = ref(); //回显值
let deptId = reactive({
  id: "",
});
const changeTree = inject("changeTree");
const getTree1 = inject("getTree1");
const getTree2 = inject("getTree2");
const getTree3 = inject("getTree3");
const clear=()=>{
  let node={name:''}
  getTree2(node)
}
function handleNodeClick(node) {
  props.objVal2.fieldList[0].defaultValue = "";
  console.log(node);
  // if(node.level==2){
  //   getTree3(node)
  // }
  // if(node.level==3){
  //   getTree3(node)
  // }
  getChildren(node.id);
  // changeTree(node.id, 2)
  // emits('changeTree', node.id, 2)
  getTree1(node,'','')
  let node2={name:''}
  getTree2(node2)
}
function handleNodeClick2(node) {
  console.log(props.objVal2);
  getTree2(node)
  // emits('update:modelValue', node.id);//v-model方式
  // nodeName2.value = node.name;
}

const state = reactive({
  timeout: null,
  RWMC: "",
});

watch(
  // () => props.items.matterVo,
  () => props.items,
  (newValue, oldValue) => {
    if (props.objVal2.paramKey == "townId"&&props.items.matterFlag==1) {
      if(props.items.matterVo){
    console.log(props.items,'props.items.matterVo')
        matterArea.value = JSON.parse(JSON.stringify(props.items.matterVo.matterArea))
      matterAreaUnit.value = JSON.parse(JSON.stringify(props.items.matterVo.matterAreaUnit))
      // tenderData.value = JSON.parse(JSON.stringify(props.items.matterVo.tradeMatterVoList))
      flags.value=true;
      }
      
    }
  },
  { deep: true, immediate: true }

);
watch(
  () => props.objVal2.defaultValue,
  (newValue, oldValue) => {
    if (
    props.objVal2.paramKey == "townId" ||
    props.objVal2.fieldKey == "townId"
  ){
    getChildren(props.objVal2.defaultValue);
    $axios({
    method: "post",
    url: "/area/getAreaListById?areaId="+props.objVal2.defaultValue,
  })
    .then((response) => {
      if (response.data.code == 200) {
      let array=response.data.data.reverse();
      array = array.slice(0, array.length - 1);
      console.log(array,'array') 
      array.forEach(item=>{
        defaultExpandedKeys.value.push(item.id)
      })
      // defaultExpandedKeys.value.push(props.objVal2.defaultValue)
      } else {
      }
    })
    .catch(() => {});
  }
  },
  { deep: true, immediate: true }

);
onMounted(() => {
  // getList();
  if (props.type == "9" || props.type == 9) {
    if (props.modelValue == null || props.modelValue == undefined) {
    } else {
      let str = props.modelValue;
      checkList.value = str.split(",");
    }
  }
  if (
    props.objVal2.paramKey == "townId" ||
    props.objVal2.fieldKey == "townId"
  ) {
    if(props.jibiecun == false){
      treeForm.areaId = props.unitId
    }
    asd();
    //   if (props.objVal2.defaultValue && props.objVal2.paramKey == "townId") {
    console.log(props.objVal2, "props.objV2al22.defaultValue1222");
    getChildren(props.objVal2.defaultValue);
    $axios({
    method: "post",
    url: "/area/getAreaListById?areaId="+props.objVal2.defaultValue,
  })
    .then((response) => {
      if (response.data.code == 200) {
      let array=response.data.data.reverse();
      array = array.slice(0, array.length - 1);
      console.log(array,'array') 
      array.forEach(item=>{
        defaultExpandedKeys.value.push(item.id)
      })
      // defaultExpandedKeys.value.push(props.objVal2.defaultValue)
      } else {
      }
    })
    .catch(() => {});
   
  }
  if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){ 

    getUnit();
    getUnit2();
    getProjectMatterList();  
    getbdwList();
    if(props.items.matterVo){
    //   matterArea.value=props.items.matterVo.matterArea
    // matterAreaUnit.value=props.items.matterVo.matterAreaUnit
    // tenderData.value=props.items.matterVo.tradeMatterVoList
    matterArea.value = JSON.parse(JSON.stringify(props.items.matterVo.matterArea))
      matterAreaUnit.value = JSON.parse(JSON.stringify(props.items.matterVo.matterAreaUnit))
      tenderData.value = JSON.parse(JSON.stringify(props.items.matterVo.tradeMatterVoList))
    }
    handleCurrentChange2(1)

    }
});
</script>

<style lang='scss' scoped>
.set-up-class {
  :deep(.left-box) {
    span::before {
      content: "*";
      color: red;
    }
  }
}
.set-up-class2 {
  span::before {
      content: "*";
      color: red;
    }
}
.butClick{
  pointer-events: none;
}
.butClick2{
 height:350px;
}
.set-class::before {
  content: "*";
  color: red;
}
:deep(.left-box) {
  text-align: right;
  min-width: 150px !important;
}
.zb :deep(.left-box) {
  text-align: right;
  width: 40px !important;
  min-width: 0px !important;
}
.zb :deep(.right-box) {
  width: 100% !important;
}
// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-autocomplete) {
  width: 100%;
}

:deep(.el-cascader) {
  width: 100%;
}
.nd-input-bdw :deep(.el-input__inner) {
 text-align: right;
}
.nd-input-box :deep(.el-input) {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
  width: 100%;
  // padding-left: 10px;
  padding-right: 10px;
}

:deep(.el-input.is-disabled) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
:deep(.el-input.is-disabled .el-input__wrapper){
  background: #F9F9F9;
}
</style>