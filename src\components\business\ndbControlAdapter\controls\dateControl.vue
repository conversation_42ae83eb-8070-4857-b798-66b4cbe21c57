<template>
   <!-- 日期控件 -->
  <nd-search-more-item :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate" :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
    v-if="type == 3 ||type == 4 ">
    <nd-date-picker :editable="false" :disabled="disabled" :format="format" :value-format="format"
      v-model="inputValue" :teleported="teleported" :placeholder="disabled == true ? '' : placeholder"  :type="typeTime" width="90%"></nd-date-picker>
  </nd-search-more-item>
  <!-- 日期控件 -->
</template>
  
<script setup>
// 导入公共组件
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndSearchMoreItem from '@/components/business/ndbControlAdapter/controlItem.vue';
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";

import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref, inject, watch, getCurrentInstance, nextTick, computed } from 'vue';
const $axios = inject("$axios");
const currentInstance = getCurrentInstance()
const props = defineProps({
    type: {// 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
        type: Number,
        default: "default",
    },
    width: {//宽度
        type: String,
        default: "",
    },
    title: {//标题
        type: String,
        default: "",
    },
    disabled: { // 只读
        type: Boolean,
        default: false,
    },
    requireFlag: { // 是否必填
        type: Number,
        default: 1,//1是0否
    },
    modelValue: {//双向绑定
        type: String,
    },
    jsonData: {
        type: Array,
        default: [],
    },
    dictType: {
        type: String,
        default: "YYYY/MM/DD",
    },
    paramKey: {
        type: String,
        default: "default",
    },
    showFlag: {
        type: Boolean,
        default: true,
    },
    jibie: {
        type: Boolean,
        default: true,
    },
    jibiecun: {
        type: Boolean,
        default: true,
    },
    dataType: {
        type: Number,
        default: 1,//1文本 2整数 3小数 4日期
    },
    maxLength: {//输入长度
        type: String,
        default: '100',
    },
    paramUnit: {
        type: String,
        default: '',
    },
    unitId: {
        type: String,
        default: '',
    },
    isFlag: {
        type: Boolean,
        default: false,
    },
    teleported: {
        type: Boolean,
        default: false,
    },
    LZFS: {
        type: String,
        default: '',
    }, objVal: {
        type: Object,
        default: {},
    }, projectType: {
        type: Number,
        default: '',
    }, childObj: {
        type: Object,
        default: {},
    },
    placeholder: {
    type: String,
    default: "请选择日期",
  },
  saveOperate: {//保存触发
      type: Boolean,
      default: false,
    },
})
const emits = defineEmits(["update:modelValue", "changeTree", "changeYXQXQ", "changeYXQGZ", "changeFWFSQGZ", "changeLYSQGZ", "changeZJDJ", "changeShowZj2", "changeShowZj", "mapClick", "changeNcp", 'changePriceUpper', "regPhone", "changeCertNo", "changeOrg"])//定义方法名

let typeTime = ref('date')
let format = ref('YYYY-MM-DD')
// 获取当前时间
const currentDate = new Date();
const year = currentDate.getFullYear();
const month = currentDate.getMonth() + 1; // 月份从0开始，所以要加1
const day = currentDate.getDate();
const hour = currentDate.getHours();
const minute = currentDate.getMinutes();
const second = currentDate.getSeconds();
function fillZero(str){
    var realNum;
    if (str < 10) {
        realNum = '0' + str;
    } else {
        realNum = str;
    }
    return realNum;
}
const inputValue = computed({//监听
    get() {
        // if(props.modelValue=='sysTime'){ 
        //     let nowTime = year + '-' + fillZero(month) + '-' + fillZero(day) + ' ' + fillZero(hour) + ':' + fillZero(minute) + ':' + fillZero(second)
        //     return nowTime
        // }else{
        //     return props.modelValue

        // }
        return props.modelValue
    },
    set(value) {
        // if(props.modelValue=='sysTime'){ 
        //     let nowTime = year + '-' + fillZero(month) + '-' + fillZero(day) + ' ' + fillZero(hour) + ':' + fillZero(minute) + ':' + fillZero(second)
        //     return nowTime
        // }else{
        //     emits("update:modelValue", value)

        // }
        emits("update:modelValue", value)
    }
})

watch(
  () => props.dictType,
  (newValue, oldValue) => {
    console.log(props.dictType,'props.dictType');
    if(props.dictType=='YYYY/MM/DD'||props.dictType=='YYYY-MM-DD'||props.dictType=='YYYY年MM月DD日'){
        typeTime.value = 'date'
        format.value = props.dictType
    }
    if(props.dictType=='YYYY-MM-DD hh:mm:ss'||props.dictType=='YYYY/MM/DD hh:mm:ss'||props.dictType=='YYYY/MM/DD HH:mm:ss'||props.dictType=='YYYY-MM-DD HH:mm:ss'){
        typeTime.value = 'datetime'
        format.value = props.dictType
    }
    if(props.dictType==''||props.dictType==null){
        typeTime.value = 'date'
    }
  },
  { deep: true, immediate: true }
);
// watch(
//   () => props.modelValue,
//   (newValue, oldValue) => {
    
//   },
//   { deep: true, immediate: true }
// );



let checkList = ref([])







onMounted(() => {
    // getList();
    if(props.modelValue=='sysTime'){ 
        console.log(props.modelValue,'props.modelValue');
        // props.modelValue=new Date();
        console.log(props.modelValue,'props.modelValue');
        console.log(new Date(),'props.modelValue');
    }
    if (props.type == '9' || props.type == 9) {
        if (props.modelValue == null || props.modelValue == undefined) {

        } else {
            let str = props.modelValue;
            checkList.value = str.split(',')
        }

    }
})
</script>
  
<style lang='scss' scoped>
.set-up-class {
    :deep(.left-box) {
        span::before {
            content: "*";
            color: red;
        }
    }
}

:deep(.left-box) {
    text-align: right;
    min-width: 150px !important;
}

// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
    width: 100%;
}

:deep(.el-select) {
    width: 100%;
}

:deep(.el-autocomplete) {
    width: 100%;
}

:deep(.el-cascader) {
    width: 100%;
}

.nd-input-box :deep(.el-input) {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
    width: 100%;
    // padding-left: 10px;
    padding-right: 10px;
}

:deep(.el-input.is-disabled) {
    width: 100%;
}

:deep(.el-date-editor.el-input) {
    width: 100%;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: #fafafa;
  }
</style>
  