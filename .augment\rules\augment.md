---
type: "always_apply"
---

# 新产权管理端项目代码规范

## 项目概述

### 技术栈
- **前端框架**: Vue 3 (使用 Composition API)
- **构建工具**: Vite 5.0.2
- **UI 组件库**: Element Plus 2.4.3 (PC端) + Vant 4.6.0 (移动端)
- **路由管理**: Vue Router 4.1.3
- **HTTP 请求**: Axios 0.18.0
- **样式预处理**: SCSS
- **图表库**: ECharts 5.4.2 + Highcharts 11.1.0
- **富文本编辑**: @vueup/vue-quill 1.2.0
- **文档编辑**: @onlyoffice/document-editor-vue 1.3.0

### 浏览器兼容性
- 支持 Chrome 90+、Edge 90+、Firefox 88+、Safari 14+
- 不兼容 IE 浏览器
- 支持微信小程序内置浏览器

## 项目结构规范

### 目录组织
```
src/
├── assets/           # 静态资源
│   ├── css/         # 样式文件
│   ├── fonts/       # 字体文件
│   ├── images/      # 图片资源
│   ├── metronome/   # 节拍器相关
│   └── task/        # 任务相关
├── components/       # 公共组件
│   ├── business/    # 业务组件 (ndb前缀)
│   ├── js/          # JS工具类
│   ├── utils/       # 工具函数
│   └── nd*.vue      # 通用组件 (nd前缀)
├── directive/        # 自定义指令
├── http/            # 网络请求相关
├── router/          # 路由配置
├── views/           # 页面视图 (主要)
├── views2/          # 扩展页面视图
├── viewsWx/         # 微信端页面视图
├── App.vue          # 根组件
├── main.js          # 入口文件
└── style.css        # 全局样式
```

### 文件命名规范
- **Vue组件**: PascalCase，如 `UserDetail.vue`
- **页面组件**: 功能名 + `View`，如 `welcomeView.vue`
- **通用组件**: `nd` + 组件名，如 `ndButton.vue`
- **业务组件**: `ndb` + 组件名，如 `ndbButtonSearch.vue`
- **工具类文件**: camelCase，如 `formatUtils.js`
- **样式文件**: kebab-case，如 `main-layout.scss`
- **图片资源**: kebab-case，如 `header-logo.png`

## Vue 组件开发规范

### 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入 Vue API
import { ref, watch, onMounted, reactive, computed, inject } from 'vue'

// 2. 导入组件
import ndButton from '@/components/ndButton.vue'

// 3. 导入工具函数
import { ElMessage } from 'element-plus'

// 4. 注入依赖
const $axios = inject('$axios')

// 5. 定义 props
const props = defineProps({
  propName: {
    type: String,
    default: '',
  },
})

// 6. 定义 emits
const emits = defineEmits(['eventName', 'refreshData'])

// 7. 响应式数据
const componentState = ref(null)
const form = reactive({
  // 表单数据
})

// 8. 计算属性
const computedValue = computed(() => {
  // 计算逻辑
})

// 9. 监听器
watch(() => props.propName, (newVal) => {
  // 监听逻辑
})

// 10. 生命周期钩子
onMounted(() => {
  // 初始化逻辑
})

// 11. 方法定义
function handleEvent() {
  emits('eventName', componentState.value)
}

// 12. 暴露方法 (如需要)
defineExpose({
  handleEvent
})
</script>

<style lang="scss" scoped>
/* 组件样式 */
.component-class {
  display: flex;
}
</style>
```

### 组件设计原则
- **单一职责**: 每个组件只负责单一功能
- **可复用性**: 业务组件设计时考虑复用场景
- **可测试性**: 避免过于复杂的组件逻辑
- **最小入侵**: 新功能尽量通过封装新组件实现，避免修改旧代码

### 组件使用规则
- 优先使用项目内的 `nd-*` 组件，不直接使用 Element Plus 原生组件
- `nd-*` 组件是 Element Plus 的二次封装，用法基本一致
- `ndb-*` 组件是业务组件，封装特定业务逻辑
- 引入新组件库需评估与现有组件的兼容性

## 编码风格约定

### 命名规范
- **变量命名**: camelCase，如 `userName`
- **常量命名**: UPPER_SNAKE_CASE，如 `API_BASE_URL`
- **函数命名**: camelCase，如 `getUserInfo`
- **CSS类名**: kebab-case，如 `.user-info`
- **组件名**: PascalCase，如 `UserDetail`

### 注释规范
```javascript
// 组件顶部注释
<!-- 出金处理弹窗 -->

// 变量注释 (行尾注释)
const form = reactive({
  realNameType: ["1", '2'], //认证方式
  transactionalMode: ["2"], //交易模式配置
  unitePublishFlag: "2", //联合发布配置
})

// 函数注释
/**
 * 校验必填项
 * @param {Array} data
 * @param {Object} checkData
 * @returns {boolean}
 */
function check(data, checkData) {
  // 实现逻辑
}
```

### 代码质量要求
- 注释要简洁，只打印关键信息到 console
- 禁止使用标签选择器
- 禁止使用 `!important`
- 生产环境禁止使用 `console.log`
- 代码禁止出现 TODO、FIXME 等标记
- 首选函数式编程风格

## 样式编写规范

### 基本原则
- 使用 SCSS 作为 CSS 预处理器
- 组件样式必须使用 `<style lang="scss" scoped>` 实现样式隔离
- 禁止使用标签选择器
- 禁止使用 `!important`
- CSS 选择器嵌套不超过3层

### 主题色彩系统
- **主色**: `#0b8df1` (蓝色)
- **字体**: "微软雅黑"

### 布局规范
- 优先使用 Flex 布局
- 使用 `box-sizing: border-box`

### 样式示例
```scss
<style lang="scss" scoped>
.component-wrapper {
  display: flex;

  .content-box {
    flex: 1;

    .title {
      color: #0b8df1;
      font-size: 14px;
    }
  }

  // 深度选择器用于修改子组件样式
  :deep(.el-button--primary) {
    --el-button-bg-color: #0b8df1;
  }
}
</style>
```

## HTTP 请求规范

### 请求配置
- 使用 axios 进行 HTTP 请求
- 通过 `inject('$axios')` 注入使用
- 请求拦截器自动添加 token
- 响应拦截器处理错误码和跳转逻辑

### 状态码规范
- **200**: 操作成功
- **401**: 未授权，跳转登录页
- **403**: 权限不足
- **802**: license过期，跳转到系统管理页面

### 请求示例
```javascript
// 基本请求
$axios({
  url: "/api/getData",
  method: "get",
  params: { id: 1 }
}).then((res) => {
  if (res.data.code === 200) {
    // 处理成功逻辑
  } else {
    ElMessage.error(res.data.msg)
  }
})

// POST 请求
$axios({
  url: "/api/saveData",
  method: "post",
  data: formData
}).then((res) => {
  if (res.data.code === 200) {
    ElMessage.success(res.data.msg)
  } else {
    ElMessage.error(res.data.msg)
  }
})
```

### 错误处理
- 统一使用 `ElMessage` 显示错误信息
- 根据不同城市类型(wx/hg)进行不同的跳转处理
- 兼容三资系统的嵌入场景

## 自定义指令

### 内置指令
```javascript
// 千分位格式化
<div v-thousands>1234567.89</div>
// 显示: 1,234,567.89

// 空值处理
<div v-empty>{{ emptyValue }}</div>
// 空值时显示: --
```

### 指令实现
```javascript
export default {
  // 千分位展示
  thousands: {
    beforeMount(el, binding, vnode) {
      if (el.innerHTML === "" || el.innerHTML === null || el.innerHTML === undefined) {
        el.innerHTML = "";
      } else {
        var newValue = new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(parseFloat(el.innerHTML));
        if (!isNaN(newValue)) {
          el.innerHTML = newValue;
        }
      }
    }
  },

  // 为空补 '--'
  empty: {
    beforeMount(el, binding, vnode) {
      if (el.innerHTML === "" || el.innerHTML === null || el.innerHTML === undefined) {
        el.innerHTML = "--";
      }
    }
  }
}
```

## 权限控制

### 权限实现方式
- 权限信息存储在 `localStorage.getItem("cqcpAuths")`
- 组件级权限通过 `authKey` 属性控制
- 按钮权限在 `onMounted` 中检查

### 权限使用示例
```vue
<template>
  <nd-button authKey="member:memberDelFlag:edit" @click="deleteUser">
    删除
  </nd-button>
</template>

<script setup>
onMounted(() => {
  if (localStorage.getItem("cqcpAuths")) {
    var auth = JSON.parse(localStorage.getItem("cqcpAuths"));
    if (props.authKey) {
      let isForbid = auth.some((item) => {
        return props.authKey === item.menuCode;
      });
      disabled.value = !isForbid;
    }
  }
});
</script>
```

## 路由管理

### 路由配置
```javascript
const routes = [
  {
    path: '/welcomeView',
    name: 'welcomeView',
    component: () => import('@/views/welcomeView/index.vue'),
  }
]
```

### 导航守卫
```javascript
router.beforeEach((to, from) => {
  // 取消之前的请求
  if (to.path !== "/reloadView") {
    window.axiosPromiseArr.forEach((ele, index) => {
      ele.cancel();
    })
    window.axiosPromiseArr = [];
    window.axiosPromiseArrName = [];
  }

  // 登录检查逻辑 (根据需要开启)
  // if (!localStorage.getItem("cqcpyhToken")) {
  //   return { path: 'loginView' }
  // }
})
```

## 状态管理

### 数据存储方式
- **组件内部状态**: 使用 `ref`、`reactive` 管理
- **跨页面状态**: 使用 `localStorage` 存储
- **常用状态**: token、权限、用户信息等

### 状态示例
```javascript
// 组件内部状态
const form = reactive({
  userName: '',
  userAge: 0
})

// 跨页面状态
localStorage.setItem('cqcpyhToken', token)
localStorage.setItem('cqcpAuths', JSON.stringify(authList))
localStorage.setItem('cityType', 'wx') // wx/hg
```

## 业务约束

### 现有代码处理原则
1. 现有系统代码是稳定运行的生产环境代码
2. 对原功能有大的改动时必须先询问确认
3. 尽量不入侵旧代码，通过封装新组件/hooks/方法实现功能
4. 实现功能时保持与系统其他模块一致的风格
5. 修改代码要追根溯源，检查相关引用文件

### 系统特性
- 根据不同城市类型(wx/hg)进行不同的业务逻辑处理
- 处理token逻辑时需兼容三资系统的嵌入场景
- 支持多环境配置 (测试、预发布、生产)

## 性能优化

### 首屏加载
- 首屏加载时间不超过3秒
- 路由组件使用懒加载
- 使用 preload/prefetch 优化关键资源

### 运行性能
- 避免不必要的组件重渲染
- 列表使用虚拟滚动
- 合理使用防抖和节流
- 避免内存泄漏

### 构建优化
```javascript
// vite.config.js
export default defineConfig({
  build: {
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true // 生产环境移除console
      }
    }
  }
})
```

## 开发工具配置

### 项目配置文件
- `package.json`: 依赖管理
- `vite.config.js`: 构建配置
- `jsconfig.json`: JS配置
- `public/config.js`: 运行时配置

### 开发命令
```bash
npm run dev      # 开发环境
npm run build    # 构建生产版本
npm run preview  # 预览构建结果
```

## 总结

本项目是一个基于 Vue 3 + Vite 的现代化前端项目，采用了完整的企业级开发规范：

1. **技术栈现代化**: Vue 3 Composition API + Vite + Element Plus
2. **组件化开发**: nd/ndb 前缀的组件体系，二次封装 Element Plus
3. **样式规范化**: SCSS + scoped + 主题色系统
4. **权限体系完善**: 基于 menuCode 的细粒度权限控制
5. **错误处理统一**: axios 拦截器 + 状态码规范
6. **代码质量保证**: ESLint + 注释规范 + 命名约定

遵循这些规范可以确保代码的可维护性、可扩展性和团队协作效率。