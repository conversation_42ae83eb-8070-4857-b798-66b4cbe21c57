<template>
  <div class="nd-search-more-item-box">
    <div class="left-box"  :class="letterSpacing ? 'letter-spacing' : ''">
    <!-- <div class="left-box" :style="{ width: width }" :class="letterSpacing ? 'letter-spacing' : ''"> -->
      <el-icon v-if="assembly == true" :size="16">
        <Box />
      </el-icon>
      <span :class="requires ? 'require' : ''">{{ title }}</span>
    </div>
    <div class="right-box" :style="{ width: 'calc(100% - ' + width + ')' }">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "未命名",
    },
    width: {
      type: String,
      default: "20px",
    },
    requires: {
      type: Boolean,
      default: false,
    },
    assembly: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      letterSpacing: false,
    };
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.nd-search-more-item-box {
  width: 33.33%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  margin-bottom: 10px;

  .left-box {
    // width: 58px;
    height: 100%;
    min-width: 120px;
    display: flex;
    flex-direction: row;
    // justify-content: flex-end;
    align-items: center;
    padding-right: 10px;
    font-size: 14px;
    color: #555555;
  }

  .letter-spacing {
    letter-spacing: -1.03px;
  }

  .right-box {
    // width: calc(100% - 58px);
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    margin-top: 5px;

    :deep(.nd-input-box) {
      width: 100% !important;
    }

    :deep(.nd-select-box) {
      width: 100% !important;
    }

    :deep(.nd-date-picker-box) {
      width: 100% !important;
    }

    :deep(.nd-tree-select-box) {
      width: 100% !important;
    }
  }

  // 是否必填
  .require::before {
    content: "* ";
    color: red;
  }
}
</style>