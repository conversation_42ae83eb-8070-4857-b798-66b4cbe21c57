<template>
  <!-- input文本类型 -->
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title" :paramKey="paramKey"  :title2="title2"  :titleBtn="titleBtn" 
    :requires="requireFlag"
    :dictType="dictType"
    :itemValue="inputValue" :saveOperate="saveOperate"
    :style="{ width: width }" 
    v-if="
      type == 1 &&
      showFlag != false &&
      paramKey != 'serviceFeeRule' &&
      paramKey != 'perFeeRule' &&
      paramKey != 'rentIncRule'
    "
  >
  <!-- <nd-input
      class="input-with-select"
      :formatter="priceFormat3"
      :parser="priceFormat3"
      :maxLength="maxLength"
      :disabled="disabled || dictType == 'YWLX' || dictType == 'JYPZ'"
      v-model="inputValue"
      :placeholder="
        paramKey == 'addressLocation' || paramKey == 'storeLocation'
          ? '地图标记'
          : '请输入'
      "
      width="90%"
      clearable
    >
      <template #prepend>
        <span style="font-size: 14px; color: #444">自定义</span>
      </template>
    </nd-input> -->

    <el-autocomplete
      :maxLength="maxLength"
      :fetch-suggestions="querySearchAsync"
      @select="handleSelect"
      @blur="blur"
      v-if="type == 1 && showFlag != false && (paramKey == 'retenant'||paramKey == 'traderPerson')"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    >
    <!-- <el-autocomplete
      :maxLength="maxLength"
      :fetch-suggestions="querySearchAsync"
      @select="handleSelect"
      @blur="blur"
      v-if="type == 1 && showFlag != false && paramKey == 'retenant'"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    > -->
      <template #default="{ item }">
        <div class="value">{{ item.value }}{{ item.link }}</div>
      </template>
    </el-autocomplete>
    <nd-input
      v-if="dataType == 2 && dictType != 'NEINTEGER'"
      :maxLength="maxLength"
      @click="mapClick"
      :disabled="disabled || paramKey == 'code'"
      v-model="inputValue"
      :formatter="priceFormat2"
      :parser="priceFormat2"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    ></nd-input>
    <nd-input
      v-if="dataType == 2 && dictType == 'NEINTEGER'"
      :maxLength="maxLength"
      :disabled="disabled || paramKey == 'code'"
      v-model="inputValue"
      :formatter="priceFormat4"
      :parser="priceFormat4"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    ></nd-input>
    <nd-input
      v-if="
        dataType == 3 &&
        dictType != '1-100' &&
        paramKey != 'tradeCount' &&
        dictType != 'money'&&
        dictType != 'wmoney'
      "
      :formatter="priceFormat"
      :parser="priceFormat"
      @click="mapClick"
      :disabled="disabled || paramKey == 'code' || showFlag == 'noEdit'"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
      @change="changeInput2"
    ></nd-input>
    <!-- :maxLength="maxLength == '12' ? '13' : maxLength" -->
    <nd-input
      v-if="
        dataType == 3 &&
        dictType != '1-100' &&
        paramKey == 'tradeCount' &&
        dictType != 'money'&&
        dictType != 'wmoney'
      "
      :maxLength="maxLength == '12' ? '13' : maxLength"
      :formatter="priceFormatTwo"
      :parser="priceFormatTwo"
      @click="mapClick"
      :disabled="disabled || paramKey == 'code'"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
      @change="changeInput2"
    ></nd-input>
    <nd-input
      v-if="
        dataType == 1 &&
        paramKey != 'retenant' &&
        dictType != 'dept' &&
        dictType != '1-100' &&
        paramKey != 'mortgageeName'&&
        paramKey != 'certCode' &&paramKey != 'traderPerson'
      "
      :maxLength="maxLength"
      @click="mapClick"
      :disabled="
        disabled ||
        (paramKey == 'code' && title != '合同编号') || paramKey == 'orgCode' || paramKey == 'userPhone' ||
        showFlag == 'noEdit' ||
        paramKey == 'mortgageCode' ||
        (paramKey == 'projectCode' && isFlag2 == true) ||(paramKey=='name'&&projectButtonTitle=='自定义项目名称'&&title!='合同名称'&&showDisabled)
      "
      v-model.trim="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
      @change="changeInput" @input="changeInp"
      :formatter="zjFormat2"
      :parser="zjFormat2"
    >
    <!-- <template #append v-if="paramKey=='name' && titleBtn">
        <span style="font-size: 14px; color: #0B8DF1;cursor:pointer;" @click="toggle">{{projectButtonTitle}}</span>
      </template> -->
  </nd-input>
    <!-- <nd-input
      v-if="
        dataType == 1 &&
        paramKey != 'retenant' &&
        dictType != 'dept' &&
        dictType != '1-100' &&
        paramKey != 'mortgageeName'
      "
      :maxLength="maxLength"
      @click="mapClick"
      :disabled="
        disabled ||
        paramKey == 'code' ||
        showFlag == 'noEdit' ||
        paramKey == 'mortgageCode' ||
        (paramKey == 'projectCode' && isFlag2 == true)
      "
      v-model.trim="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
      @change="changeInput"
    ></nd-input> -->
    <nd-input
      v-if="dictType == '1-100'"
      :maxLength="maxLength"
      :formatter="priceFormat3"
      :parser="priceFormat3"
      :disabled="disabled || paramKey == 'code' || paramKey == 'mortgageCode'"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    ></nd-input>
    <el-select v-if="paramKey == 'certCode' && title == '鉴证书编号'"
        v-model="inputValue"
        filterable
        remote
        reserve-keyword
        placeholder="请输入"
        remote-show-suffix
        :remote-method="remoteMethod"
        :loading="loading"
        :teleported="false"
        @change="selectChange"
        value-key="certCode"
        v-loadMore="loadList"
        :popper-append-to-body="false"
      >
      <el-option v-for="item in selectList" :key="item.id" :label="item.certCode" :value="item.certCode"/>
      </el-select>
    <!-- <ndButton
      style="width: 80px; margin-left: 12px"
      v-if="paramKey == 'certCode' && title == '鉴证书编号'"
      @click="toSearch"
      type="primary"
      color="#0b8df1"
      >查询</ndButton
    > -->
    <el-autocomplete
      v-if="dictType == 'dept'"
      :disabled="disabled"
      :maxLength="maxLength"
      width="90%"
      value-key="deptName"
      :fetch-suggestions="querySearchAsync2"
      @select="handleSelect2"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      clearable
    >
      <template #suffix>
        <span
          style="font-size: 12px; cursor: pointer; color: #0b8df1"
          @click="handleIconClick"
          >请选择组织</span
        >
      </template>
    </el-autocomplete>

    <ndb-input-thousands-separator
      :formatter="priceFormatTwo"
      :parser="priceFormatTwo" :precision="decimalLength"
      :disabled="disabled || paramKey == 'code' || showFlag == 'noEdit'"
      v-if="
        dataType == 3 &&
        dictType != '1-100' &&
        dictType != 'dept' &&
        (dictType == 'money'||dictType == 'wmoney')
      "
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
    >
    <template #append v-if="dictType == 'wmoney'"
        ><span style="font-size: 12px;color:#666">{{
          toChies2(inputValue)
        }}</span></template
      >
      <template #append v-else
        ><span style="font-size: 12px;color:#666">{{
          toChies(inputValue)
        }}</span></template
      >
    </ndb-input-thousands-separator>

    <!-- <el-autocomplete v-if="paramKey == 'mortgageeName'" :disabled="disabled" :maxLength="maxLength" width="90%"
            value-key="mortgageeName" :fetch-suggestions="querySearchAsync3" @select="handleSelect3" v-model="inputValue"
            :placeholder="disabled == true ? '' : placeholder" clearable>
            <template #suffix>
                <span style="font-size:12px;cursor:pointer;color:#0B8DF1;" @click="handleIconClick2">请选择</span>
            </template>
        </el-autocomplete> -->
  </nd-search-more-item>
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"   :title2="title2" 
    :style="{ width: width }" :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    v-if="type == 20"
  >
    <el-autocomplete
      v-if="paramKey == 'mortgageeName'"
      :disabled="disabled"
      :maxLength="maxLength"
      width="90%"
      value-key="mortgageeName"
      :fetch-suggestions="querySearchAsync3"
      @select="handleSelect3"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      clearable
    >
      <template #suffix>
        <span
          style="font-size: 13px; cursor: pointer; color: #0b8df1"
          @click="handleIconClick3"
          >请选择</span
        >
      </template>
    </el-autocomplete>
  </nd-search-more-item>
  <nd-search-more-item :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"   :title2="title2" 
    :style="{ width: width }"
    v-if="
      type == 1 &&
      (paramKey == 'serviceFeeRule' || paramKey == 'perFeeRule') &&
      isFlag == false
    "
  >
    <nd-input
      :maxLength="maxLength"
      @click="mapClick"
      :disabled="disabled || paramKey == 'code'"
      v-model="inputValue"
      :formatter="priceFormat2"
      :parser="priceFormat2"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    ></nd-input>
  </nd-search-more-item>

  <!-- <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 5 && paramKey == 'serviceFeeRule' && (showFlag != false)">

        <div v-if="LZFS == 1"
            style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;">按</span>
            <nd-select @change="changeSelect" :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" filterable
                placeholder="请选择" v-model="inputValue" clearable>
                <el-option v-for="item in jsonData" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
            </nd-select>
            <span style="margin-left:2%;">年成交价收取</span>
        </div>
        <div v-else style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;">按成交价收取</span>
        </div>
    </nd-search-more-item>

    <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 1 && paramKey == 'serviceFeeRule' && (showFlag != false) && isFlag == true">
        <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">服务费固定金额</span>
            <nd-input :maxLength="maxLength" :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')"
                v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="60%"
                clearable></nd-input>
            <span style="margin-left:2%;">元</span>
        </div>
    </nd-search-more-item> -->
  <nd-search-more-item :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"   :title2="title2" 
    :style="{ width: width }"
    v-if="type == 16 && paramKey == 'serviceFeeRule' && showFlag != false"
  >
    <!-- <div v-if="LZFS == 1"
            style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;">按</span>
            <nd-select @change="changeSelect" :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" filterable
                placeholder="请选择" v-model="inputValue" clearable>
                <el-option v-for="item in jsonData" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
            </nd-select>
            <span style="margin-left:2%;">年成交价收取</span>
        </div> -->
    <div
      v-if="LZFS == 1"
      class="shadows"
      style="
        display: flex;
        flex-direction: row;
        font-size: 14px;
        color: #555555;
        align-items: center;
        width: 100%;
      "
    >
      <el-select
        @change="changeSelect"
        :disabled="disabled || dictType == 'YWLX' || dictType == 'JYPZ'"
        filterable
        style="width: 90%"
        placeholder="请选择"
        v-model="inputValue"
        clearable
      >
        <el-option
          v-for="item in jsonData"
          :key="item.dataKey"
          :label="item.dataValue"
          :value="item.dataKey"
        />
        <template #prefix>
          <span style="font-size: 14px; color: #444">按</span>
        </template>
      </el-select>
      <span
        style="
          border: 1px solid #dcdfe6;
          width: 25%;
          display: inline-block;
          height: 36px;
          line-height: 36px;
          border-left: none;
          text-align: center;
          border-top-right-radius: 5px;
          border-bottom-right-radius: 5px;
          position: relative;
          left: -2px;
          font-size: 14px;
          color: #444;
        "
        >年成交价收取</span
      >
    </div>
    <div
      v-else
      style="
        display: flex;
        flex-direction: row;
        font-size: 14px;
        color: #555555;
        align-items: center;
        width: 100%;
      "
    >
      <span style="margin-right: 2%">按成交价收取</span>
    </div>
  </nd-search-more-item>

  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''" :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    :title="title"   :title2="title2" 
    :style="{ width: width }"
    v-if="
      type == 17 &&
      paramKey == 'serviceFeeRule' &&
      showFlag != false &&
      isFlag == true
    "
  >
    <nd-input
      class="input-with-select"
      :maxLength="maxLength"
      :disabled="disabled || dictType == 'YWLX' || dictType == 'JYPZ'"
      v-model="inputValue"
      :placeholder="
        paramKey == 'addressLocation' || paramKey == 'storeLocation'
          ? '地图标记'
          : '请输入'
      "
      width="60%"
      clearable
    >
      <template #prepend>
        <span style="font-size: 14px; color: #444">服务费固定金额(元)</span>
      </template>
    </nd-input>
    <!-- <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">服务费固定金额</span>
            <nd-input :maxLength="maxLength" :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')"
                v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="60%"
                clearable></nd-input>
            <span style="margin-left:2%;">元</span>
        </div> -->
  </nd-search-more-item>

  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"   :title2="title2" 
    :style="{ width: width }" :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    v-if="
      type == 16 && paramKey == 'perFeeRule' && isFlag == true && LZFS == '1'
    "
  >
    <nd-input
      class="input-with-select"
      :formatter="priceFormat3"
      :parser="priceFormat3"
      :maxLength="maxLength"
      :disabled="disabled || dictType == 'YWLX' || dictType == 'JYPZ'"
      v-model="inputValue"
      :placeholder="
        paramKey == 'addressLocation' || paramKey == 'storeLocation'
          ? '地图标记'
          : '请输入'
      "
      width="90%"
      clearable
    >
      <template #prepend>
        <span style="font-size: 14px; color: #444">收取首年租金的(%)</span>
      </template>
    </nd-input>
    <!-- <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">收取首年租金的</span>
            <nd-input :formatter="priceFormat3" :parser="priceFormat3" :maxLength="maxLength"
                :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="90%"
                clearable></nd-input>
            <span style="margin-left:2%;">%</span>
        </div> -->
  </nd-search-more-item>

  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"   :title2="title2" 
    :style="{ width: width }" :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    v-if="
      type == 16 && paramKey == 'perFeeRule' && isFlag == true && LZFS != '1'
    "
  >
    <nd-input
      class="input-with-select"
      :formatter="priceFormat3"
      :parser="priceFormat3"
      :maxLength="maxLength"
      :disabled="disabled || dictType == 'YWLX' || dictType == 'JYPZ'"
      v-model="inputValue"
      :placeholder="
        paramKey == 'addressLocation' || paramKey == 'storeLocation'
          ? '地图标记'
          : '请输入'
      "
      width="90%"
      clearable
    >
      <template #prepend>
        <span style="font-size: 14px; color: #444">收取成交价的(%)</span>
      </template>
    </nd-input>
    <!-- <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">收取成交价的</span>
            <nd-input :formatter="priceFormat3" :parser="priceFormat3" :maxLength="maxLength"
                :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="90%"
                clearable></nd-input>
            <span style="margin-left:2%;">%</span>
        </div> -->
  </nd-search-more-item>
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"   :title2="title2" 
    :style="{ width: width }" :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    v-if="type == 17 && paramKey == 'perFeeRule' && isFlag == true"
  >
    <nd-input
      class="input-with-select"
      :maxLength="maxLength"
      :formatter="priceFormat"
      :parser="priceFormat"
      :disabled="disabled || dictType == 'YWLX' || dictType == 'JYPZ'"
      v-model="inputValue"
      :placeholder="
        paramKey == 'addressLocation' || paramKey == 'storeLocation'
          ? '地图标记'
          : '请输入'
      "
      width="90%"
      clearable
    >
      <template #prepend>
        <span style="font-size: 14px; color: #444">履约金固定金额(元)</span>
      </template>
    </nd-input>
    <!-- <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">履约金固定金额</span>
            <nd-input :maxLength="maxLength" :formatter="priceFormat" :parser="priceFormat"
                :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="90%"
                clearable></nd-input>
            <span style="margin-left:2%;">元</span>
        </div> -->
  </nd-search-more-item>

  <!-- <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 5 && paramKey == 'perFeeRule' && (showFlag != false) && isFlag == true && LZFS == '1'">
        <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">收取首年租金的</span>
            <nd-input :formatter="priceFormat3" :parser="priceFormat3" :maxLength="maxLength"
                :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="90%"
                clearable></nd-input>
            <span style="margin-left:2%;">%</span>
        </div>
    </nd-search-more-item>

    <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 5 && paramKey == 'perFeeRule' && (showFlag != false) && isFlag == true && LZFS != '1'">
        <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">收取成交价的</span>
            <nd-input :formatter="priceFormat3" :parser="priceFormat3" :maxLength="maxLength"
                :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="90%"
                clearable></nd-input>
            <span style="margin-left:2%;">%</span>
        </div>
    </nd-search-more-item>
    <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 1 && paramKey == 'perFeeRule' && (showFlag != false) && isFlag == true">
        <div style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
            <span style="margin-right:2%;white-space: nowrap;">履约金固定金额</span>
            <nd-input :maxLength="maxLength" :formatter="priceFormat" :parser="priceFormat"
                :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ')" v-model="inputValue"
                :placeholder="paramKey == 'addressLocation' || paramKey == 'storeLocation' ? '地图标记' : '请输入'" width="90%"
                clearable></nd-input>
            <span style="margin-left:2%;">元</span>
        </div>
    </nd-search-more-item> -->
  <!-- 租金递增规则 -->
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"  :title2="title2" 
    :style="{ width: width }"
    v-if="type == 18 && paramKey == 'rentIncRule' && showFlag != false"
  >
  <!-- <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"  :title2="title2" 
    :style="{ width: width }"
    v-if="type == 18 && paramKey == 'rentIncRule' && showFlag != false"
  > -->
    <div
      style="display: flex; flex-direction: column"
      :style="{ width: width }"
    >
      <div
        v-for="(item, index) in jsonData"
        :key="index"
        style="margin-top: 10px"
      >
      <!-- :maxLength="maxLength" -->
        <nd-input 
          class="input-with-select"
          :formatter="priceFormat33"
          :parser="priceFormat33"
          :disabled="disabled"
          v-model="item.values"
          placeholder="请输入"
          width="90%"
          clearable
        >
          <template #prepend>
            <span style="font-size: 14px; color: #444"
              >第{{ item.year }}年递增上年金额的(%)</span
            >
          </template>
        </nd-input>
      </div>
    </div>

    <!-- <div style="display: flex;flex-direction: column;">
            <div v-for="(item, index) in jsonData" :key="index"
                style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
                <span style="margin-right:2%;">第{{ item.year }}年递增</span>
                <nd-input :maxLength="maxLength" :formatter="priceFormat3" :parser="priceFormat3" :disabled="disabled"
                    v-model="item.values" placeholder="请输入" width="90%" clearable></nd-input>
                <span style="margin-left:2%;">%</span>
            </div>
        </div> -->
  </nd-search-more-item>
  <!-- <nd-search-more-item :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 1 && paramKey == 'rentIncRule' && (showFlag != false)">
        <div style="display: flex;flex-direction: column;">
            <div v-for="(item, index) in jsonData" :key="index"
                style="display:flex;flex-direction:row;font-size: 14px;color:#555555;align-items:center;width:100%;">
                <span style="margin-right:2%;">第{{ item.year }}年递增</span>
                <nd-input :maxLength="maxLength" :formatter="priceFormat3" :parser="priceFormat3" :disabled="disabled"
                    v-model="item.values" placeholder="请输入" width="90%" clearable></nd-input>
                <span style="margin-left:2%;">%</span>
            </div>
        </div>
    </nd-search-more-item> -->
  <!-- 文本域类型 -->
  <nd-search-more-item :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate"
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"  :title2="title2" 
    :style="{ width: width }"
    v-if="type == 2"
  >
    <nd-input
      :maxLength="maxLength"
      :disabled="disabled"
      type="textarea"
      v-model="inputValue"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    ></nd-input>
  </nd-search-more-item>
  <!-- 文本域类型 -->

  <!-- 数字框 -->
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title" :requires="requireFlag"  :title2="title2" 
    :itemValue="inputValue" :saveOperate="saveOperate"
    :style="{ width: width }"
    v-if="type ==22"
  >
    <!-- <nd-input
      :maxLength="maxLength"
      :disabled="disabled || paramKey == 'code'"
      v-model="inputValue"
      :formatter="priceFormatRange"
      :parser="priceFormatRange"
      :placeholder="disabled == true ? '' : placeholder"
      width="90%"
      clearable
    ></nd-input> -->
    <nd-input
      :maxLength="maxLength"
      :disabled="disabled || paramKey == 'code'" min="10" max="50"
      v-model="inputValue" :formatter="priceFormatRange"
      :placeholder="disabled == true ? '' : placeholder"  @blur="handleInput"
      width="90%"
      clearable
    ></nd-input>
  </nd-search-more-item>
</template>
  
<script setup>
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndSearchMoreItem from "@/components/business/ndbControlAdapter/controlItem.vue";
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";
import ndButton from "@/components/ndButton.vue";

import { ElMessage, ElMessageBox } from "element-plus";

import {
  onMounted,
  reactive,
  ref,
  inject,
  provide,
  watch,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
const $axios = inject("$axios");
const currentInstance = getCurrentInstance();
const props = defineProps({
  type: {
    // 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
    type: Number,
    default: "default",
  },
  width: {
    //宽度
    type: String,
    default: "",
  },
  title: {
    //标题
    type: String,
    default: "",
  },
  title2: {
    //标题
    type: String,
    default: "",
  },
  disabled: {
    // 只读
    type: Boolean,
    default: false,
  },
  requireFlag: {
    // 是否必填
    type: Number,
    default: 1, //1是0否
  },
  modelValue: {
    //双向绑定
    type: String,
  },
  modelValue2: {
    //双向绑定
    type: String,
  },
  jsonData: {
    type: Array,
    default: [],
  },
  dictType: {
    type: String,
    default: "default",
  },
  paramKey: {
    type: String,
    default: "default",
  },
  showFlag: {
    type: Boolean,
    default: true,
  },
  jibie: {
    type: Boolean,
    default: true,
  },
  jibiecun: {
    type: Boolean,
    default: true,
  },
  dataType: {
    type: Number,
    default: 1, //1文本 2整数 3小数 4日期
  },
  maxLength: {
    //输入长度
    type: String,
    default: "100",
  },
  paramUnit: {
    type: String,
    default: "",
  },
  unitId: {
    type: String,
    default: "",
  },
  isFlag: {
    type: Boolean,
    default: false,
  },
  titleBtn: {
    type: Boolean,
    default: false,
  },
  isFlag2: {
    type: Boolean,
    default: false,
  },
  LZFS: {
    type: String,
    default: "",
  },
  objVal: {
    type: Object,
    default: {},
  },
  projectType: {
    type: Number,
    default: "",
  },
  childObj: {
    type: Object,
    default: {},
  },
  placeholder: {
    type: String,
    default: "请输入",
  },
  valueRange: {
    type: String,
    default: "",
  },
  decimalLength: {
    type: String,
    default: 2,
  },
  showDisabled: {
    type: Boolean,
    default: false,
  },
  saveOperate: {//保存触发
      type: Boolean,
      default: false,
    },
});
const emits = defineEmits([
  "update:modelValue",
  "update:modelValue2",
  "changeTree",
  "changeYXQXQ",
  "changeYXQGZ",
  "changeFWFSQGZ",
  "changeLYSQGZ",
  "changeZJDJ",
  "changeShowZj2",
  "changeShowZj",
  "mapClick",
  "changeNcp",
  "changePriceUpper",
  "regPhone",
  "changeCertNo",
  "changeOrg",
  "changeJzs",
]); //定义方法名
const handleIconClick = (ev) => {
  // triggerValue.val=true;
};
let rangeValue=ref('')
const inputValue = computed({
  //监听
  get() {
    console.log(props.objVal);
    // console.log(props.modelValue,'props.modelValue')
    // console.log(rangeValue.value,'rangeValue.value')
    // if(props.modelValue &&props.valueRange){
    // console.log(props.modelValue,'props.modelValue')
    //   return props.modelValue;
    // }
    // if(props.valueRange &&rangeValue.value){
    // console.log(rangeValue.value,'rangeValue.value')
    //   return rangeValue.value;
    // }
  //   if (
  //   props.valueRange
  // ){
  //   return rangeValue.value;
  // }else{
  //   return props.modelValue;
  // }
  return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  },
});
const inputValue2 = computed({
  //监听
  get() {
    return props.modelValue2;
  },
  set(value) {
    emits("update:modelValue2", value);
  },
});
const toSearch = (val) => {
  if (props.paramKey == "certCode") {
    $axios({
      method: "post",
      url: "/mortgage/getInfoByForensicsCode",
      data:{
        code:val
      }
    })
      .then((response) => {
        if (response.data.code == 200) {
          emits("changeJzs", response.data.data);
        } else {
          ElMessage.error(response.data.msg);
        }
      })
      .catch(() => {});
  }
};
let projectButtonTitle=ref('自定义项目名称')
const setProjectName = inject("setProjectName");

let moneyUpper = ref("");
let toggle=()=>{
  projectButtonTitle.value=projectButtonTitle.value=='自动生成项目名称'?'自定义项目名称':'自动生成项目名称'
  if(projectButtonTitle.value=='自动生成项目名称'){
    setProjectName(1)
    // emits("setProjectName");
  }else{
    setProjectName(2)
  }
}
provide("toggle", toggle);
// const regex = /^[a-zA-Z0-9()]+$/;
// const zjFormat2 = (value) => {
//   if(props.paramKey=='traderCertNo'||props.paramKey=='reletCertNo'||props.paramKey=='certNo'||props.paramKey=='accountCertNo'||props.paramKey=='legalPersonNo'||props.paramKey=='agentCertNo'){
//     if (!regex.test(value)){
//       return value.replace(/[^a-zA-Z0-9()]/g,'')
//     }else{
//       return value
//     }
//     // return value.replace(/[^\w_]/g,'')
//   }else{
//     return value
//   }
// }
const priceFormatTwo = (value, int = 6) => {
  if (props.decimalLength == 2) {

  }
  // value = value.toString();
  // // 先把非数字的都替换掉，除了数字和小数点
  // value = value.replace(/[^\d.]/g, "");
  // // 必须保证第一个为数字而不是小数点
  // value = value.replace(/^\./g, "");
  // // 保证只有出现一个小数点而没有多个小数点
  // value = value.replace(/\.{2,}/g, ".");
  // // 保证小数点只出现一次，而不能出现两次以上
  // value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  // // 保证只能输入4个小数
  // value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
  // // 只能8位整数
  // let index = value.indexOf('.')
  // if (index > -1) {
  //     value = value.slice(0, index < int ? index : int) + value.slice(index)
  // } else {
  //     value = value.slice(0, int)
  // }
  // return value
  if (props.decimalLength == 2) {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 10 ? index : 10) + value.slice(index);
    } else {
      value = value.slice(0, 10);
      // value = value.slice(0, 8);
    }
    return value;
  }else if (props.decimalLength ==4) {

    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 6 ? index : 6) + value.slice(index);
    } else {
      value = value.slice(0, 6);
    }
    return value;
  }  else {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 10 ? index : 10) + value.slice(index);
    } else {
      value = value.slice(0, 10);
    }
    return value;
  }
};
const priceFormat = (value, int = 10) => {
  // console.log(props.paramKey)
  // console.log(props.decimalLength)

  if (props.decimalLength == 2) {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 8 ? index : 8) + value.slice(index);
    } else {
      value = value.slice(0, 8);
    }
    return value;
  }else if (props.decimalLength ==4 &&props.paramKey != "buildingArea") {
console.log(11166)
value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 6 ? index : 6) + value.slice(index);
    } else {
      value = value.slice(0, 6);
    }
    return value;
    // value = value.toString();
    // // 先把非数字的都替换掉，除了数字和小数点
    // value = value.replace(/[^\d.]/g, "");
    // // 必须保证第一个为数字而不是小数点
    // value = value.replace(/^\./g, "");
    // // 保证只有出现一个小数点而没有多个小数点
    // value = value.replace(/\.{2,}/g, ".");
    // // 保证小数点只出现一次，而不能出现两次以上
    // value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // // 保证只能输入4个小数
    // value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
    // // 只能8位整数
    // let index = value.indexOf(".");
    // if (index > -1) {
    //   value = value.slice(0, index < 8 ? index : 8) + value.slice(index);
    // } else {
    //   value = value.slice(0, 8);
    // }
    // return value;
  } else if (props.paramKey == "buildingArea") {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 12 ? index : 12) + value.slice(index);
    } else {
      value = value.slice(0, 12);
    }
    return value;
  } else if (props.paramKey == "rentIncAmount") {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 10 ? index : 10) + value.slice(index);
    } else {
      value = value.slice(0, 10);
    }
    return value;
  } else if (props.paramKey == "rentIncPercent") {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 5 ? index : 5) + value.slice(index);
    } else {
      value = value.slice(0, 5);
    }
    return value;
  } else {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < int ? index : int) + value.slice(index);
    } else {
      value = value.slice(0, int);
    }
    return value;
  }
};
const priceFormat2 = (value) => {
  if (
    props.paramKey == "rentFreeDays" ||
    props.paramKey == "rentIncMonth" ||
    props.paramKey == "landNum" ||
    props.paramKey == "rentIncMonthFrequency"
  ) {
    value = value.replace(/^[0]/, "");
    // value = value.replace(/^0+(\d)/g, "");
    value = value.replace(/[^0-9]/g, "");
    value = value.slice(0, props.maxLength||5);
    // value = value.slice(0, 5);
    return value;
  } else {
    value = value.toString();
    value = value.replace(/[^\w\.\/]/gi, "");
    return value;
  }
};
function handleInput(val) {
      // console.log(typeof Number(val));
      val=val.target.value
      console.log(val)
      let startVal='';
      let endVal='';
      if(props.valueRange){
        startVal=props.valueRange.split('-')[0]
        endVal=props.valueRange.split('-')[1]
        
      }
     if(val){
      if(startVal!=''&&endVal!=''){
        // val = val.replace(/^(0+)|[^\d]+/g, "");
        console.log(val)
        if (Number(val) < Number(startVal)) {
          val = Number(startVal);
        console.log(val)
        } else if (Number(val) > Number(endVal)) {
          val = Number(endVal);
        console.log(val)
        }
         rangeValue.value=val
      }else if(startVal!='' && endVal==''){
        // val = val.replace(/^(0+)|[^\d]+/g, "");
        if (Number(val) < startVal) {
          val = startVal;
        }
      rangeValue.value=val

      }else if(startVal=='' && endVal!=''){
        // val = val.replace(/^(0+)|[^\d]+/g, "");
        if (Number(val) > endVal) {
          val = endVal;
        }
      rangeValue.value=val
      
      }
      if(props.valueRange){
        console.log(rangeValue.value)
        props.modelValue=rangeValue.value
        emits("update:modelValue", rangeValue.value);
      }
     }
      // val = val.replace(/^(0+)|[^\d]+/g, "");
      // if (Number(val) < 10) {
      //   val = 10;
      // } else if (Number(val) > 50) {
      //   val = 50;
      // }
    }
const priceFormatRange = (value) => {
  console.log(props.valueRange)
  // if (
  //   props.valueRange == "10-50"
  // ) {
    if (
    props.valueRange
  ) {
    console.log(props.decimalLength)
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    // value = value.replace(/[^\d.]/g, "");
    if(props.decimalLength==0){
      value = value.replace(/[^\d]/g, "");
    }else{
      value = value.replace(/[^\d.]/g, "");
    } 
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入2个小数
    if(props.decimalLength==0){
      value = value.replace(/^(\d+)\.(\d{0,0}).*$/, "$1.$2");
    }else if(props.decimalLength==1){
      value = value.replace(/^(\d+)\.(\d{0,1}).*$/, "$1.$2");
    }else if(props.decimalLength==2){
      value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    }else if(props.decimalLength==3){
      value = value.replace(/^(\d+)\.(\d{0,3}).*$/, "$1.$2");
    }else if(props.decimalLength==4){
      value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
    }else if(props.decimalLength==5){
      value = value.replace(/^(\d+)\.(\d{0,5}).*$/, "$1.$2");
    }
    // value = value.replace(/^[0]/, "");
    // value = value.replace(/[^0-9]/g, "");
    console.log(value,'valuevalue')
    return value;
  } else {
    value = value.toString();
    value = value.replace(/[^\w\.\/]/gi, "");
    return value;
  }
};
const priceFormat4 = (value) => {
  value = value.replace(/^(0+)|[^\d]+/g, ""); // 以0开头或者输入非数字，会被替换成空
  // value = value.replace(/(\d{10})\d*/, '$1') // 最多保留10位整数
  return value;
};

const priceFormat3 = (val) => {
  // val = val.replace(/^(([1-9]\d?)|100)/g, "");
  if (val < 0) {
    // 如果输入的值小于0
    return 0;
  } else if (val >= 100) {
    // 如果输入的值大于等于100
    return 100;
  }
  val = val.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
  val = val.replace(/^\./g, ""); //验证第一个字符是数字而不是
  val = val.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
  val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  switch (2) {
    case 1:
      val = val.replace(/^(\\-)*(\d+)\.(\d).*$/, "$1$2.$3"); //只能输入一个小数
      break;
    case 2:
      val = val.replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
      break;
    case 3:
      val = val.replace(/^(\\-)*(\d+)\.(\d\d\d).*$/, "$1$2.$3"); //只能输入三个小数
      break;
    case 4:
      val = val.replace(/^(\\-)*(\d+)\.(\d\d\d\d).*$/, "$1$2.$3"); //只能输入四个小数
      break;
    default:
      val = val.replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
      break;
  }
  return val;
};
const priceFormat33 = (value) => {
  // val = val.replace(/^(([1-9]\d?)|100)/g, "");
  if (value < 0) {
    // 如果输入的值小于0
    return 0;
  }
  value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
    // 只能8位整数
    let index = value.indexOf(".");
    if (index > -1) {
      value = value.slice(0, index < 3 ? index : 3) + value.slice(index);
    } else {
      value = value.slice(0, 3);
    }
    return value;
};
function formatMoneyWith6digts2(value) {
  let obj = value.toString();
  if (String(obj).indexOf(".") > -1) {
    //判断有没有输入小数点
  } else {
    var p11 = /[^\d]/g;
    var p22 = /(\d{6})\d*$/g;
    var p44 = /(\d*)\6/g; //删除当我输入第七位的整数时候进行删除
    obj = obj.replace(p11, "").replace(p22, "$1").replace(p44, "$1$9");
  }
  var p1 = /[^\d\.]/g; // 过滤非数字及小数点 /g :所有范围中过滤
  var p2 = /(\.\d{4})\d*$/g;
  var p4 = /(\.)(\d*)\1/g;
  obj = obj.replace(p1, "").replace(p2, "$1").replace(p4, "$1$9");
  obj = obj.replace(/[^0-9.]/g, "");
  var p5 = /\.+/g; //多个点的话只取1个点，屏蔽1....234的情况
  obj = obj.replace(p5, ".");
  var p6 = /(\.+)(\d+)(\.+)/g; //屏蔽1....234.的情况
  obj = obj.replace(p6, "$1$2"); // 屏蔽最后一位的.
  props.modelValue = obj;
}
let checkList = ref([]);
let dataList = ref([]); //乡镇
let dataList2 = ref([]); //村社区
function handleCheckedCitiesChange(value) {
  checkList.value = checkList.value.filter((item) => item.trim() !== "");
  emits("update:modelValue", checkList.value.join(","));
  emits("changeYXQGZ", checkList.value.join(","));
}
/** 懒加载获取树形结构*/
function getList() {
  if (props.paramKey == "townId") {
    let params = {
      areaId: "3781d3ca21cb11ec8850f48e38bf4326",
      minLevel: "4",
      type: "1",
      useToken: "1",
    };
    $axios({
      method: "get",
      url: "/area/getAreaTree",
      data: params,
    })
      .then((response) => {
        if (response.data.code == 200) {
          response.data.data[0].children.forEach((element) => {
            element.children = [];
          });
          dataList.value = response.data.data[0].children;
        } else {
        }
      })
      .catch(() => {});
  }
}
const mapClick = () => {
  if (
    (props.paramKey == "addressLocation" ||
    props.paramKey == "storeLocation") && !props.disabled
  ) {
    emits("mapClick");
  }
  // if (
  //   props.paramKey == "addressLocation" ||
  //   props.paramKey == "storeLocation"
  // ) {
  //   emits("mapClick");
  // }
};
const nodeName = ref(); //回显值
const nodeName2 = ref(); //回显值
let deptId = reactive({
  id: "",
});
function handleNodeClick(node) {
  emits("changeTree", node.id, 2);
}
const getTree3 = inject("getTree3");
function handleNodeClick2(node) {
  // emits('update:modelValue', node.id);//v-model方式
  // nodeName2.value = node.name;
}
function handleChange(e) {
  emits("changeNcp", e);
}
function changeSelect(e) {
  if (props.dictType == "LZFS") {
    emits("changeShowZj", e);
  }
  if (props.paramKey == "yearNum" || props.paramKey == "monthNum") {
    emits("changeShowZj2", e, props.objVal);
  }
}
function changeRadio(e) {
  if (props.dictType == "YXQXQ") {
    emits("changeYXQXQ", e, props.objVal);
  }
  if (props.dictType == "FWFSQGZ") {
    emits("changeFWFSQGZ", e, props.objVal);
  }
  if (props.dictType == "LYSQGZ") {
    emits("changeLYSQGZ", e, props.objVal);
  }
  if (props.dictType == "ZJDJ") {
    emits("changeZJDJ", e, props.objVal);
  }
}
function changeInput(e) {
  if (props.paramKey == "totalTransationPrice") {
    if (e.target) {
      emits("changePriceUpper", e.target.value);
    }
  }
  if (props.dictType == "telephone") {
    emits("regPhone", e, props.objVal);
  }
}
function changeInput2(e) {
  // 勿删
  if (props.paramKey == "totalTransationPrice") {
    if (e.target) {
      emits("changePriceUpper", e.target.value);
    }
  }
}
let changeInp=(e)=>{
  console.log(e)
    if(props.paramKey=='projectNumberArea'){
      getTree3(e)
    }
}
function changeMoney(e) {
  toChies(e);
}
function toChies2(amount){
  amount=amount*10000;
  // 汉字的数字
  const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // 基本单位
  const cnIntRadice = ["", "拾", "佰", "仟"];
  // 对应整数部分扩展单位
  const cnIntUnits = ["", "万", "亿", "兆"];
  // 对应小数部分单位
  const cnDecUnits = ["角", "分", "厘", "毫"];
  // 整数金额时后面跟的字符
  const cnInteger = "整";
  // 整型完以后的单位
  const cnIntLast = "元";
  // 最大处理的数字
  const maxNum = 999999999999999.99;
  // 金额整数部分
  let integerNum;
  // 金额小数部分
  let decimalNum;
  // 输出的中文金额字符串
  let chineseStr = "";
  // 分离金额后用的数组，预定义
  let parts;
  if (amount === "") {
    return "";
  }
  amount = parseFloat(amount);
  if (amount >= maxNum) {
    // 超出最大处理数字
    return "";
  }
  if (amount === 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  // 转换为字符串
  amount = amount.toString();
  if (amount.indexOf(".") === -1) {
    integerNum = amount;

    decimalNum = "";
  } else {
    parts = amount.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        // 归零
        zeroCount = 0;
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  // 小数部分
  if (decimalNum !== "") {
    const decLen = decimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1);
      if (n !== "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr === "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === "") {
    chineseStr += cnInteger;
  }
  moneyUpper.value = chineseStr;
  return chineseStr;
  //  // 汉字的数字
  // const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // // 基本单位
  // const cnIntRadice = ["", "拾", "佰", "仟"];
  // // const cnIntRadice = ["", "拾", "佰", "仟"];
  // // 对应整数部分扩展单位
  // const cnIntUnits = ["", "万"];
  // // const cnIntUnits = ["", "万", "亿", "兆"];
  // // 对应小数部分单位
  // const cnDecUnits = ["仟","佰","拾","角", "分", "厘", "毫"];
  // // const cnDecUnits = ["角", "分", "厘", "毫"];
  // // 整数金额时后面跟的字符
  // const cnInteger = "整";
  // // 整型完以后的单位
  // const cnIntLast = "万元";
  // // 最大处理的数字
  // const maxNum = 999999999999999.99;
  // // 金额整数部分
  // let integerNum;
  // // 金额小数部分
  // let decimalNum;
  // // 输出的中文金额字符串
  // let chineseStr = "";
  // // 分离金额后用的数组，预定义
  // let parts;
  // if (amount === "") {
  //   return "";
  // }
  // amount = parseFloat(amount);
  // if (amount >= maxNum) {
  //   // 超出最大处理数字
  //   return "";
  // }
  // if (amount === 0) {
  //   chineseStr = cnNums[0] + cnIntLast + cnInteger;
  //   return chineseStr;
  // }
  // // 转换为字符串
  // amount = amount.toString();
  // if (amount.indexOf(".") === -1) {
  //   integerNum = amount;

  //   decimalNum = "";
  // } else {
  //   parts = amount.split(".");
  //   integerNum = parts[0];
  //   decimalNum = parts[1].substr(0, 4);
  // }
  // // 获取整型部分转换
  // if (parseInt(integerNum, 10) > 0) {
  //   let zeroCount = 0;
  //   const IntLen = integerNum.length;
  //   for (let i = 0; i < IntLen; i++) {
  //     const n = integerNum.substr(i, 1);
  //     const p = IntLen - i - 1;
  //     const q = p / 4;
  //     const m = p % 4;
  //     if (n === "0") {
  //       zeroCount++;
  //     } else {
  //       if (zeroCount > 0) {
  //         chineseStr += cnNums[0];
  //       }
  //       // 归零
  //       zeroCount = 0;
  //       //alert(cnNums[parseInt(n)])
  //       chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
  //     }
  //     if (m === 0 && zeroCount < 4) {
  //       chineseStr += cnIntUnits[q];
  //     }
  //   }
  //   chineseStr += cnIntLast;
  // }
  // // 小数部分
  // if (decimalNum !== "") {
  //   const decLen = decimalNum.length;
  //   for (let i = 0; i < decLen; i++) {
  //     const n = decimalNum.substr(i, 1);
  //     if (n !== "0") {
  //       chineseStr += cnNums[Number(n)] + cnDecUnits[i];
  //     }
  //   }
  // }
  // if (chineseStr === "") {
  //   chineseStr += cnNums[0] + cnIntLast + cnInteger;
  // } else if (decimalNum === "") {
  //   chineseStr += cnInteger;
  // }
  // moneyUpper.value = chineseStr;
  // return chineseStr;
  
}
// 大写数字过滤器
function toChies(amount) {
  // 汉字的数字
  const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // 基本单位
  const cnIntRadice = ["", "拾", "佰", "仟"];
  // 对应整数部分扩展单位
  const cnIntUnits = ["", "万", "亿", "兆"];
  // 对应小数部分单位
  const cnDecUnits = ["角", "分", "厘", "毫"];
  // 整数金额时后面跟的字符
  const cnInteger = "整";
  // 整型完以后的单位
  const cnIntLast = "元";
  // 最大处理的数字
  const maxNum = 999999999999999.99;
  // 金额整数部分
  let integerNum;
  // 金额小数部分
  let decimalNum;
  // 输出的中文金额字符串
  let chineseStr = "";
  // 分离金额后用的数组，预定义
  let parts;
  if (amount === "") {
    return "";
  }
  amount = parseFloat(amount);
  if (amount >= maxNum) {
    // 超出最大处理数字
    return "";
  }
  if (amount === 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  // 转换为字符串
  amount = amount.toString();
  if (amount.indexOf(".") === -1) {
    integerNum = amount;

    decimalNum = "";
  } else {
    parts = amount.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        // 归零
        zeroCount = 0;
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  // 小数部分
  if (decimalNum !== "") {
    const decLen = decimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1);
      if (n !== "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr === "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === "") {
    chineseStr += cnInteger;
  }
  moneyUpper.value = chineseStr;
  return chineseStr;
}

const state = reactive({
  timeout: null,
  RWMC: "",
});
const changeCertNo = inject("changeCertNo");
const crfCertNo = inject("crfCertNo");
const dyCertNo = inject("dyCertNo");

const handleSelect = (item) => {
  // emits('changeCertNo', item)
  changeCertNo(item, props.objVal, 1);
};
const handleSelect2 = (item) => {
  crfCertNo(item, props.objVal);
  // emits('changeOrg', item.deptCode)
};
const handleSelect3 = (item) => {
  dyCertNo(item, props.objVal);
  // emits('changeOrg', item.deptCode)
};
const querySearchAsync2 = async (queryString, cb) => {
  $axios({
    url: "/transferor/getDept/" + props.unitId,
    method: "get",
  }).then((res) => {
    if (res.data.code === 200) {
      if (res.data.data != null && res.data.data) {
        const restaurants = res.data.data;
        cb(restaurants);
      } else {
        cb([]);
      }
    } else {
      ElMessage.error(res.data.msg);
    }
  });
};
const querySearchAsync3 = async (queryString, cb) => {
  if (props.paramKey == "mortgageeName") {
    var results = [];
    $axios({
      url: "/mortgageMortgagee/getDeptList",
      method: "get",
      data: {
        name: queryString,
        pageNo: 1,
        pageSize: 100000,
      },
    }).then((res) => {
      if (res.data.code === 200) {
        if (res.data.data.records != null && res.data.data) {
          results = res.data.data.records;
          cb(results);
        } else {
          cb([]);
        }
      } else {
        ElMessage.error(res.data.msg);
      }
    });
    
  }
};
//获取输入建议的方法,回调函数返回
let blurStatus = ref(false);
const blur = () => {
  if (blurStatus.value == false) {
    changeCertNo(1, props.objVal, 2);
  }
};
const querySearchAsync = async (queryString, cb) => {
  clearTimeout(state.timeout);
  var results = [];
  if (queryString == "") {
    cb(results);
  } else {
    //掉接口需要的参数
    try {
      let result = [];
      $axios({
        method: "get",
        url: "/tenders/getHyUser/" + queryString,
      })
        .then((response) => {
          if (response.data.code == 200) {
            result = response.data.data.data;
            if (result) {
              blurStatus.value = true;
              //循环放到一个远程搜索需要的数组
              for (let i = 0; i < result.length; i++) {
                const element = result[i];
                results.push({
                  // value: element.retenant +element.reletCertNo,
                  value: element.retenant,
                  link: element.reletCertNo,
                  id: element.id,
                  reletCertNo: element.reletCertNo,
                  reletCertType: element.reletCertType,
                  legalPersonName: element.legalPersonName,
                  legalPersonNo: element.legalPersonNo,
                  legalPersonPhone: element.legalPersonPhone,
                  idType: element.idType,
                });
              }
              cb(results);
            } else {
              blurStatus.value = false;
              results = [];
              cb(results);
            }
          } else {
            // ElMessage.error(response.data.msg);
            blurStatus.value = false;
            // changeCertNo(1, props.objVal,2)
            results = [];
            cb(results);
          }
        })
        .catch(() => {});
    } catch (error) {
    }
  }
};
const selectList=ref([])
const listHasMore=ref([false]) //账户类型列表是否还有数据
const  pageOption=reactive({
  pageIndex: 1,
  pageSize: 20,
})

const vLoadMore={
  mounted(el, binding){
    // 获取element，定义scroll
    let select_dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
    console.log(el.querySelector('.el-select-dropdown .el-select-dropdown__wrap'),"监听滚动");
    select_dom?.addEventListener('scroll', function () {
      let height = this.scrollHeight - this.scrollTop <= this.clientHeight;
      if (height) {
        binding.value();
      }
    });
  }
}
//滚动到达底部触发加载更多事件
const loadList=()=> {
  console.log(listHasMore.value,"listHasMore.value");
      // 获取到的不是全部数据 当滚动到底部 继续获取新的数据
      if (listHasMore.value) {
        //加载更多(兼容IE-- IE浏览器需要防抖)
        shakeSubmit(search, 500)();
      } else {
        ElMessage.success('全部加载完毕');
        return false;
      }
}
//滚动条触发事件防抖
const shakeSubmit=(fn, delay)=>  {
  var timer=null
  return function (e) {
    //如果定时器存在则清空定时器
    if (timer) {
      clearTimeout(timer);
    }
    //设置定时器，规定时间后执行真实要执行的函数
    timer = setTimeout(function () {
      fn.apply(e);
    }, delay);
  };
}
const loading=ref(false)
const queryData=ref('')
const remoteMethod = (query) => {
  queryData.value=query
  if (query) {
    loading.value = true;
    selectList.value=[]
    pageOption.pageIndex=1
    if (props.paramKey == "certCode"){
      console.log( props.modelValue,"props.modelValue")
      $axios({
      url: "/mortgage/getForensicsCodeByParam",
      method: "post",
      data: {
        code: query,
        // code: '321181100002',
        pageNo: pageOption.pageIndex,
        pageSize: pageOption.pageSize,
      },
    }).then((res) => {
      if (res.data.code === 200) {
        if (res.data.data.records != null && res.data.data) {
        //添加列表数据
          res.data.data.records.forEach((item) => {
            selectList.value.push(item);
          });
          //判断是否有下一页
          if (res.data.data.current<res.data.data.pages) {
              nextTick(() => {
                //还有下一页
                listHasMore.value = true;
                pageOption.pageIndex += 1;
              })
            } else {
              nextTick(() => {
                listHasMore.value = false;
              })
            }
        }
        loading.value = false;
      } else {
        loading.value = false;
        ElMessage.error(res.data.msg);
      }
    });
    }
  } else {
    selectList.value = []
  }
}
const search = () => {
  if (queryData.value) {
    loading.value = true;
    if (props.paramKey == "certCode"){
      $axios({
      url: "/mortgage/getForensicsCodeByParam",
      method: "post",
      data: {
        code: queryData.value,
        // code: '321181100002',
        pageNo: pageOption.pageIndex,
        pageSize: pageOption.pageSize,
      },
    }).then((res) => {
      if (res.data.code === 200) {
        if (res.data.data.records != null && res.data.data) {
          //添加列表数据
          res.data.data.records.forEach((item) => {
            selectList.value.push(item);
          });
          //判断是否有下一页
          if (res.data.data.current<res.data.data.pages) {
              nextTick(() => {
                //还有下一页
                listHasMore.value = true;
                pageOption.pageIndex += 1;
              })
            } else {
              nextTick(() => {
                listHasMore.value = false;
              })
            }
        }
        loading.value = false;
      } else {
        loading.value = false;
        ElMessage.error(res.data.msg);
      }
    });
    }
  } else {
    selectList.value = []
  }
}
const selectChange=(val)=>{
  toSearch(val)
  console.log(val,"selectChange")
}
onMounted(() => {
  if (props.type == "9" || props.type == 9) {
    if (props.modelValue == null || props.modelValue == undefined) {
    } else {
      let str = props.modelValue;
      checkList.value = str.split(",");
    }
  }
});
</script>
  
<style lang='scss' scoped>
.set-up-class {
  :deep(.left-box) {
    span::before {
      content: "*";
      color: red;
    }
  }
}
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #fafafa;
}
.shadows :deep(.el-input__wrapper) {
  box-shadow: none;
  border: 1px solid #dcdfe6;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-right: none;
}
:deep(.left-box) {
  text-align: left;
  // text-align: right;
  min-width: 150px !important;
}
:deep(.input-with-select .el-input-group__prepend) {
  background-color: #fff;
  box-shadow: none;
}
:deep(.el-input-group__append) {
  box-shadow: none;
}
// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-autocomplete) {
  width: 100%;
  .el-input__wrapper{
    height: 32px;
  }
}

:deep(.el-cascader) {
  width: 100%;
}

.nd-input-box :deep(.el-input) {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
  width: 100%;
  // padding-left: 10px;
  padding-right: 10px;
}

:deep(.el-input.is-disabled) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>
  