<template>
  <!-- 表单区域 -->
  <el-scrollbar>
    <div class="main-cont">
      <el-form
        ref="ruleFormRef"
        :model="costObj"
        :rules="rules"
        label-width="160px"
        :size="formSize"
        :status-icon="false"
      >
        <div class="row-cont">
          <div>
            <el-form-item label="入金节点" prop="incomeFlow">
              <nd-select
                style="width: 387px;"
                v-model="costObj.incomeFlow"
                placeholder=""
                :disabled="isDisabled"
                clearable
              >
                <el-option
                  v-for="(item, index) in selectArr.incomeFlowList"
                  :key="index"
                  :label="item.dataValue"
                  :value="item.dataKey"
                />
              </nd-select>
            </el-form-item>
          </div>
            <div>
            <el-form-item label="是否需要确认收款" prop="needPaymentFlag">
              <el-radio-group
                style="width: 387px"
                v-model="costObj.needPaymentFlag"
                :disabled="isDisabled"
                @change="paymentFlagChange"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div>
            <!-- <el-form-item label="是否需要出金申请" prop="needExpendFlag">
              <el-radio-group
                style="width: 387px"
                v-model="costObj.needExpendFlag"
                :disabled="isDisabled"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item> -->
          </div>
        </div>
        <div class="row-cont" v-if="costObj.needPaymentFlag === '1'">
          <div>
            <el-form-item label="支付方式" prop="needPaymentWay">
              <nd-select
                style="width: 387px"
                v-model="costObj.paymentWayArr"
                placeholder="请选择需要确认收款的支付方式"
                multiple
                :disabled="isDisabled"
                clearable
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="item in selectArr.paymentWayList"
                  :key="item.dataKey"
                  :label="item.dataValue"
                  :value="item.dataKey"
                />
              </nd-select>
            </el-form-item>
          </div>
        </div>
        <div class="row-cont">
          <div>
            <el-form-item label="缴款过期未到账" prop="payExpire">
              <nd-select
                style="width: 387px"
                v-model="costObj.payExpire"
                placeholder="请选择"
                :disabled="isDisabled"
                clearable
              >
                <el-option
                  v-for="(item, index) in selectArr.payExpireList"
                  :key="index"
                  :label="item.dataValue"
                  :value="item.dataKey"
                />
              </nd-select>
            </el-form-item>
          </div>
          <div>
             <ndTable>
                      
            </ndTable>
          </div>
        </div>
        <div class="row-cont">
          <div>
            <el-form-item label="未成交方出金节点" prop="notClosedExpendFlow">
              <nd-select
                style="width: 387px"
                v-model="costObj.notClosedExpendFlow"
                placeholder=""
                :disabled="isDisabled"
                clearable
              >
                <el-option
                  v-for="(item, index) in selectArr.notExpendFlowList"
                  :key="index"
                  :label="item.dataValue"
                  :value="item.dataKey"
                />
              </nd-select>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="是否需要自动退款" prop="needRefundFlag">
              <el-radio-group
                style="width: 387px"
                v-model="costObj.needRefundFlag"
                :disabled="isDisabled"
                @change="returnChange"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>

        <div class="row-cont">
          <div>
            <el-form-item label="成交方出金节点" prop="closedExpendFlow">
              <nd-select
                style="width: 387px"
                v-model="costObj.closedExpendFlow"
                placeholder=""
                :disabled="isDisabled"
                clearable
              >
                <el-option
                  v-for="(item, index) in selectArr.closedExpendFlowList"
                  :key="index"
                  :label="item.dataValue"
                  :value="item.dataKey"
                />
              </nd-select>
            </el-form-item>
          </div>

        </div>

        <div class="row-cont">

        </div>

        <div class="row-cont" v-if="costObj.needRefundFlag === '1'">
          <div>
            <el-form-item
              label="未成交方退款规则"
              prop="refundRule"
              style="display: flex; align-items: center"
            >
              <div class="account-cont">
                <div class="account-text">出金节点后第</div>
                <div style="margin: 0 10px">
                  <nd-input
                    style="width: 180px"
                    v-model="costObj.refundRule"
                    @change="inputChange"
                    @input="handleInput($event)"
                    placeholder="请输入"
                    clearable
                    :disabled="isDisabled"
                  />
                </div>
                <div class="account-text">天,系统自动退款</div>
              </div>
            </el-form-item>
          </div>
        </div>

        <el-form-item v-if="!isDisabled">
          <nd-button
            type="primary"
            style="width: 100px; color: #fff"
            class="btn"
            @click="whetherSave"
          >
            <el-icon style="margin-right: 6px">
              <img :src="tj" alt="" srcset="" /> </el-icon
            >确认
          </nd-button>
          <nd-button style="width: 100px" class="btn" @click="handleClose">
            <el-icon style="margin-right: 6px"> <CircleClose /> </el-icon>取消
          </nd-button>
        </el-form-item>
      </el-form>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref, reactive, inject, onMounted, provide, watch } from "vue";
import ndButton from "@/components/ndButton.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import tj from "@/assets/images/tj.png";

let areaId = inject("areaId");
const $axios = inject("$axios");
const emits = defineEmits(["closeItem", "showEdit", "firstOpen"]);

let props = defineProps({
  costData: {
    type: Array,
    default: undefined,
  },
  placeIndex: {
    type: Number,
    default: undefined,
  },
  compEdit: {
    type: Boolean,
    default: undefined,
  },
  showIndex: {
    type: Number,
    default: undefined,
  },
  openDisabled: {
    default: undefined,
  },
});

const rules = reactive({
  incomeFlow: [{ required: true, message: "请选择", trigger: "change" }],
  needExpendFlag: [
    {
      required: true,
      message: "请选择",
      trigger: "blur",
    },
  ],
  notClosedExpendFlow: [
    {
      required: true,
      message: "请选择",
      trigger: "change",
    },
  ],
  closedExpendFlow: [
    {
      required: true,
      message: "请选择",
      trigger: "change",
    },
  ],
  needPaymentFlag: [
    {
      required: true,
      message: "请选择",
      trigger: "blur",
    },
  ],
  payExpire: [
    {
      required: true,
      message: "请选择",
      trigger: "change",
    },
  ],
  needRefundFlag: [
    {
      required: true,
      message: "请选择",
      trigger: "change",
    },
  ],
  refundRule: [
    {
      required: true,
      message: "请输入",
      trigger: "blur",
    },
  ],
  needPaymentWay: [
    {
      required: true,
      message: "请选择支付方式",
      trigger: "change",
      validator: (rule, value, callback) => {
        if (costObj.needPaymentFlag === '1' && (!costObj.paymentWayArr || costObj.paymentWayArr.length === 0)) {
          callback(new Error('请至少选择一种支付方式'));
        } else {
          callback();
        }
      }
    },
  ],
});

watch(
  () => props.costData,
  (newVal, oldVal) => {
    renderData();
  },
  { deep: true }
);

watch(
  () => props.compEdit,
  (newVal, oldVal) => {
    if (props.compEdit && props.showIndex == props.placeIndex) openEdit();
  }
);
watch(
  () => props.openDisabled,
  (newVal, oldVal) => {
    if (props.openDisabled) {
      isDisabled.value = true;
      emits("showEdit", props.placeIndex);
    }
  }
);

onMounted(() => {
  // getExpenseData()
  // v-if时候使用，v-show不用
  renderData();
  getSelectData();
});

// 下拉选择框
const selectArr = reactive({
  incomeFlowList: [],
  notExpendFlowList: [], //未成交方
  payExpireList: [],
  closedExpendFlowList: [], //成交方
  paymentWayList: [],
});
const costObj = reactive({
  closedExpendFlow: null, //成交方出金节点
  dataKey: null,
  effectFlag: null,
  expendFlow: null, //出金节点
  id: null,
  incomeFlow: null, //入金节点k
  needCleanFlag: null, //是否需要自动清算
  needExpendFlag: null, //是否需要出金申请
  needPaymentFlag: null, //是否需要确认收款
  notClosedExpendFlow: null, //未成交方出金节点
  payExpire: null, //缴款过期未到账
  cleanRule: null, //自动清算规则
  needRefundFlag: "", // 是否自动退款
  refundRule: "", //退款规则
  paymentWayArr: [],
});

const selectId = ref();
const isDisabled = ref(true);
const formSize = ref("default");
const ruleFormRef = ref(null);

const ruleForm = reactive({
  region: undefined,
  bank: undefined,
  resource: "是",
  isCal: "是",
  isEnsure: "否",
  settle: undefined,
});

// 渲染数据
const renderData = () => {
  console.log(props.costData, "传递的数据");
  let needExpendFlag;
  let needPaymentFlag;
  let needRefundFlag;
  props.costData.forEach((item) => {
    // 待定
    // if (item.costFeeConfigVo.effectFlag == 1) {
    //     isDisabled.value = true
    // }
    if (item.dataKey == 3) {
      if (
        item.costFeeConfigVo.needExpendFlag === 1 ||
        item.costFeeConfigVo.needExpendFlag === 0
      ) {
        needExpendFlag = item.costFeeConfigVo.needExpendFlag.toString();
      }
      if (
        item.costFeeConfigVo.needPaymentFlag === 1 ||
        item.costFeeConfigVo.needPaymentFlag === 0
      ) {
        needPaymentFlag = item.costFeeConfigVo.needPaymentFlag.toString();
      }

      if (
        item.costFeeConfigVo.needRefundFlag === 0 ||
        item.costFeeConfigVo.needRefundFlag === 1
      ) {
        needRefundFlag = item.costFeeConfigVo.needRefundFlag.toString();
        costObj.needRefundFlag = needRefundFlag;
      } else {
        costObj.needRefundFlag = "0";
      }

      costObj.refundRule = item.costFeeConfigVo.refundRule;
      
      // 处理支付方式数据
      if (item.costFeeConfigVo.needPaymentWay) {
        costObj.paymentWayArr = item.costFeeConfigVo.needPaymentWay.split(",");
      } else {
        costObj.paymentWayArr = [];
      }

      selectId.value = item.id;
      costObj.id = item.costFeeConfigVo.id;
      if (item.costFeeConfigVo.incomeFlow) {
        costObj.incomeFlow = item.costFeeConfigVo.incomeFlow;
      } else {
        costObj.incomeFlow = "0";
      }
      costObj.needExpendFlag = needExpendFlag;

      if (item.costFeeConfigVo.notClosedExpendFlow) {
        costObj.notClosedExpendFlow = item.costFeeConfigVo.notClosedExpendFlow;
      } else {
        costObj.notClosedExpendFlow = "1";
      }

      if (item.costFeeConfigVo.closedExpendFlow) {
        costObj.closedExpendFlow = item.costFeeConfigVo.closedExpendFlow;
      } else {
        costObj.closedExpendFlow = "2";
      }
      costObj.needPaymentFlag = needPaymentFlag;
      costObj.cleanRule = item.costFeeConfigVo.cleanRule;
      costObj.payExpire = item.costFeeConfigVo.payExpire;

      if (!item.costFeeConfigVo.id) {
        isDisabled.value = false;
        emits("firstOpen", props.placeIndex);
      }
    }
  });
};

// 获取下拉列表值
const getSelectData = () => {
  $axios({
    url: "/feeAreaConfig/getSelectInfo",
    method: "post",
    data: {
      buttonType: 1,
      effectFlag: null,
      id: selectId.value,
      orderTypeKey: 3,
    },
  }).then((res) => {
    console.log(res, "下拉选择框数据");
    if (res.data.code == 200) {
      selectArr.incomeFlowList = res.data.data.incomeFlowList;
      selectArr.notExpendFlowList = res.data.data.notExpendFlowList;
      selectArr.payExpireList = res.data.data.jkgqList;
      selectArr.closedExpendFlowList = res.data.data.closedExpendFlowList;
    }
  });
  
  // 获取支付方式枚举值
  $axios({
    url: "/feeAreaConfig/getPaymentWay?baseType=ZFFS",
    method: "get"
  }).then((res) => {
    console.log(res, "支付方式数据");
    if (res.data.code == 200) {
      selectArr.paymentWayList = res.data.data;
    }
  });
};

// 点击编辑，开启编辑
function openEdit() {
  isDisabled.value = false;
}

const handleClose = () => {
  emits("closeItem", props.placeIndex);
};

// 是否保存
const whetherSave = () => {
  ElMessageBox.confirm(
    "请确认是否保存资金配置?您的设置有可能影响该地区的资金数据，请谨慎处理！！！",
    "警告",
    {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      handleSave();
    })
    .catch(() => {
      console.log("取消");
    });
};

// 保存
const handleSave = async () => {
  await ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      let refundRule;
      if (
        costObj.refundRule !== "" &&
        costObj.refundRule !== null &&
        costObj.refundRule !== undefined
      ) {
        refundRule = Number(costObj.refundRule);
      } else {
        refundRule = costObj.refundRule;
      }
      const data = {
        cleanRule: costObj.cleanRule,
        closedExpendFlow: costObj.closedExpendFlow,
        id: costObj.id,
        incomeFlow: costObj.incomeFlow,
        expendFlow: costObj.expendFlow,
        needExpendFlag: Number(costObj.needExpendFlag),
        needPaymentFlag: Number(costObj.needPaymentFlag),
        notClosedExpendFlow: costObj.notClosedExpendFlow,
        orderType: 3,
        payExpire: costObj.payExpire,
        unitId: areaId._value,
        needRefundFlag: Number(costObj.needRefundFlag),
        refundRule,
        needPaymentWay: costObj.paymentWayArr.join(",") // 添加支付方式字段
      };

      // const data = { ...costObj, unitId: areaId,orderType:1 }
      console.log(data, "请求参数");

      $axios({
        url: "/feeAreaConfig/saveConfigInfo",
        method: "post",
        data,
      }).then((res) => {
        console.log(res, "保存结果");
        if (res.data.code == 200) {
          emits("showEdit", props.placeIndex);
          isDisabled.value = true;
          ElMessage({
            message: res.data.msg,
            type: "success",
          });
        } else {
          ElMessage({
            message: res.data.msg,
            type: "error",
          });
        }
      });
    } else {
      console.log("error submit!", fields);
    }
  });
};

function handleInput(event) {
  // 只允许输入数字
  console.log(event, "输入值");
  if (event == 0) {
    costObj.refundRule = costObj.refundRule.replace(/\D/g, "");
  } else {
    costObj.refundRule = costObj.refundRule.replace(/^0|\D/g, "");
  }
}

const inputChange = (e) => {
  console.log(e, "输入值");
  if (e > 365) {
    costObj.refundRule = 365;
  } else if (e < 1) {
    costObj.refundRule = "";
  }
};

const returnChange = (val) => {
  if (val === "0") {
    costObj.refundRule = "";
  }
};

const paymentFlagChange = (val) => {
  if (val === "0") {
    costObj.paymentWayArr = [];
  }
};

defineExpose({
  openEdit,
  getSelectData,
  renderData,
});
</script>

<style scoped lang="scss">
// :deep(.el-form-item__content) {
//     justify-content: center;
// }

// :deep(.el-form-item__label) {
//     width: 160px !important;
// }

// :deep(.el-radio-group) {
//     transform: translateX(-30px);
// }

.row-cont {
  display: flex;
  flex-direction: row;
}

.main-cont {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

.account-cont {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  // width:387px;

  .account-text {
    white-space: nowrap;
    font-family: Microsoft YaHei;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;
    color: #444444;
  }
}
</style>