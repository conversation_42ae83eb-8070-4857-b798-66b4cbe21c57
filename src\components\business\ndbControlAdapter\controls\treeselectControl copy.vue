<template>
  <!-- 乡镇街道 -->
  <div
    v-if="type == 7 && dataList.length != 0"
    style="display: flex; align-items: center; height: 100%; width: 100%"
  >
    <!-- <div
    v-if="type == 7"
    style="display: flex; align-items: center; height: 100%; width: 100%"
  > -->
    <div :style="matterFlag==2?'display: flex; flex-direction: row; width: 100%':'display: flex; flex-direction: row; width: 50%'">
      <nd-search-more-item
        :class="requireFlag == 1 ? 'set-up-class' : ''"
        :title="title"
        style="width: 100%"
        v-if="matterFlag==2"
      >
        <!-- style="flex:1"> -->
        <nd-tree-select
          v-model="objVal2.defaultValue"
          :disabled="disabled || props.jibie == false"
          width="90%"
          clearable
          :data="dataList"
          ref="treeRef"
          :render-after-expand="false"
          :props="{ value: 'id', label: 'name' }"
        />
      </nd-search-more-item>
      <nd-search-more-item
      v-else
        :class="requireFlag == 1 ? 'set-up-class' : ''"
        :title="title"
        style="width: 50%"
      >
        <!-- style="flex:1"> -->
        <nd-tree-select
          v-model="objVal2.defaultValue"
          :disabled="disabled || props.jibie == false"
          width="90%"
          clearable
          :data="dataList"
          ref="treeRef"
          :render-after-expand="false"
          :props="{ value: 'id', label: 'name' }"
          @node-click="handleNodeClick"
        />
      </nd-search-more-item>
      <!-- 村 -->
      <nd-search-more-item
        v-if="
          objVal2 &&
          objVal2.fieldList &&
          objVal2.fieldList[0].fieldKey == 'villageId'
        "
        :class="objVal2.fieldList[0].requireFlag == 1 ? 'set-up-class' : ''"
        title="村"
        style="width: 47%"
        class="zb"
      >
        <!-- :class="requireFlag == 1 ? 'set-up-class' : ''" title="村" :style="{ width: width }" style="flex:1"> -->
        <nd-tree-select
          :disabled="disabled || props.jibiecun == false"
          v-model="objVal2.fieldList[0].defaultValue"
          width="90%"
          clearable
          :data="dataList2"
          ref="treeRef2"
          :placeholder="disabled == true ? ' ' : '请选择'"
          :render-after-expand="false"
          :props="{ value: 'id', label: 'name' }"
          @node-click="handleNodeClick2"
        />
      </nd-search-more-item>
    </div>
    <div style="width: 50%" v-if="matterFlag!=2">
      <!-- 组别 -->
      <nd-search-more-item
        v-if="
          objVal2 &&
          objVal2.fieldList &&
          objVal2.fieldList[1].fieldKey == 'groupCode'
        "
        :class="objVal2.fieldList[0].requireFlag == 1 ? 'set-up-class' : ''"
        title="组别"
        style="width: 100%"
      >
        <!-- :class="requireFlag == 1 ? 'set-up-class' : ''"  title="组别" :style="{ width: width }" style="flex:1"> -->
        <nd-input
          v-model="objVal2.fieldList[1].defaultValue"
          :disabled="disabled"
          :placeholder="disabled == true ? '' : placeholder"
          width="90%"
          clearable
        ></nd-input>
      </nd-search-more-item>
    </div>
  </div>
  <div
    v-if="type == 7 && items.matterFlag == 1"
    style="
      display: flex;
      align-items: center;
      height: 100%;
      width: 100%;
      flex-direction: column;
    "
  >
  
    <!-- 标的物组件 -->
    <!-- <nd-search-more-item
    title="交易标的物" style="width:100%"
     width="100%"
  > -->
    <div :class="disabled ? 'butClick' : ''"
      style="
        width: 100%;
        font-size: 14px;
        color: #555555;
        padding-bottom: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      "
    >
      <span>交易标的物</span>
      <div v-if="!disabled">
        <nd-button icon="Plus" @click="addForm(1)">新增标的物</nd-button>
        <nd-button icon="Plus" @click="addForm(2)">从标的物库新增</nd-button>
      </div>
    </div>
    <div  :class="disabled ? 'butClick' : ''"
      style="
        width: 100%;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 10px 10px;
        border: 1px solid #eeeeee;
        border-bottom: none;
      "
    >
      <span style="margin-right: 10px">本次交易标的数量合计:</span>
      <nd-input disabled placeholder="自动计算" v-model="tenderData.length" clearable></nd-input>
      <span style="margin-left: 10px">个</span>
      <span style="margin-right: 10px; margin-left: 20px"
        >交易面积/数量合计:</span
      >
      <nd-input :disabled="disabled" placeholder="自动计算支持修改" v-model="matterArea" clearable :formatter="priceFormat2" :parser="priceFormat2"></nd-input>
      <nd-select :disabled="disabled" style="margin-left: 10px; width: 100px" v-model="matterAreaUnit" >
        <el-option v-for="item in optionList2" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue" />
      </nd-select>
    </div>
    <nd-table  :class="disabled ? 'butClick' : ''"
      style="height: 350px; width: 100%; margin-bottom: 15px"
      :data="tenderData"
    >
      <el-table-column
        align="center"
        :index="indexMethod"
        type="index"
        label="序号"
        width="80"
      />
      <el-table-column align="center" prop="description" label="* 标的物名称">
        <template #header>
          <span class="set-class">标的物名称</span>
        </template>
        <template #default="{ row }">
          <nd-input  v-if="row.assetId==''||row.assetId==null" :disabled="disabled"
            style="width: 100%" maxLength="50"
            placeholder="请输入"
            clearable
            v-model="row.name"
          ></nd-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="标的物编号">
        <template #header>
          <span>标的物编号</span>
        </template>
        <template #default="{ row }">
          <span style="color:#C0C4CC;">{{ row.code || "无需填写" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="标的物类型">
        <template #default="{ row }">
          <el-select v-model="row.type" :disabled="disabled" placeholder="请选择" v-if="row.assetId==''||row.assetId==null" >
            <el-option
            v-for="item in optionList1" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue">
            </el-option>
          </el-select>
          <span v-else style="text-align:left;display:inline-block;width:100%;">{{ row.type }}</span>
        </template>
        <!-- <template #default="{ row }">
          <nd-select @change="changeType" style="width: 100%" v-model="row.value3"> 
            <el-option v-for="item in optionList1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
          </nd-select>
        </template> -->
      </el-table-column>
      <el-table-column align="right" prop="description" label="*交易面积/数量">
        <template #header>
          <span class="set-class">交易面积/数量</span>
        </template>
        <template #default="{ row }">
          <nd-input class="nd-input-bdw" :disabled="disabled"
            style="width: 100%;" @blur="blur"  :formatter="priceFormat" :parser="priceFormat"
            placeholder="请输入"
            clearable
            v-model="row.matterArea"
          ></nd-input>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="*单位">
        <template #header>
          <span class="set-class">单位</span>
        </template>
        <template #default="{ row }">
          <el-select  :disabled="disabled" v-if="row.assetId==''||row.assetId==null" v-model="row.matterAreaUnit" placeholder="请选择"  style="width: 100%">
            <el-option
            v-for="item in optionList2" :key="item.dataValue" :label="item.dataValue" :value="item.dataValue">
            </el-option>
          </el-select>
          <span v-else  style="text-align:left;display:inline-block;width:100%;">{{ row.matterAreaUnit }}</span>
          <!-- <nd-select @change="changeUnit" style="width: 100%"  v-model="row.value5">
            <el-option v-for="item in optionList1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
          </nd-select> -->
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="description"
        label="预期价格（元）"
      >
        <template #header>
          <span>预期价格（元）</span>
        </template>
        <template #default="{ row }">
          <nd-input
            style="width: 100%" :disabled="disabled"  @blur="blur"  :formatter="priceFormat1" :parser="priceFormat1"
            placeholder="请输入"
            clearable
            v-model="row.matterPrice"
          ></nd-input>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        align="center"
        prop="status"
        width="80"
        label="操作"
      >
        <template #default="scoped">
          <span style="cursor: pointer;color:#0b8df1;"  @click.stop="deleteTender(scoped.row, scoped.$index)">删除</span>
          <!-- <el-tooltip effect="light" content="删除">
            <el-button
              link
              @click.stop="deleteTender(scoped.row, scoped.$index)"
            >
              <img
                src="@/assets/images/transactionManageImage/delete.png"
                alt=""
              />
            </el-button>
          </el-tooltip> -->
        </template>
      </el-table-column>
    </nd-table>
    <!-- </nd-search-more-item> -->
    <!-- 标的物组件 -->
  </div>
  <ndDialog
    ref="dialog"
   
    title="从标的物库新增"
    style="overflow: hidden;padding: 0px 10px"
  >
    <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center">
      <ndButton
        @click="toSure"
        style="width:80px;"
        type="primary"
        color="#0b8df1"
        >确认</ndButton
      >
      <div style="display: flex;flex-direction: row;justify-content: space-between;">
        <nd-input
            style="width: 100%;margin-right: 10px" v-model="proForm.assetName" maxLength="50"
            placeholder="资产/资源名称"
            clearable
          ></nd-input>
          <ndButton
        @click="toSearch()"
        style="width:80px;"
        type="primary"
        color="#0b8df1"
        >查询</ndButton
      >
      </div>
    </div>
    <nd-table
      style="height: 360px; width: 100%; margin-bottom: 15px;margin-top: 10px;" ref="tableRef"
      :data="tenderData2"  row-key="id" @selection-change="handleSelectionChange"
    >
    <el-table-column
      type="selection" label="选择"
      width="55">
    </el-table-column>
      <el-table-column align="center" prop="assetName" label="资产/资源名称">
      </el-table-column>
      <el-table-column align="center" prop="description" label="所属组织">
      </el-table-column>
      <el-table-column align="center" prop="assetCode" label="资产/资源编号">
      </el-table-column>
      <el-table-column align="center" prop="amount" label="面积/数量">
      </el-table-column>
      <el-table-column align="center" prop="overplusAmount" label="可申请面积/数量">
      </el-table-column>
      <el-table-column align="center" prop="assetType" label="类型">
      </el-table-column>
    </nd-table>
    
   
    <template #footer>
      <div>
      <nd-pagination v-model:total="page.total" v-model:current-page="proForm.pageNo"
          v-model:page-size="proForm.pageSize" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
      </nd-pagination>
    </div>
    </template>
  </ndDialog>
</template>

<script setup>
// 导入公共组件
import ndTreeSelect from "@/components/ndTreeSelect.vue";
import ndInput from "@/components/ndInput.vue";
import ndSearchMoreItem from "@/components/business/ndbControlAdapter/controlItem.vue";
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndPagination from "@/components/ndPagination.vue";

import { ElMessage, ElMessageBox } from "element-plus";

import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
const $axios = inject("$axios");
const currentInstance = getCurrentInstance();
const props = defineProps({
  type: {
    // 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
    type: Number,
    default: "default",
  },
  width: {
    //宽度
    type: String,
    default: "",
  },
  title: {
    //标题
    type: String,
    default: "",
  },
  disabled: {
    // 只读
    type: Boolean,
    default: false,
  },
  requireFlag: {
    // 是否必填
    type: Number,
    default: 1, //1是0否
  },
  modelValue: {
    //双向绑定
    type: String || Object,
  },
  jsonData: {
    type: Array,
    default: [],
  },
  dictType: {
    type: String,
    default: "default",
  },
  paramKey: {
    type: String,
    default: "default",
  },
  showFlag: {
    type: Boolean,
    default: true,
  },
  jibie: {
    type: Boolean,
    default: true,
  },
  jibiecun: {
    type: Boolean,
    default: true,
  },
  dataType: {
    type: Number,
    default: 1, //1文本 2整数 3小数 4日期
  },
  maxLength: {
    //输入长度
    type: String,
    default: "100",
  },
  paramUnit: {
    type: String,
    default: "",
  },
  unitId: {
    type: String,
    default: "",
  },
  isFlag: {
    type: Boolean,
    default: false,
  },
  LZFS: {
    type: String,
    default: "",
  },
  objVal: {
    type: Object,
    default: {},
  },
  projectType: {
    type: Number,
    default: "",
  },
  childObj: {
    type: Object,
    default: {},
  },
  objVal2: {
    type: Object,
    default: {},
  },
  items: {
    type: Object,
    default: {},
  },
  contractFlags: {
        type: Boolean,
        default: false,
    },
    matterFlag: {
    type: Number,
    default: 0,
  },
});
const emits = defineEmits([
  "update:modelValue",
  "changeTree",
  "getTenderData",
  "changeYXQXQ",
  "changeYXQGZ",
  "changeFWFSQGZ",
  "changeLYSQGZ",
  "changeZJDJ",
  "changeShowZj2",
  "changeShowZj",
  "mapClick",
  "changeNcp",
  "changePriceUpper",
  "regPhone",
  "changeCertNo",
  "changeOrg",
]); //定义方法名    

let checkList = ref([]);
let dataList = ref([]); //乡镇
let dataList2 = ref([]); //村社区
const proForm = reactive({
    pageNo: 1,
    pageSize: 10,
    assetName:''
})
const page = reactive({
    total: '',
})
const dialog = ref(null);
let tenderData = ref([
  {
    id: "",
    code: "",//编号
    serialNo: "",//序号
    name: "",//名称
    matterArea: "",//交易面积/数量
    matterAreaUnit: "",//交易面积/数量单位
    assetId:"",
    matterPrice: "",//"预期价格（分）"
    matterPrice: "",//标的物类型
    type:""//标的物类型
  },
  {
    id: "",
    code: "",//编号
    serialNo: "",//序号
    name: "",//名称
    matterArea: "",//交易面积/数量
    matterAreaUnit: "",//交易面积/数量单位
    assetId:"",
    matterPrice: "",//"预期价格（分）"
    type:""//标的物类型
  },
]);

let tenderData2 = ref([])
// let matterArea=ref('')
let optionList1 = ref([])//标的物类型下拉
let optionList2 = ref([])//单位下拉
let matterArea=ref('')//标的物数量合计
let matterAreaUnit=ref('')//标的物数量合计单位
let lastSelection=ref([])
let cancelledIds=ref([])
const priceFormat = (value, int = 6) => {//输入框正则
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const priceFormat1 = (value, int = 9) => {//输入框正则
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const priceFormat2 = (value, int = 9) => {//输入框正则
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const indexMethod = (index) => {//序号
  if (index == 0) {
    return 1;
  } else {
    return index + 1;
  }
};
const tableRef = ref(null);

let addForm = (th) => {//新增
  if (th == 1) {
    //新增
    tenderData.value.push({
      id: "",
      code: "",//编号
      serialNo: "",//序号
      name: "",//名称
      matterArea: "",//交易面积/数量
      assetId:"",
      matterAreaUnit: "",//交易面积/数量单位
      matterPrice: "",//"预期价格（分）"
      type:""//标的物类型
    });
  } else {
    arrys.value=[];
    dialog.value.open();
    console.log(tenderData2.value)
    console.log(tenderData.value)
    setTimeout(function () {
    nextTick(() => {
      tenderData2.value.forEach((ele) => {
        if (tenderData.value.some((item) => item.assetId == ele.id)) {
          console.log(ele);
          tableRef.value.toggleRowSelection(ele, true);
        }
      });
    });
  }, 1000);
  }
};
let deleteTender = (rows, index) => {//删除
  // 当前标的物关联的权属和评估信息将一并删除，是否删除？
  ElMessageBox.confirm(
    '当前标的物关联的权属和评估信息将一并删除，是否删除？',
    {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }
  )
    .then(() => {
      if (tenderData.value.length == 1) {
        ElMessage.error('至少保留一条数据')
        return;
      }
      tenderData.value.forEach((item, i) => {
        if (index == i) {
          tenderData.value.splice(i, 1);
        }
      });
    })
    .catch(() => {
      
    })
 
};
const getProjectMatterList = () => {//获取标的物类型
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=BDWLX",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList1.value = response.data.data; 
        console.log(optionList1.value)
      } else {
      }
    })
    .catch(() => {});
};
const getUnit = () => {//获取标的物单位
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=BDWDW",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList2.value = response.data.data; 
      } else {
      }
    })
    .catch(() => {});
};
const getbdwList = () => {//获取资源列表
  $axios({
    method: "get",
    url: "/tradeAsset/listPage",
    data: {
            overPlusLeastAmount:0,
            assetName: proForm.assetName,
            pageSize: proForm.pageSize,
            pageNo: proForm.pageNo
        }
  })
    .then((response) => {
      if (response.data.code == 200) {
        tenderData2.value = response.data.data.records;
        proForm.pageNo = response.data.data.current;
        page.total = response.data.data.total
        setTimeout(function () {
          nextTick(() => {
            tenderData2.value.forEach((ele) => {
              if (tenderData.value.some((item) => item.assetId == ele.id)) {
                console.log(ele);
                tableRef.value.toggleRowSelection(ele, true);
              }
            });
          });
        }, 1000);
      } else {
      }
    })
    .catch(() => {});
};
const getChildren = (val) => {
  let params = {
    areaId: val,
    minLevel: "5",
    type: "1",
    useToken: "1",
  };
  $axios({
    method: "get",
    url: "/area/getAreaTree",
    data: params,
  })
    .then((response) => {
      if (response.data.code == 200) {
        dataList2.value = response.data.data;
      } else {
      }
    })
    .catch(() => {});
};
const toSearch=()=>{
  getbdwList()
}
const toSure = () => {
  tenderData.value.map(item=>{ 
    arrys.value = arrys.value.filter(item2 => item2.id != item.assetId);
})
console.log(arrys.value)
  arrys.value.forEach(item=>{
    let obj={};
    obj.id='';
    obj.code=item.assetCode;
    obj.assetId=item.id;
    obj.name=item.assetName;
    obj.matterArea=item.overplusAmount;
    obj.matterAreaUnit=item.unit;
    obj.matterPrice='';
    obj.type=item.assetType;
    // obj.type=item.type;
    console.log(obj,'---')
    tenderData.value.push(obj)
  })

  cancelledIds.value.map(item=>{ 
    tenderData.value = tenderData.value.filter(item2 => item2.assetId != item);
})
    // tenderData.value.forEach((item,i)=>{
    //   cancelledIds.value.forEach((item2,j)=>{
    //       if(item.code==item2){
    //         tenderData.value.splice(i,1)
    //       }
    //   })
    // })

  dialog.value.close();
 
};
const getTenderData = inject("getTenderData");
function handleCurrentChange(val) {
    proForm.pageNo = val
    tenderData2.value = []
    getbdwList()
}
let arrys = ref([]);
function handleInput(value){
  // console.log(value)
  // // 只允许在末尾输入点
  // const regex = /^.*[.]?$/;
  //     if (!regex.test(value)) {
  //       // 如果输入的不符合规则，则将值设置为上一个合法的值
  //      return value.substring(0, value.length - 1)
  //     }

}
function blur(e){
  console.log(tenderData.value,'tenderData.value')
  tenderData.value.forEach(item=>{
    if(item.matterArea!=''){
      item.matterArea=Number(item.matterArea).toFixed(4)
    // item.matterPrice=Number(item.matterPrice).toFixed(2)
    }
    if(item.matterPrice!=''){
      // item.matterArea=Number(item.matterArea).toFixed(4)
    item.matterPrice=Number(item.matterPrice).toFixed(2)
    }
  })
  // console.log(e.target.value)
  // let index = e.target.value.indexOf('.')
  // console.log(index)

}
// 选中选项触发
const handleSelectionChange = (selection) => {
  cancelledIds.value = lastSelection.value.filter(item => !selection.some(sel => sel.id === item.id)).map(item => item.id);
  lastSelection.value=selection
  arrys.value = selection;
};
function handleSizeChange(val) {
    proForm.pageSize = val
    tenderData2.value = []
    getbdwList()
}
watch(
  () => tenderData.value,
  (newValue, oldValue) => {
    let sum=0;
    for (let i=0;i<newValue.length;i++){
        sum+=Number(newValue[i].matterArea);
        // matterArea.value=sum
        matterArea.value=sum.toFixed(4)
    }
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
    getTenderData(newValue,matterArea.value,matterAreaUnit.value)
    }
    }
   
  },
  { deep: true, immediate: true }
);
watch(
  () => matterArea.value,
  (newValue, oldValue) => {
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
      getTenderData(tenderData.value,newValue,matterAreaUnit.value)
    }
    }
   
  },
  { deep: true, immediate: true }
);
watch(
  () => matterAreaUnit.value,
  (newValue, oldValue) => {
    if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
      if(props.contractFlags==false){
      getTenderData(tenderData.value,matterArea.value,newValue)
    }
    }
    
  },
  { deep: true, immediate: true }
);
watch(
  () => props.objVal2,
  (newValue, oldValue) => {
    if (
      props.objVal2.paramKey == "townId" ||
      props.objVal2.fieldKey == "townId"
    ) {
      nextTick(() => {
        asd();
      });
      //   if (props.objVal2.defaultValue && props.objVal2.paramKey == "townId") {
      console.log(
        props.objVal2.defaultValue,
        "props.objV2al22.defaultValue1222"
      );
      getChildren(props.objVal2.defaultValue);
    }
  },
  { deep: true, immediate: true }
);

const asd = () => {
  let params = {
    areaId: props.unitId,
    minLevel: "4",
    type: "1",
    useToken: "1",
  };
  $axios({
    method: "get",
    url: "/area/getAreaTree",
    data: params,
  })
    .then((response) => {
      if (response.data.code == 200) {
        dataList.value = response.data.data;
      } else {
      }
    })
    .catch(() => {});
};

watch(
  () => props.items.matterVo,
  (newValue, oldValue) => {
    if (props.objVal2.paramKey == "townId"&&props.items.matterFlag==1) {
      if(props.items.matterVo){
        matterArea.value = props.items.matterVo.matterArea
      matterAreaUnit.value = props.items.matterVo.matterAreaUnit
      tenderData.value = props.items.matterVo.tradeMatterVoList
      }
      
    }
    // if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){
    //   console.log(newValue,'newValuenewValue')

    // }
  },
  { deep: true, immediate: true }
);

const nodeName = ref(); //回显值
const nodeName2 = ref(); //回显值
let deptId = reactive({
  id: "",
});
const changeTree = inject("changeTree");
function handleNodeClick(node) {
  props.objVal2.fieldList[0].defaultValue = "";
  console.log(12312);
  getChildren(node.id);
  // changeTree(node.id, 2)
  // emits('changeTree', node.id, 2)
}
function handleNodeClick2(node) {
  console.log(props.objVal2);
  // emits('update:modelValue', node.id);//v-model方式
  // nodeName2.value = node.name;
}

const state = reactive({
  timeout: null,
  RWMC: "",
});

onMounted(() => {
  // getList();
  if (props.type == "9" || props.type == 9) {
    if (props.modelValue == null || props.modelValue == undefined) {
    } else {
      let str = props.modelValue;
      checkList.value = str.split(",");
    }
  }
  if (
    props.objVal2.paramKey == "townId" ||
    props.objVal2.fieldKey == "townId"
  ) {
    asd();
    //   if (props.objVal2.defaultValue && props.objVal2.paramKey == "townId") {
    console.log(props.objVal2.defaultValue, "props.objV2al22.defaultValue1222");
    getChildren(props.objVal2.defaultValue);
   
  }
  if(props.objVal2.paramKey == "townId"&&props.items.matterFlag==1){ 

    getUnit();
    getProjectMatterList();  
    getbdwList();
    if(props.items.matterVo){
      matterArea.value=props.items.matterVo.matterArea
    matterAreaUnit.value=props.items.matterVo.matterAreaUnit
    tenderData.value=props.items.matterVo.tradeMatterVoList
    }
   

    }
});
</script>

<style lang='scss' scoped>
.set-up-class {
  :deep(.left-box) {
    span::before {
      content: "*";
      color: red;
    }
  }
}
.butClick{
  pointer-events: none;
}
.set-class::before {
  content: "*";
  color: red;
}
:deep(.left-box) {
  text-align: right;
  min-width: 150px !important;
}
.zb :deep(.left-box) {
  text-align: right;
  width: 40px !important;
  min-width: 0px !important;
}
.zb :deep(.right-box) {
  width: 100% !important;
}
// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-autocomplete) {
  width: 100%;
}

:deep(.el-cascader) {
  width: 100%;
}
.nd-input-bdw :deep(.el-input__inner) {
 text-align: right;
}
.nd-input-box :deep(.el-input) {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
  width: 100%;
  // padding-left: 10px;
  padding-right: 10px;
}

:deep(.el-input.is-disabled) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>