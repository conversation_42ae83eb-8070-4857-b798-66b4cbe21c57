---
description: 
globs: 
alwaysApply: true
---
# 项目工程约束规范

## 技术栈约束

### 前端框架
- Vue 3 (使用 Composition API)
- Vite 作为构建工具
- Vue Router 4.x 进行路由管理
- Element Plus 作为 UI 组件库
- Vant 作为移动端 UI 组件库
- SCSS 用于样式预处理

### 浏览器兼容性
- 支持 Chrome 90+
- 支持 Edge 90+
- 支持 Firefox 88+
- 支持 Safari 14+
- 不兼容 IE 浏览器
- 支持微信小程序内置浏览器

## 项目结构约束

### 目录规范
- `/src/views/` 存放页面级组件，按功能模块划分子目录
- `/src/views2/` 存放扩展页面视图
- `/src/viewsWx/` 存放微信端页面视图
- `/src/components/` 存放通用组件
- `/src/components/business/` 存放业务组件
- `/src/assets/` 存放静态资源，按类型划分子目录
- `/src/router/` 存放路由配置
- `/src/http/` 存放 API 请求相关
- `/src/directive/` 存放自定义指令

### 文件命名
- Vue 组件文件使用 PascalCase，如 `UserDetail.vue`
- 工具类文件使用 camelCase，如 `formatUtils.js`
- 样式文件使用 kebab-case，如 `main-layout.scss`
- 图片资源使用 kebab-case，如 `header-logo.png`

## 代码质量约束

### 编码规范
- 避免使用标签选择器
- 样式添加scoped属性
- 不使用!important
- 代码禁止出现 TODO、FIXME 等标记
- 生产环境禁止使用 console.log

### 注释要求
- 所有组件必须有顶部注释，说明功能和使用方式
- 复杂业务逻辑必须添加注释
- 公共方法必须添加参数和返回值注释

## 业务约束

### 现有代码处理
- 现有系统代码是稳定运行的生产环境代码
- 对原功能有大的改动时必须先询问确认
- 尽量不入侵旧代码，通过封装新组件/hooks/方法实现功能
- 实现功能时保持与系统其他模块一致的风格

### 系统特性
- 根据不同城市类型(wx/hg)进行不同的业务逻辑处理
- 处理token逻辑时需兼容三资系统的嵌入场景

## 性能约束

### 首屏加载
- 首屏加载时间不超过3秒
- 路由组件使用懒加载
- 使用 preload/prefetch 优化关键资源

### 运行性能
- 避免不必要的组件重渲染
- 列表使用虚拟滚动
- 合理使用防抖和节流
- 避免内存泄漏



