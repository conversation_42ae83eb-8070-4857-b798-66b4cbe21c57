<template>
  <!-- 下拉单选框 -->
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"
    :assembly="assembly"
    :style="{ width: width }"
    :requires="requireFlag"
    :itemValue="inputValue"
    :saveOperate="saveOperate"
    v-if="
      (type == 5 && showFlag != false && paramKey == 'orgName') ||
      paramKey == 'userName'
    "
  >
    <nd-select
      v-if="paramKey == 'orgName'"
      @change="changeSelectOrg"
      value-key="id"
      :disabled="
        disabled ||
        (previewShow && dictType == 'YWLX') ||
        dictType == 'JYPZ' ||
        (previewShow && dictType == 'LZFS') ||
        showFlag == 'noEdit' ||
        (projectType == 1 && dictType == 'LZFS')
      "
      filterable
      collapse-tags
      :placeholder="disabled == true ? ' ' : placeholder"
      width="90%"
      v-model="inputValue"
      :teleported="true"
    >
      <el-option
        v-for="item in orgArry"
        :key="item.id"
        :label="item.deptName"
        :value="item"
      />
    </nd-select>
    <!-- :placeholder="disabled == true ? ' ' : placeholder" -->
    <nd-select
      v-if="paramKey == 'userName'"
      @change="changeSelectUser"
      :disabled="
        disabled ||
        (previewShow && dictType == 'YWLX') ||
        dictType == 'JYPZ' ||
        (previewShow && dictType == 'LZFS') ||
        showFlag == 'noEdit' ||
        (projectType == 1 && dictType == 'LZFS')
      "
      filterable
      collapse-tags
      :placeholder="
        !jsonData || jsonData.length == 0 ? '请先选择服务机构' : placeholder
      "
      width="90%"
      value-key="id"
      v-model="inputValue"
      :teleported="true"
    >
      <el-option
        v-for="item in jsonData"
        :key="item.id"
        :label="item.username"
        :value="item"
      />
    </nd-select>
  </nd-search-more-item>

  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"
    :assembly="assembly"
    :style="{ width: width }"
    :requires="requireFlag"
    :itemValue="inputValue"
    :saveOperate="saveOperate"
    v-if="
      (type == 5 &&
        showFlag != false &&
        paramKey != 'serviceFeeRule' &&
        paramKey != 'perFeeRule' &&
        paramKey != 'farmType' &&
        paramKey != 'rentIncRule' &&
        paramKey != 'orgName' &&
        paramKey != 'userName') ||
      (type == 5 &&
        showFlag != false &&
        paramKey != 'serviceFeeRule' &&
        paramKey != 'perFeeRule' &&
        paramKey != 'farmType' &&
        paramKey != 'rentIncRule' &&
        matterFlag == 2 &&
        paramKey != 'orgName' &&
        paramKey != 'userName')
    "
  >
    <!-- <nd-select @change="changeSelect" v-if="dictType=='JJFS' ||dictType=='LZFS'"
      :disabled="disabled || ((previewShow&&dictType == 'YWLX') || dictType == 'JYPZ' || (previewShow&&dictType == 'LZFS')) || (showFlag == 'noEdit') ||(projectType==1&&dictType=='LZFS')" filterable
      :placeholder="disabled==true?' ':placeholder"  width="90%" v-model="inputValue" >
      <el-option v-for="item in jsonData" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
    </nd-select> -->
    <nd-select
      @change="changeSelect"
      :disabled="
        disabled ||
        (previewShow && dictType == 'YWLX') ||
        dictType == 'JYPZ' ||
        (previewShow && dictType == 'LZFS') ||
        showFlag == 'noEdit' ||
        (projectType == 1 && dictType == 'LZFS')
      "
      filterable
      collapse-tags
      :placeholder="disabled == true ? ' ' : placeholder"
      width="90%"
      v-model="inputValue"
      :teleported="true"
    >
      <el-option
        v-for="item in dataArry"
        :key="item.dataKey"
        :label="item.dataValue"
        :value="item.dataKey"
      />
    </nd-select>
  </nd-search-more-item>
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"
    :assembly="assembly"
    :style="{ width: width }"
    :requires="requireFlag"
    :itemValue="inputValue"
    :saveOperate="saveOperate"
    v-if="(type == 19 && matterFlag == 1) || (type == 19 && matterFlag == 2)"
  >
    <nd-select
      @change="changeSelect2"
      collapse-tags
      multiple
      filterable
      :placeholder="disabled == true ? ' ' : placeholder"
      @click="selectClick"
      width="90%"
      v-model="selectList"
      :disabled="
        disabled ||
        (previewShow && dictType == 'YWLX') ||
        dictType == 'JYPZ' ||
        (previewShow && dictType == 'LZFS') ||
        showFlag == 'noEdit' ||
        (projectType == 1 && dictType == 'LZFS')
      "
    >
      <el-option
        v-for="item in dataArry2"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </nd-select>
  </nd-search-more-item>
  <!-- 自定义字段 -->
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"
    :assembly="assembly"
    :style="{ width: width }"
    v-if="type == 6 && objVal2.customFlag == 1"
  >
    <nd-select
      @change="changeSelect2"
      collapse-tags
      multiple
      filterable
      :disabled="disabled"
      :placeholder="disabled == true ? ' ' : placeholder"
      width="90%"
      v-model="selectList"
    >
      <el-option
        v-for="item in dataArry"
        :key="item.dataKey"
        :label="item.dataValue"
        :value="item.dataKey"
      />
    </nd-select>
  </nd-search-more-item>
  <!-- <div
    v-if="(dictType == 'PBFF'||dictType == 'JJFS') && matterFlag == 1 && bdwTypeShow"
    style="
      display: flex;
      align-items: center;
      height: 100%;
      width: 100%;
      flex-direction: column;margin-bottom: 15px
    "
  > -->
  <div
    v-if="dictType == 'JJFS' && matterFlag == 1 && bdwTypeShow"
    style="
      display: flex;
      align-items: center;
      height: 100%;
      width: 100%;
      flex-direction: column;
      margin-bottom: 15px;
    "
  >
    <!-- 标的物组件 -->
    <!-- <nd-search-more-item
    title="交易标的物" style="width:100%"
     width="100%"
  > -->
    <!-- <div
      style="
        width: 100%;
        font-size: 14px;
        color: #555555;
        padding-bottom: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      "
      class="set-up-class2"
    >
      <span>交易标的物</span>
      <div>
        <nd-button icon="Plus" @click="addForm()">选择标的</nd-button>
      </div>
    </div> -->
    <!-- <div  v-if="tenderData.length == 0" style="color:red;text-align:left;width:100%;font-size:12px;">交易标的物交易标的物不能为空</div> -->

    <div
      style="
        width: 100%;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px 10px;
        border: 1px solid #eeeeee;
        border-bottom: none;
      "
    >
      <div
        style="
          width: 100%;
          font-size: 14px;
          color: #555555;
          padding-bottom: 15px;
          display: flex;
          flex-direction: row;
          align-items: center;
        "
        class="set-up-class2"
      >
        <span>交易标的物</span>
        <div>
          <!-- <nd-button icon="Plus" @click="addForm(1)">新增标的物</nd-button> -->
          <!-- <nd-button icon="Plus" @click="addForm()">选择标的</nd-button> -->
          <i
            @click="addForm()"
            style="
              font-size: 14px;
              color: #0b8df1;
              cursor: pointer;
              font-style: normal;
              margin-left: 10px;
            "
            >选择标的</i
          >
        </div>
      </div>

      <div
        style="
          width: 100%;
          font-size: 14px;
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 10px 10px;
          border-bottom: none;
        "
      >
        <span style="margin-right: 10px">本次交易标的数量合计:</span>
        <nd-input
          style="width: 100px"
          v-if="tenderData.length == 0"
          disabled
          placeholder="自动计算"
          clearable
        ></nd-input>
        <nd-input
          v-else
          disabled
          placeholder="自动计算"
          v-model="tenderData.length"
          clearable
        ></nd-input>
        <span style="margin-left: 10px">个</span>
        <span style="margin-right: 10px; margin-left: 20px"
          >面积/数量单位:</span
        >
        <nd-select
          @change="changeSelect3"
          style="width: 100px"
          v-model="matterAreaUnit"
        >
          <el-option
            v-for="item in optionList2"
            :key="item.dataValue"
            :label="item.dataValue"
            :value="item.dataValue"
          />
        </nd-select>
        <span style="margin-right: 10px; margin-left: 20px"
          >交易面积/数量合计:</span
        >
        <nd-input
          placeholder="自动计算支持修改"
          :formatter="priceFormat2"
          :parser="priceFormat2"
          v-model="matterArea"
          clearable
        ></nd-input>
      </div>
      <!-- <nd-select @change="changeSelect3"
        style="margin-left: 10px; width: 100px"
        v-model="matterAreaUnit"
      >
        <el-option
          v-for="item in optionList2"
          :key="item.dataValue"
          :label="item.dataValue"
          :value="item.dataValue"
        />
      </nd-select> -->
    </div>
    <nd-table style="width: 100%" :data="pagedData">
      <!-- <nd-table
      style=" width: 100%; margin-bottom: 15px"  max-height="500"
      :data="tenderData"
    > -->
      <el-table-column
        align="center"
        :index="indexMethod"
        type="index"
        label="序号"
        width="80"
      />
      <el-table-column align="center" prop="description" label="* 标的物名称">
        <template #header>
          <span class="set-class">标的物名称</span>
        </template>
        <template #default="{ row }">
          <el-tooltip effect="light" :content="row.name">
            <nd-input
              style="width: 100%"
              maxLength="50"
              disabled
              placeholder="请输入"
              clearable
              v-model="row.name"
            ></nd-input>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="标的物编号">
        <template #header>
          <span>标的物编号</span>
        </template>
        <template #default="{ row }">
          <span style="color: #c0c4cc">{{ row.code || "无需填写" }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="标的物类型">
        <template #default="{ row }">
          <el-select v-model="row.type" disabled placeholder="请选择">
            <el-option
              v-for="item in optionList1"
              :key="item.dataValue"
              :label="item.dataValue"
              :value="item.dataValue"
            >
            </el-option>
          </el-select>
        </template>
        <!-- <template #default="{ row }">
          <nd-select @change="changeType" style="width: 100%" v-model="row.value3"> 
            <el-option v-for="item in optionList1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
          </nd-select>
        </template> -->
      </el-table-column>
      <el-table-column align="center" prop="description" label="*交易面积/数量">
        <template #header>
          <span class="set-class">交易面积/数量</span>
        </template>
        <template #default="{ row }">
          <nd-input
            class="nd-input-bdw"
            style="width: 100%"
            @blur="blur"
            @input="changeVal(row)"
            :formatter="priceFormat1"
            :parser="priceFormat1"
            placeholder="请输入"
            clearable
            v-model="row.matterArea"
          ></nd-input>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="*单位">
        <template #header>
          <span class="set-class">单位</span>
        </template>
        <template #default="{ row }">
          <el-select
            v-model="row.matterAreaUnit"
            disabled
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in optionList2"
              :key="item.dataValue"
              :label="item.dataValue"
              :value="item.dataValue"
            >
            </el-option>
          </el-select>
          <!-- <nd-select @change="changeUnit" style="width: 100%"  v-model="row.value5">
            <el-option v-for="item in optionList1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
          </nd-select> -->
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="交易底价">
        <!-- <el-table-column align="center" prop="description" label="预期价格（元）"> -->
        <template #header>
          <span>交易底价</span>
        </template>
        <template #default="{ row }">
          <nd-input
            style="width: 100%"
            :formatter="priceFormat"
            @blur="blur"
            :parser="priceFormat"
            placeholder="请输入"
            clearable
            v-model="row.matterPrice"
          ></nd-input>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="description" label="价格单位">
        <template #default="{ row }">
          <el-select
            v-model="row.matterPriceUnit"
            disabled
            placeholder="请选择"
          >
            <!-- <el-select v-model="row.matterPriceUnit" :disabled="disabled" placeholder="请选择" v-if="row.assetId==''||row.assetId==null" > -->
            <el-option
              v-for="item in optionList3"
              :key="item.dataKey"
              :label="item.dataValue"
              :value="item.dataKey"
            >
            </el-option>
          </el-select>
          <!-- <span v-else style="text-align:left;display:inline-block;width:100%;">{{ row.matterPriceUnit }}</span> -->
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        align="center"
        prop="status"
        width="80"
        label="操作"
      >
        <template #default="scoped">
          <span
            style="cursor: pointer; color: #0b8df1"
            @click.stop="deleteTender(scoped.row, scoped.$index, indexMethod)"
            >删除</span
          >
          <!-- <el-tooltip effect="light" content="删除">
            <el-button link @click.stop="deleteTender(scoped.row, scoped.$index)">
              <img src="@/assets/images/transactionManageImage/delete.png" alt="" />
            </el-button>
          </el-tooltip> -->
        </template>
      </el-table-column>
    </nd-table>

    <div
      class="pagination-box"
      style="
        width: 100%;
        padding: 10px 15px;
        background: #fff;
        border: 1px solid #eeeeee;
        border-top: none;
      "
      v-if="page.total2 >= 10"
    >
      <nd-pagination
        v-model:total="page.total2"
        v-model:current-page="proForm.pageNo2"
        v-model:page-size="proForm.pageSize2"
        @size-change="handleSizeChange2"
        @current-change="handleCurrentChange2"
      >
      </nd-pagination>
    </div>
    <!-- </nd-search-more-item> -->
    <!-- 标的物组件 -->
  </div>
  <ndDialog
    ref="dialog"
    height="50vh"
    title="选择标的"
    style="overflow: hidden; padding: 0px 10px"
  >
    <div
      style="
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      "
    >
      <ndButton
        @click="toSure()"
        style="width: 80px"
        type="primary"
        color="#0b8df1"
        >确认</ndButton
      >
    </div>
    <nd-table
      style="height: 360px; width: 100%; margin-bottom: 15px; margin-top: 10px"
      row-key="id"
      :data="tenderData2"
      @selection-change="handleSelectionChange"
      ref="tableRef"
    >
      <el-table-column type="selection" label="选择" width="55">
      </el-table-column>
      <el-table-column align="center" prop="name" label="标的物名称">
      </el-table-column>
      <el-table-column align="center" prop="code" label="标的物编号">
      </el-table-column>
      <el-table-column align="center" prop="type" label="标的物类型">
      </el-table-column>
      <el-table-column align="center" prop="matterArea" label="交易面积/数量">
      </el-table-column>
      <el-table-column align="center" prop="matterAreaUnit" label="单位">
      </el-table-column>
      <el-table-column align="center" prop="matterPrice" label="交易底价">
      </el-table-column>
      <el-table-column
        align="center"
        prop="matterPriceUnitName"
        label="价格单位"
      >
      </el-table-column>
    </nd-table>
    <!-- <template #footer>
      <ndButton
        @click="toProRecord"
        :icon="Check"
        type="primary"
        color="#0b8df1"
        >确定</ndButton
      >
    </template> -->
  </ndDialog>
  <!-- 下拉单选框 -->
</template>

<script setup>
// 导入公共组件
import ndSearchMoreItem from "@/components/business/ndbControlAdapter/controlItem.vue";
import ndInput from "@/components/ndInput.vue";
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndButton from "@/components/ndButton.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndPagination from "@/components/ndPagination.vue";
import { ElMessage, ElMessageBox } from "element-plus";

import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
const $axios = inject("$axios");
const currentInstance = getCurrentInstance();
const props = defineProps({
  type: {
    // 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
    type: Number,
    default: "default",
  },
  projectId: {
    //projectId
    type: String,
    default: "",
  },
  width: {
    //宽度
    type: String,
    default: "",
  },
  title: {
    //标题
    type: String,
    default: "",
  },
  disabled: {
    // 只读
    type: Boolean,
    default: false,
  },
  bdwTypeShow: {
    type: Boolean,
    default: false,
  },
  matterFlag: {
    type: Number || String,
    default: 0, //1是0否
  },
  requireFlag: {
    // 是否必填
    type: Number,
    default: 1, //1是0否
  },
  modelValue: {
    //双向绑定
    type: String,
  },
  jsonData: {
    type: Array,
    default: [],
  },
  dictType: {
    type: String,
    default: "default",
  },
  paramKey: {
    type: String,
    default: "default",
  },
  showFlag: {
    type: Boolean,
    default: true,
  },
  jibie: {
    type: Boolean,
    default: true,
  },
  jibiecun: {
    type: Boolean,
    default: true,
  },
  dataType: {
    type: Number,
    default: 1, //1文本 2整数 3小数 4日期
  },
  maxLength: {
    //输入长度
    type: String,
    default: "100",
  },
  paramUnit: {
    type: String,
    default: "",
  },
  unitId: {
    type: String,
    default: "",
  },
  isFlag: {
    type: Boolean,
    default: false,
  },
  LZFS: {
    type: String,
    default: "",
  },
  objVal: {
    type: Object,
    default: {},
  },
  objVal2: {
    type: Object,
    default: {},
  },
  projectType: {
    type: Number,
    default: "",
  },
  childObj: {
    type: Object,
    default: {},
  },
  placeholder: {
    type: String,
    default: "请输入",
  },
  assembly: {
    type: Boolean,
    default: false,
  },
  previewShow: {
    type: Boolean,
    default: true,
  },
  items: {
    type: Object,
    default: {},
  },
  saveOperate: {
    //保存触发
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits([
  "update:modelValue",
  "changeTree",
  "changeYXQXQ",
  "changeYXQGZ",
  "changeFWFSQGZ",
  "changeLYSQGZ",
  "changeZJDJ",
  "changeShowZj2",
  "changeShowZj",
  "mapClick",
  "changeNcp",
  "changePriceUpper",
  "regPhone",
  "changeCertNo",
  "changeOrg",
  "changexzrLx",
]); //定义方法名
const handleIconClick = (ev) => {
  // triggerValue.val=true;
};
const proForm = reactive({
  pageNo: 1,
  pageSize: 10,
  pageNo2: 1,
  pageSize2: 10,
});
const page = reactive({
  total: "",
  total2: "",
});
let selectList = ref([]);
let tenderData = ref([
  //   {
  //     id: "",
  //     code: "",//编号
  //     serialNo: "",//序号
  //     name: "",//名称
  //     matterArea: "",//交易面积/数量
  //     matterAreaUnit: "",//交易面积/数量单位
  //     matterPrice: "",//"预期价格（分）"
  //     matterPrice: "",//标的物类型
  //     type:""//标的物类型
  //   },
  //   {
  //     id: "",
  //     code: "",//编号
  //     serialNo: "",//序号
  //     name: "",//名称
  //     matterArea: "",//交易面积/数量
  //     matterAreaUnit: "",//交易面积/数量单位
  //     matterPrice: "",//"预期价格（分）"
  //     matterPrice: "",//标的物类型
  //     type:""//标的物类型
  //   },
]);
let tenderData2 = ref([]);
let pagedData = ref([]);

let tenderDataAll = ref([]);
// let matterArea=ref('')
let optionList1 = ref([]); //标的物类型下拉
let optionList2 = ref([]); //单位下拉
let optionList3 = ref([]); //单位下拉
let matterArea = ref(""); //标的物数量合计
let matterAreaUnit = ref(""); //标的物数量合计单位
let lastSelection = ref([]);
let cancelledIds = ref([]);
let arrys = ref([]);
let dataArry = ref([]);
let dataArry2 = ref([]);
let orgArry = ref([]);
let userArry = ref([]);
// 选中选项触发
const handleSelectionChange = (selection) => {
  cancelledIds.value = lastSelection.value
    .filter((item) => !selection.some((sel) => sel.id === item.id))
    .map((item) => item.id);
  lastSelection.value = selection;
  arrys.value = selection;
};
function blur(e) {
  page.total2 = tenderData.value.length;
  const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
  const endIndex = startIndex + proForm.pageSize2;
  pagedData.value = tenderData.value.slice(startIndex, endIndex);
  tenderData.value.forEach((item) => {
    item.matterArea = Number(item.matterArea).toFixed(4);
    if (item.matterPrice) {
      item.matterPrice = Number(item.matterPrice).toFixed(2);
    }
  });
  const sortedButtonList = tenderData.value.map(function (value, index) {
    if (matterAreaUnit.value == "平方米") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? value.matterArea
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 0.0015).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 10000).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "亩") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea * 0.0015).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? value.matterArea
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 15).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "公顷") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea / 10000).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 15).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? value.matterArea
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea: value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    }
  });
  console.log(sortedButtonList, "matterAreaUnit.value");
  let sum = 0;
  for (let i = 0; i < sortedButtonList.length; i++) {
    if (
      matterAreaUnit.value == "平方米" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "亩" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "公顷" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    // if (
    //   matterAreaUnit.value == "个" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "本" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "台" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "棵" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
    // matterArea.value=sum
    matterArea.value = sum.toFixed(4);
  }
  // let sum = 0;
  // for (let i = 0; i < tenderData.value.length; i++) {
  //   sum += Number(tenderData.value[i].matterArea);
  //   // matterArea.value = sum;
  //   // matterArea.value=parseFloat(sum.toFixed(2))
  //   matterArea.value = sum.toFixed(4);
  //   // matterArea.value=sum.toFixed(4)
  // }
  getTenderData2(tenderData.value, matterArea.value, matterAreaUnit.value);
}
const priceFormat = (value, int = 9) => {
  //输入框正则
  value = value.toString();
  // 先把非数字的都替换掉，除了数字和小数点
  value = value.replace(/[^\d.]/g, "");
  // 必须保证第一个为数字而不是小数点
  value = value.replace(/^\./g, "");
  // 保证只有出现一个小数点而没有多个小数点
  value = value.replace(/\.{2,}/g, ".");
  // 保证小数点只出现一次，而不能出现两次以上
  value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  // 保证只能输入4个小数
  value = value.replace(/^(\d+)\.(\d{0,2}).*$/, "$1.$2");
  // 只能8位整数
  let index = value.indexOf(".");
  if (index > -1) {
    value = value.slice(0, index < int ? index : int) + value.slice(index);
  } else {
    value = value.slice(0, int);
  }
  return value;
};
const priceFormat2 = (value, int = 9) => {
  //输入框正则
  value = value.toString();
  // 先把非数字的都替换掉，除了数字和小数点
  value = value.replace(/[^\d.]/g, "");
  // 必须保证第一个为数字而不是小数点
  value = value.replace(/^\./g, "");
  // 保证只有出现一个小数点而没有多个小数点
  value = value.replace(/\.{2,}/g, ".");
  // 保证小数点只出现一次，而不能出现两次以上
  value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  // 保证只能输入4个小数
  value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
  // 只能8位整数
  let index = value.indexOf(".");
  if (index > -1) {
    value = value.slice(0, index < int ? index : int) + value.slice(index);
  } else {
    value = value.slice(0, int);
  }
  return value;
};
const priceFormat1 = (value, int = 6) => {
  //输入框正则
  value = value.toString();
  // 先把非数字的都替换掉，除了数字和小数点
  value = value.replace(/[^\d.]/g, "");
  // 必须保证第一个为数字而不是小数点
  value = value.replace(/^\./g, "");
  // 保证只有出现一个小数点而没有多个小数点
  value = value.replace(/\.{2,}/g, ".");
  // 保证小数点只出现一次，而不能出现两次以上
  value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
  // 保证只能输入4个小数
  value = value.replace(/^(\d+)\.(\d{0,4}).*$/, "$1.$2");
  // 只能8位整数
  let index = value.indexOf(".");
  if (index > -1) {
    value = value.slice(0, index < int ? index : int) + value.slice(index);
  } else {
    value = value.slice(0, int);
  }
  return value;
};
// const indexMethod = (index) => {
//   //序号
//   if (index == 0) {
//     return 1;
//   } else {
//     return index + 1;
//   }
// };
const indexMethod = (index) => {
  //序号
  return (proForm.pageNo2 - 1) * proForm.pageSize2 + index + 1;
  // if (index == 0) {
  //   return 1;
  // } else {
  //   return index + 1;
  // }
};
let changeVal = (rows) => {
  tenderDataAll.value.forEach((item) => {
    if (item.id == rows.id) {
      tenderData.value.forEach((item2) => {
        if (
          item2.id == item.id &&
          Number(item2.matterArea) > Number(item.matterArea)
        ) {
          item2.matterArea = item.matterArea;
        }
      });
    }
  });
  page.total2 = tenderData.value.length;
  const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
  const endIndex = startIndex + proForm.pageSize2;
  pagedData.value = tenderData.value.slice(startIndex, endIndex);
  const sortedButtonList = tenderData.value.map(function (value, index) {
    if (matterAreaUnit.value == "平方米") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? value.matterArea
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 0.0015).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 10000).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "亩") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea * 0.0015).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? value.matterArea
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 15).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "公顷") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea / 10000).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 15).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? value.matterArea
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea: value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    }
  });
  console.log(sortedButtonList, "matterAreaUnit.value");
  let sum = 0;
  for (let i = 0; i < sortedButtonList.length; i++) {
    if (
      matterAreaUnit.value == "平方米" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "亩" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "公顷" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    // if (
    //   matterAreaUnit.value == "个" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "本" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "台" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
    // matterArea.value=sum
    matterArea.value = sum.toFixed(4);
  }
  // let sum = 0;
  // for (let i = 0; i < tenderData.value.length; i++) {
  //   sum += Number(tenderData.value[i].matterArea);
  //   matterArea.value = sum.toFixed(4);
  // }
  getTenderData2(tenderData.value, matterArea.value, matterAreaUnit.value);
};
const dialog = ref(null);
const tableRef = ref(null);
let bdwList = ref([]);
const getbdwList = () => {
  //获取标的物下拉列表
  $axios({
    method: "get",
    url: "/tradeMatter/getProjectMatterListXl?projectId=" + props.projectId,
    // url: "/tradeMatter/getProjectMatterList?projectId=" + props.projectId,
  })
    .then((response) => {
      if (response.data.code == 200) {
        tenderData2.value = JSON.parse(JSON.stringify(response.data.data));
        dataArry2.value = JSON.parse(JSON.stringify(response.data.data));
        tenderDataAll.value = JSON.parse(JSON.stringify(response.data.data));
      } else {
      }
    })
    .catch(() => {});
};
const toSure = () => {
  tenderData.value.map((item) => {
    arrys.value = arrys.value.filter((item2) => item2.id != item.id);
  });
  arrys.value.forEach((item) => {
    tenderData.value.push(item);
  });

  cancelledIds.value.map((item) => {
    tenderData.value = tenderData.value.filter((item2) => item2.id != item);
  });
  page.total2 = tenderData.value.length;
  const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
  const endIndex = startIndex + proForm.pageSize2;
  pagedData.value = tenderData.value.slice(startIndex, endIndex);
  dialog.value.close();
  const sortedButtonList = tenderData.value.map(function (value, index) {
    if (matterAreaUnit.value == "平方米") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? value.matterArea
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 0.0015).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 10000).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "亩") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea * 0.0015).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? value.matterArea
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 15).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "公顷") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea / 10000).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 15).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? value.matterArea
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea: value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    }
  });
  console.log(sortedButtonList, "matterAreaUnit.value");
  let sum = 0;
  for (let i = 0; i < sortedButtonList.length; i++) {
    if (
      matterAreaUnit.value == "平方米" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "亩" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "公顷" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    // if (
    //   matterAreaUnit.value == "个" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "本" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "台" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "棵" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
    // matterArea.value=sum
    matterArea.value = sum.toFixed(4);
  }
  // let sum = 0;
  // for (let i = 0; i < tenderData.value.length; i++) {
  //   sum += Number(tenderData.value[i].matterArea);
  //   // matterArea.value = sum;
  //   // matterArea.value=parseFloat(sum.toFixed(2))
  //   matterArea.value = sum.toFixed(4);
  //   // matterArea.value=sum.toFixed(4)
  // }
  getTenderData2(tenderData.value, matterArea.value, matterAreaUnit.value);
};
let addForm = () => {
  arrys.value = [];
  //选择
  getbdwList();
  dialog.value.open();
  setTimeout(function () {
    nextTick(() => {
      tenderData2.value.forEach((ele) => {
        if (tenderData.value.some((item) => item.id == ele.id)) {
          console.log(ele);
          tableRef.value.toggleRowSelection(ele, true);
        }
      });
    });
  }, 1000);
};
let deleteTender = (rows, index, th) => {
  // this.tableData.splice(index + (this.currentPage - 1) * this.pageSize, 1);
  //删除
  // if (tenderData.value.length == 1) {
  //   return;
  // }
  tenderData.value.forEach((item, i) => {
    if (index == i) {
      tenderData.value.splice(
        index + (proForm.pageNo2 - 1) * proForm.pageSize2,
        1
      );
      // tenderData.value.splice(i, 1);
    }
  });
  console.log(tenderData.value, "tenderData.value");
  console.log(tenderData.value.length, "tenderData.value");
  if (tenderData.value.length == 0) {
    matterArea.value = 0.0;
    page.total2 = tenderData.value.length;
    const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
    const endIndex = startIndex + proForm.pageSize2;
    pagedData.value = tenderData.value.slice(startIndex, endIndex);
  } else {
    const sortedButtonList = tenderData.value.map(function (value, index) {
      if (matterAreaUnit.value == "平方米") {
        return {
          assetId: value.assetId,
          code: value.code,
          id: value.id,
          matterArea:
            value.matterAreaUnit == "平方米"
              ? value.matterArea
              : value.matterAreaUnit == "亩"
              ? (value.matterArea / 0.0015).toFixed(4)
              : value.matterAreaUnit == "公顷"
              ? (value.matterArea * 10000).toFixed(4)
              : value.matterArea,
          matterAreaUnit: value.matterAreaUnit,
          matterPrice: value.matterPrice,
          name: value.name,
          projectId: value.projectId,
          serialNo: value.serialNo,
          type: value.type,
        };
      } else if (matterAreaUnit.value == "亩") {
        return {
          assetId: value.assetId,
          code: value.code,
          id: value.id,
          matterArea:
            value.matterAreaUnit == "平方米"
              ? (value.matterArea * 0.0015).toFixed(4)
              : value.matterAreaUnit == "亩"
              ? value.matterArea
              : value.matterAreaUnit == "公顷"
              ? (value.matterArea * 15).toFixed(4)
              : value.matterArea,
          matterAreaUnit: value.matterAreaUnit,
          matterPrice: value.matterPrice,
          name: value.name,
          projectId: value.projectId,
          serialNo: value.serialNo,
          type: value.type,
        };
      } else if (matterAreaUnit.value == "公顷") {
        return {
          assetId: value.assetId,
          code: value.code,
          id: value.id,
          matterArea:
            value.matterAreaUnit == "平方米"
              ? (value.matterArea / 10000).toFixed(4)
              : value.matterAreaUnit == "亩"
              ? (value.matterArea / 15).toFixed(4)
              : value.matterAreaUnit == "公顷"
              ? value.matterArea
              : value.matterArea,
          matterAreaUnit: value.matterAreaUnit,
          matterPrice: value.matterPrice,
          name: value.name,
          projectId: value.projectId,
          serialNo: value.serialNo,
          type: value.type,
        };
      } else {
        return {
          assetId: value.assetId,
          code: value.code,
          id: value.id,
          matterArea: value.matterArea,
          matterAreaUnit: value.matterAreaUnit,
          matterPrice: value.matterPrice,
          name: value.name,
          projectId: value.projectId,
          serialNo: value.serialNo,
          type: value.type,
        };
      }
    });
    console.log(sortedButtonList, "matterAreaUnit.value");
    let sum = 0;
    for (let i = 0; i < sortedButtonList.length; i++) {
      if (
        matterAreaUnit.value == "平方米" &&
        (sortedButtonList[i].matterAreaUnit == "平方米" ||
          sortedButtonList[i].matterAreaUnit == "亩" ||
          sortedButtonList[i].matterAreaUnit == "公顷")
      ) {
        sum += Number(sortedButtonList[i].matterArea);
      }
      if (
        matterAreaUnit.value == "亩" &&
        (sortedButtonList[i].matterAreaUnit == "平方米" ||
          sortedButtonList[i].matterAreaUnit == "亩" ||
          sortedButtonList[i].matterAreaUnit == "公顷")
      ) {
        sum += Number(sortedButtonList[i].matterArea);
      }
      if (
        matterAreaUnit.value == "公顷" &&
        (sortedButtonList[i].matterAreaUnit == "平方米" ||
          sortedButtonList[i].matterAreaUnit == "亩" ||
          sortedButtonList[i].matterAreaUnit == "公顷")
      ) {
        sum += Number(sortedButtonList[i].matterArea);
      }
    //   if (
    //     matterAreaUnit.value == "个" &&
    //     matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    //   ) {
    //     sum += Number(sortedButtonList[i].matterArea);
    //   }
    //   if (
    //     matterAreaUnit.value == "本" &&
    //     matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    //   ) {
    //     sum += Number(sortedButtonList[i].matterArea);
    //   }
    //   if (
    //     matterAreaUnit.value == "台" &&
    //     matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    //   ) {
    //     sum += Number(sortedButtonList[i].matterArea);
    //   }
    //   if (
    //   matterAreaUnit.value == "棵" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
      // matterArea.value=sum
      matterArea.value = sum.toFixed(4);
    }
    //   let sum = 0;
    // for (let i = 0; i < tenderData.value.length; i++) {
    //   sum += Number(tenderData.value[i].matterArea);
    //   // matterArea.value = sum;
    //   // matterArea.value=parseFloat(sum.toFixed(2))
    //   matterArea.value = sum.toFixed(4);
    //   // matterArea.value=sum.toFixed(4)
    // }
    page.total2 = tenderData.value.length;
    const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
    const endIndex = startIndex + proForm.pageSize2;
    pagedData.value = tenderData.value.slice(startIndex, endIndex);
    getTenderData2(tenderData.value, matterArea.value, matterAreaUnit.value);
  }
};
const getProjectMatterList = () => {
  //获取标的物类型
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=BDWLX",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList1.value = response.data.data;
        console.log(optionList1.value);
      } else {
      }
    })
    .catch(() => {});
};
const getUnit = () => {
  pagedData.value = [];
  //获取标的物单位
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=BDWDW",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList2.value = response.data.data;
      } else {
      }
    })
    .catch(() => {});
};
const getUnit2 = () => {
  //获取价格单位
  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=DJDW",
  })
    .then((response) => {
      if (response.data.code == 200) {
        optionList3.value = response.data.data;
      } else {
      }
    })
    .catch(() => {});
};
const inputValue = computed({
  //监听
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  },
});
let moneyUpper = ref("");
// const priceFormatTwo = (value, int = 6) => {
//     value = value.toString();
//     // 先把非数字的都替换掉，除了数字和小数点
//     value = value.replace(/[^\d.]/g, "");
//     // 必须保证第一个为数字而不是小数点
//     value = value.replace(/^\./g, "");
//     // 保证只有出现一个小数点而没有多个小数点
//     value = value.replace(/\.{2,}/g, ".");
//     // 保证小数点只出现一次，而不能出现两次以上
//     value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
//     // 保证只能输入4个小数
//     value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
//     // 只能8位整数
//     let index = value.indexOf('.')
//     if (index > -1) {
//         value = value.slice(0, index < int ? index : int) + value.slice(index)
//     } else {
//         value = value.slice(0, int)
//     }
//     return value
// }
// const priceFormat = (value, int = 8) => {
//     value = value.toString();
//     // 先把非数字的都替换掉，除了数字和小数点
//     value = value.replace(/[^\d.]/g, "");
//     // 必须保证第一个为数字而不是小数点
//     value = value.replace(/^\./g, "");
//     // 保证只有出现一个小数点而没有多个小数点
//     value = value.replace(/\.{2,}/g, ".");
//     // 保证小数点只出现一次，而不能出现两次以上
//     value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
//     // 保证只能输入4个小数
//     value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
//     // 只能8位整数
//     let index = value.indexOf('.')
//     if (index > -1) {
//         value = value.slice(0, index < int ? index : int) + value.slice(index)
//     } else {
//         value = value.slice(0, int)
//     }
//     return value
// }
// const priceFormat2 = (value) => {
//     value = value.toString();
//     value = value.replace(/[^\w\.\/]/ig, '');
//     return value

// }
const getTenderData2 = inject("getTenderData2");
function selectClick() {
  getbdwList();
}
// watch(
//   () => tenderData.value,
//   (newValue, oldValue) => {
//     if (props.dictType == 'JJFS' && props.matterFlag == 1){
//       console.log(newValue,'newValuenewValue')
//     }
//     let sum = 0;
//     for (let i = 0; i < newValue.length; i++) {
//       sum += Number(newValue[i].matterArea);
//       // matterArea.value = sum;
//       // matterArea.value=parseFloat(sum.toFixed(2))
//       matterArea.value=sum.toFixed(4)
//       // matterArea.value=sum.toFixed(4)

//     }
//     getTenderData2(newValue, matterArea.value, matterAreaUnit.value);

//   },
//   { deep: true, immediate: true }
// );
// watch(
//   () => tenderData.value.map(item => item.matterArea),
//   (newValue, oldValue) => {
//     if (props.dictType == 'JJFS' && props.matterFlag == 1){
//       console.log(newValue,'newValuenewValue')
//     }
//     let sum = 0;
//     for (let i = 0; i < tenderData.value.length; i++) {
//       sum += Number(tenderData.value[i].matterArea);
//       // matterArea.value = sum;
//       // matterArea.value=parseFloat(sum.toFixed(2))
//       matterArea.value=sum.toFixed(4)
//       // matterArea.value=sum.toFixed(4)

//     }
//     getTenderData2(newValue, matterArea.value, matterAreaUnit.value);

//   },
//   { deep: true, immediate: true }
// );
// 勿动
// watch(
//   () => matterArea.value,
//   (newValue, oldValue) => {
//     getTenderData2(tenderData.value, newValue, matterAreaUnit.value);
//   },
//   { deep: true, immediate: true }
// );
// watch(
//   () => matterAreaUnit.value,
//   (newValue, oldValue) => {
//     getTenderData2(tenderData.value, matterArea.value, newValue);
//   },
//   { deep: true, immediate: true }
// );
watch(
  () => props.type,
  (newValue, oldValue) => {
    if (props.type == 19) {
      getbdwList();
    }
  },
  { deep: true, immediate: true }
);
// const priceFormat3 = (val) => {
//     // val = val.replace(/^(([1-9]\d?)|100)/g, "");
//     if (val < 0) { // 如果输入的值小于0
//         return 0;
//     } else if (val >= 100) { // 如果输入的值大于等于100
//         return 100;
//     }
//     val = val.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
//     val = val.replace(/^\./g, ""); //验证第一个字符是数字而不是
//     val = val.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
//     val = val
//         .replace(".", "$#$")
//         .replace(/\./g, "")
//         .replace("$#$", ".");
//     switch (2) {
//         case 1:
//             val = val.replace(/^(\\-)*(\d+)\.(\d).*$/, "$1$2.$3"); //只能输入一个小数
//             break;
//         case 2:
//             val = val.replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
//             break;
//         case 3:
//             val = val.replace(/^(\\-)*(\d+)\.(\d\d\d).*$/, "$1$2.$3"); //只能输入三个小数
//             break;
//         case 4:
//             val = val.replace(/^(\\-)*(\d+)\.(\d\d\d\d).*$/, "$1$2.$3"); //只能输入四个小数
//             break;
//         default:
//             val = val.replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
//             break;
//     }
//     return val;

// }
function formatMoneyWith6digts2(value) {
  let obj = value.toString();
  if (String(obj).indexOf(".") > -1) {
    //判断有没有输入小数点
  } else {
    var p11 = /[^\d]/g;
    var p22 = /(\d{6})\d*$/g;
    var p44 = /(\d*)\6/g; //删除当我输入第七位的整数时候进行删除
    obj = obj.replace(p11, "").replace(p22, "$1").replace(p44, "$1$9");
  }
  var p1 = /[^\d\.]/g; // 过滤非数字及小数点 /g :所有范围中过滤
  var p2 = /(\.\d{4})\d*$/g;
  var p4 = /(\.)(\d*)\1/g;
  obj = obj.replace(p1, "").replace(p2, "$1").replace(p4, "$1$9");
  obj = obj.replace(/[^0-9.]/g, "");
  var p5 = /\.+/g; //多个点的话只取1个点，屏蔽1....234的情况
  obj = obj.replace(p5, ".");
  var p6 = /(\.+)(\d+)(\.+)/g; //屏蔽1....234.的情况
  obj = obj.replace(p6, "$1$2"); // 屏蔽最后一位的.
  props.modelValue = obj;
}
let checkList = ref([]);
let dataList = ref([]); //乡镇
let dataList2 = ref([]); //村社区
function handleCheckedCitiesChange(value) {
  checkList.value = checkList.value.filter((item) => item.trim() !== "");
  emits("update:modelValue", checkList.value.join(","));
  emits("changeYXQGZ", checkList.value.join(","));
}
function changeSelect2() {
  if (props.type == 19) {
    selectList.value = selectList.value.filter((item) => item.trim() !== "");
    emits("update:modelValue", selectList.value.join(","));
  } else if (props.type == 6 && props.objVal2.customFlag == 1) {
    selectList.value = selectList.value.filter((item) => item.trim() !== "");
    emits("update:modelValue", selectList.value.join(","));
  }
}
function changeSelect3() {
  const sortedButtonList = tenderData.value.map(function (value, index) {
    if (matterAreaUnit.value == "平方米") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? value.matterArea
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 0.0015).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 10000).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "亩") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea * 0.0015).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? value.matterArea
            : value.matterAreaUnit == "公顷"
            ? (value.matterArea * 15).toFixed(4)
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else if (matterAreaUnit.value == "公顷") {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea:
          value.matterAreaUnit == "平方米"
            ? (value.matterArea / 10000).toFixed(4)
            : value.matterAreaUnit == "亩"
            ? (value.matterArea / 15).toFixed(4)
            : value.matterAreaUnit == "公顷"
            ? value.matterArea
            : value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    } else {
      return {
        assetId: value.assetId,
        code: value.code,
        id: value.id,
        matterArea: value.matterArea,
        matterAreaUnit: value.matterAreaUnit,
        matterPrice: value.matterPrice,
        name: value.name,
        projectId: value.projectId,
        serialNo: value.serialNo,
        type: value.type,
      };
    }
  });
  console.log(sortedButtonList, "matterAreaUnit.value");
  let sum = 0;
  for (let i = 0; i < sortedButtonList.length; i++) {
    if (
      matterAreaUnit.value == "平方米" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "亩" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    if (
      matterAreaUnit.value == "公顷" &&
      (sortedButtonList[i].matterAreaUnit == "平方米" ||
        sortedButtonList[i].matterAreaUnit == "亩" ||
        sortedButtonList[i].matterAreaUnit == "公顷")
    ) {
      sum += Number(sortedButtonList[i].matterArea);
    }
    // if (
    //   matterAreaUnit.value == "个" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "本" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    // if (
    //   matterAreaUnit.value == "台" &&
    //   matterAreaUnit.value == sortedButtonList[i].matterAreaUnit
    // ) {
    //   sum += Number(sortedButtonList[i].matterArea);
    // }
    if((matterAreaUnit.value!='平方米'&&matterAreaUnit.value!='亩'&&matterAreaUnit.value!='公顷')&&matterAreaUnit.value==sortedButtonList[i].matterAreaUnit){
            sum+=Number(sortedButtonList[i].matterArea);
            }
    // matterArea.value=sum
    matterArea.value = sum.toFixed(4);
  }
  getTenderData2(tenderData.value, matterArea.value, matterAreaUnit.value);
}
/** 懒加载获取树形结构*/
function getList() {
  if (props.paramKey == "townId") {
    let params = {
      areaId: "3781d3ca21cb11ec8850f48e38bf4326",
      minLevel: "4",
      type: "1",
      useToken: "1",
    };
    $axios({
      method: "get",
      url: "/area/getAreaTree",
      data: params,
    })
      .then((response) => {
        if (response.data.code == 200) {
          response.data.data[0].children.forEach((element) => {
            element.children = [];
          });
          dataList.value = response.data.data[0].children;
        } else {
        }
      })
      .catch(() => {});
  }
}
const mapClick = () => {
  if (
    props.paramKey == "addressLocation" ||
    props.paramKey == "storeLocation"
  ) {
    emits("mapClick");
  }
};
const nodeName = ref(); //回显值
const nodeName2 = ref(); //回显值
let deptId = reactive({
  id: "",
});
function handleNodeClick(node) {
  emits("changeTree", node.id, 2);
}
function handleNodeClick2(node) {
  // emits('update:modelValue', node.id);//v-model方式
  // nodeName2.value = node.name;
}
function handleChange(e) {
  emits("changeNcp", e);
}
const changexzrLx = inject("changexzrLx");

function changeRadio(e) {
  if (props.dictType == "YXQXQ") {
    emits("changeYXQXQ", e, props.objVal);
  }
  if (props.dictType == "FWFSQGZ") {
    emits("changeFWFSQGZ", e, props.objVal);
  }
  if (props.dictType == "LYSQGZ") {
    emits("changeLYSQGZ", e, props.objVal);
  }
  if (props.dictType == "ZJDJ") {
    emits("changeZJDJ", e, props.objVal);
  }
}
function changeInput(e) {
  if (props.paramKey == "totalTransationPrice") {
    if (e.target) {
      emits("changePriceUpper", e.target.value);
    }
  }
  if (props.dictType == "telephone") {
    emits("regPhone", e, props.objVal);
  }
}
function changeInput2(e) {
  // 勿删
  if (props.paramKey == "totalTransationPrice") {
    if (e.target) {
      emits("changePriceUpper", e.target.value);
    }
  }
}
function changeMoney(e) {
  toChies(e);
}
// 大写数字过滤器
function toChies(amount) {
  // 汉字的数字
  const cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // 基本单位
  const cnIntRadice = ["", "拾", "佰", "仟"];
  // 对应整数部分扩展单位
  const cnIntUnits = ["", "万", "亿", "兆"];
  // 对应小数部分单位
  const cnDecUnits = ["角", "分", "厘", "毫"];
  // 整数金额时后面跟的字符
  const cnInteger = "整";
  // 整型完以后的单位
  const cnIntLast = "元";
  // 最大处理的数字
  const maxNum = 999999999999999.99;
  // 金额整数部分
  let integerNum;
  // 金额小数部分
  let decimalNum;
  // 输出的中文金额字符串
  let chineseStr = "";
  // 分离金额后用的数组，预定义
  let parts;
  if (amount === "") {
    return "";
  }
  amount = parseFloat(amount);
  if (amount >= maxNum) {
    // 超出最大处理数字
    return "";
  }
  if (amount === 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  // 转换为字符串
  amount = amount.toString();
  if (amount.indexOf(".") === -1) {
    integerNum = amount;

    decimalNum = "";
  } else {
    parts = amount.split(".");
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        // 归零
        zeroCount = 0;
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  // 小数部分
  if (decimalNum !== "") {
    const decLen = decimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1);
      if (n !== "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr === "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === "") {
    chineseStr += cnInteger;
  }
  moneyUpper.value = chineseStr;
  return chineseStr;
}
function handleSizeChange2(val) {
  proForm.pageSize2 = val;
  handleCurrentChange2(1);
  // tenderData2.value = []
  // getbdwList()
}
function handleCurrentChange2(val) {
  proForm.pageNo2 = val;
  const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
  const endIndex = startIndex + proForm.pageSize2;
  pagedData.value = tenderData.value.slice(startIndex, endIndex);

  // tenderData2.value = []
  // getbdwList()
}
const state = reactive({
  timeout: null,
  RWMC: "",
});
const handleSelect = (item) => {
  emits("changeCertNo", item);
  // console.log(item)
};
const handleSelect2 = (item) => {
  emits("changeOrg", item.deptCode);
};
const querySearchAsync2 = async (queryString, cb) => {
  $axios({
    url: "/transferor/getDept/" + props.unitId,
    method: "get",
  }).then((res) => {
    if (res.data.code === 200) {
      const restaurants = res.data.data;
      cb(restaurants);
    } else {
      ElMessage.error(res.data.msg);
    }
  });
};
//获取输入建议的方法,回调函数返回
const querySearchAsync = async (queryString, cb) => {
  clearTimeout(state.timeout);
  var results = [];
  if (queryString == "") {
    cb(results);
  } else {
    //掉接口需要的参数
    // let find = {
    //   name: queryString, //上面输入框绑定的数据
    // };
    try {
      let result = [];
      $axios({
        method: "get",
        url: "/tenders/getHyUser/" + queryString,
      })
        .then((response) => {
          if (response.data.code == 200) {
            result = response.data.data.data;
            if (result) {
              //循环放到一个远程搜索需要的数组
              for (let i = 0; i < result.length; i++) {
                const element = result[i];
                results.push({
                  // value: element.retenant +element.reletCertNo,
                  value: element.retenant,
                  link: element.reletCertNo,
                  id: element.id,
                  reletCertNo: element.reletCertNo,
                  reletCertType: element.reletCertType,
                });
              }
              cb(results);
            } else {
              results = [];
              cb(results);
            }
          } else {
            ElMessage.error(response.data.msg);
            results = [];
            cb(results);
          }
        })
        .catch(() => {});
    } catch (error) {
      console.log(error);
    }
  }
};
const changeAddType = inject("changeAddType");
const changeZJLX = inject("changeZJLX");
let bidValue = ref("");
function changeSelect(e) {
  if (props.dictType == "ZJLX") {
    changeZJLX(e, props.objVal);
  }
  if (props.dictType == "RENTINCTYPE") {
    changeAddType(e, props.objVal);
  }
  if (props.dictType == "LZFS" || props.dictType == "YWLX") {
    emits("changeShowZj", e, props.dictType);
  }
  if (props.paramKey == "yearNum" || props.paramKey == "monthNum") {
    emits("changeShowZj2", e, props.objVal);
  }
  if (
    props.paramKey == "reletType" ||
    props.paramKey == "transferorType" ||
    props.paramKey == "traderType" ||
    props.paramKey == "mortgagerType" ||
    props.paramKey == "bidType"
  ) {
    changexzrLx(e, props.objVal, props.paramKey, 1);
    bidValue.value = e;
    // selectFun2(e)
    // 11合理低价法
    // 12竞争性磋商
    // 13竞争性谈判
    // 14询价
    // if(e!='1'){
    //     console.log(666)
    //     $axios({
    //     method: "get",
    //     url: "/basecode/getBaseCodeInfo?baseType="+props.dictType
    // })
    //     .then((response) => {
    //         if (response.data.code == 200) {
    //             if(props.paramKey=='certType' ||props.paramKey=='accountCertType' ||props.paramKey=='traderCertType'){
    //                 response.data.data.forEach((item,index)=>{
    //                      if(item.dataKey=='0'){
    //                         response.data.data.splice(index, 1);
    //                      }
    //                 })
    //             }
    //             dataArry.value=response.data.data

    //         }
    //     })
    //     .catch(() => {

    //     });
    // }
  }
}
const selectFun = () => {
  // if (!props.dictType) return;
  if (!props.dictType || props.dictType == "PBFF") return;

  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=" + props.dictType,
  })
    .then((response) => {
      if (response.data.code == 200) {
        if (
          props.dictType == "ZJLX" &&
          props.paramKey != "certType" &&
          props.paramKey != "accountCertType" &&
          props.paramKey != "traderCertType" &&
          props.paramKey != "outflowCertType1" &&
          props.paramKey != "inflowCertType" &&
          props.paramKey != "outflowCertType2" &&
          props.paramKey != "outflowCertType3" &&
          props.paramKey != "outflowCertType4" &&
          props.paramKey != "outflowCertType5" &&
          props.paramKey != "mortgagerCertType"
        ) {
          // response.data.data.forEach((item, index) => {
          //   if (item.dataKey == "0") {
          //     response.data.data.splice(index, 1);
          //   }
          // });
        }
        dataArry.value = response.data.data;
        console.log(props.items);

        if (props.dictType == "JJFS") {
        }
        // if(props.dictType == "PBFF"){
        //   alert(bidType.value)
        // }
      }
    })
    .catch(() => {});
};
const selectFun2 = (val) => {
  if (props.dictType == "PBFF") {
    $axios({
      method: "get",
      url: "/basecode/getBaseCodeInfo?baseType=" + props.dictType,
    })
      .then((response) => {
        if (response.data.code == 200) {
          let bidTypeArry = [];
          response.data.data.forEach((item) => {
            item.dataValue4 = item.dataValue4.split(",");
            if (item.dataValue4.includes(val)) {
              bidTypeArry.push(item);
              dataArry.value = bidTypeArry;
            }
          });
          // dataArry.value = response.data.data;
          // if(props.dictType == "PBFF"){
          //   // alert(bidType.value)
          // }
        }
      })
      .catch(() => {});
  }
};
watch(
  () => props,
  (newValue, oldValue) => {
    if(props.dictType == "PBFF"){
      if (props.objVal.templateModleParam) {
      props.objVal.templateModleParam[0].templateParamVoList[0].templateParam.forEach(
        (item) => {
          if (item.dictType == "JJFS") {
            selectFun2(item.defaultValue);
          }
        }
      );
    }
    }
    

    // if (props.dictType == "JJFS") {
    //   bidType.value=props.objVal2.defaultValue
    //   console.log(props.objVal2.defaultValue)
    //   console.log( dataArry.value)
    // }
    // if (props.dictType == "PBFF") {
    //   alert(bidType.value)
    //   console.log( dataArry.value)
    // }
  },
  { deep: true, immediate: true }
);
const getOrgFun = () => {
  // if (!props.dictType) return;
  let type;
  if (localStorage.getItem("deptType") == 11) {
    type = 1;
  } else {
    type = 2;
  }
  $axios({
    method: "post",
    url: "/tradeIntermediaryOrgan/getAutoDeptList?type=" + type,
    // data:{
    //       type:2,
    //       orgName:'',
    //     },
  })
    .then((response) => {
      if (response.data.code == 200) {
        if (localStorage.getItem("deptType") == 11) {
          orgArry.value = response.data.data;
          //       if(props.objVal2.paramKey=='userName'){
          // props.objVal2.defaultValue=localStorage.getItem("userName")
          // }
          // props.objVal.templateParam.forEach(item=>{
          //           if(item.paramKey=='userPhone'){
          //             item.defaultValue=localStorage.getItem("userName")
          //           }
          //         })
          changeSelectOrg(response.data.data[0]);
        } else {
          console.log(props.objVal);
          console.log(props.objVal2);
          orgArry.value = response.data.data;
          $axios({
            method: "post",
            url:
              "/tradeIntermediaryOrgan/getDeptUser?orgId=" + props.objVal.orgId,
          })
            .then((response) => {
              if (response.data.code == 200) {
                nextTick(() => {
                  props.objVal.templateParam.forEach((item) => {
                    if (item.paramKey == "userName") {
                      item.optionList = response.data.data;
                    }
                  });
                });
              }
            })
            .catch(() => {});
        }
      }
    })
    .catch(() => {});
};
let changeSelectUser = (e) => {
  if (props.objVal2.paramKey == "userName") {
    props.objVal2.defaultValue = e.username;
  }
  props.objVal.templateParam.forEach((item) => {
    if (item.paramKey == "userPhone") {
      item.defaultValue = e.tel;
    }
  });
  console.log(e);
  console.log(props.objVal);
  console.log(props.objVal2);
};
let changeSelectOrg = (e) => {
  if (props.objVal2.paramKey == "orgName") {
    props.objVal2.defaultValue = e.deptName;
    props.objVal2.optionSetValue = e.id;
  }
  props.objVal.templateParam.forEach((item) => {
    if (item.paramKey == "orgCode") {
      item.defaultValue = e.deptCode;
    }
  });

  $axios({
    method: "post",
    url: "/tradeIntermediaryOrgan/getDeptUser?orgId=" + e.id,
  })
    .then((response) => {
      if (response.data.code == 200) {
        nextTick(() => {
          if (localStorage.getItem("deptType") == 11) {
            props.objVal.templateParam.forEach((item) => {
              if (item.paramKey == "userName") {
                item.defaultValue = localStorage.getItem("userName");
                item.showFlag = "noEdit";
              }
              if (item.paramKey == "userPhone") {
                item.defaultValue = localStorage.getItem("userPhone");
              }
            });
          } else {
            props.objVal.templateParam.forEach((item) => {
              if (item.paramKey == "userName") {
                item.defaultValue = "";
                item.optionList = response.data.data;
              }
              if (item.paramKey == "userPhone") {
                item.defaultValue = "";
              }
            });
          }
        });
      }
    })
    .catch(() => {});
};
const selectFunTwo = () => {
  if (!props.dictType) return;

  $axios({
    method: "get",
    url: "/basecode/getBaseCodeInfo?baseType=" + props.dictType,
  })
    .then((response) => {
      if (response.data.code == 200) {
        if (true) {
          response.data.data.forEach((item, index) => {
            if (item.dataKey == "0") {
              response.data.data.splice(index, 1);
            }
          });
        }
        dataArry.value = response.data.data;
      }
    })
    .catch(() => {});
};
// watch(() => props.objVal.optionSet, (newValue, oldValue) => {
//     if(props.objVal.enumType !== 0 && props.objVal.optionSet){
//         if (Array.isArray(props.objVal.optionSet)){
//             dataArry.value=props.objVal.optionSet
//         }else{
//             dataArry.value=JSON.parse(props.objVal.optionSet);
//         }
//     }else{
//         selectFun();
//     }
// }, { deep: true, immediate: true },)
watch(
  () => [props.objVal2.optionSet, props.paramKey, props.objVal2],
  (newValue, oldValue) => {
    if (props.objVal2.enumType !== 0 && props.objVal2.optionSet) {
      if (Array.isArray(props.objVal2.optionSet)) {
        dataArry.value = props.objVal2.optionSet;
      } else {
        console.log(props.objVal2.optionSet);

        dataArry.value = JSON.parse(props.objVal2.optionSet);
      }
    } else {
      if (props.dictType && props.type == 5) {
        selectFun();
      }
      // 复选框
      if (props.dictType && props.type == 6) {
        selectFunTwo();
      }
    }
    if (
      props.paramKey == "reletType" ||
      props.paramKey == "traderType" ||
      props.paramKey == "transferorType"
    ) {
      console.log(props.objVal2, "llll");
      if (
        props.objVal2.defaultValue !== "" &&
        props.objVal2.defaultValue !== null
      ) {
        changexzrLx(
          props.objVal2.defaultValue,
          props.objVal,
          props.paramKey,
          2
        );
      }
    }

    if (props.dictType == "BMRLX" && props.type == 5 && !props.modelValue) {
      emits("update:modelValue", "1");
      changexzrLx("1", props.objVal, props.paramKey, 1);
    }
  },
  { deep: true, immediate: true }
);
watch(
  // () => props.items.matterVo,
  () => props.items,
  (newValue, oldValue) => {
    if (props.dictType == "JJFS" && props.matterFlag == 1) {
      if (props.items.matterVo) {
        matterArea.value = JSON.parse(
          JSON.stringify(props.items.matterVo.matterArea)
        );
        matterAreaUnit.value = JSON.parse(
          JSON.stringify(props.items.matterVo.matterAreaUnit)
        );
        tenderData.value = JSON.parse(
          JSON.stringify(props.items.matterVo.tradeMatterVoList)
        );
        page.total2 = tenderData.value.length;
        const startIndex = (proForm.pageNo2 - 1) * proForm.pageSize2;
        const endIndex = startIndex + proForm.pageSize2;
        pagedData.value = tenderData.value.slice(startIndex, endIndex);
        handleCurrentChange2(1);
      }
    }
    // if (props.type == 19 && props.matterFlag == 1) {
    //   console.log('6666666')
    // }
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  if (props.paramKey == "orgName") {
    getOrgFun();
  }
  if (props.type == 19) {
    console.log(props.items, "props.itemsprops.items");
    console.log(props.modelValue, "props.modelValue");
    if (props.modelValue == null || props.modelValue == undefined) {
      selectList.value = [];
    } else {
      let str = props.modelValue || [];
      selectList.value = str.split(",");
    }
    getbdwList();
  }
  if (props.type == 6 && props.objVal2.customFlag == 1) {
    if (
      props.modelValue == null ||
      props.modelValue == undefined ||
      props.modelValue == ""
    ) {
      selectList.value = [];
    } else {
      let str = props.modelValue || [];
      selectList.value = str.split(",");
    }
  }
  if (props.dictType == "JJFS" && props.matterFlag == 1) {
    getUnit();
    getUnit2();
    getProjectMatterList();
    if (props.items.matterVo) {
      // matterArea.value = props.items.matterVo.matterArea
      // matterAreaUnit.value = props.items.matterVo.matterAreaUnit
      // tenderData.value = props.items.matterVo.tradeMatterVoList
      matterArea.value = JSON.parse(
        JSON.stringify(props.items.matterVo.matterArea)
      );
      matterAreaUnit.value = JSON.parse(
        JSON.stringify(props.items.matterVo.matterAreaUnit)
      );
      tenderData.value = JSON.parse(
        JSON.stringify(props.items.matterVo.tradeMatterVoList)
      );
      page.total2 = tenderData.value.length;
      handleCurrentChange2(1);
    }
  }

  if (props.dictType == "BMRLX" && props.type == 5 && !props.modelValue) {
    emits("update:modelValue", "1");
    changexzrLx("1", props.objVal, props.paramKey, 1);
    // changexzrLx('1', props.objVal, props.paramKey, 1);
    // changexzrLx(
    //       props.objVal2.defaultValue,
    //       props.objVal,
    //       props.paramKey,
    //       2
    //     );
  }
  if (props.dictType == "ZJLX" && props.type == 5 && !props.modelValue) {
    emits("update:modelValue", "1");
    // changexzrLx(
    //   '1',
    //   props.objVal,
    //   props.paramKey,
    //   2
    // );
    // changexzrLx(
    //   '1',
    //   props.objVal,
    //   props.paramKey,
    //   2
    // );
  }
});
defineExpose({
  selectFun2,
});
</script>

<style lang='scss' scoped>
.set-up-class {
  :deep(.left-box) {
    span::before {
      content: "*";
      color: red;
    }
  }
}
.set-up-class2 {
  span::before {
    content: "*";
    color: red;
  }
}
:deep(.left-box) {
  text-align: right;
  min-width: 150px !important;
}

// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-autocomplete) {
  width: 100%;
}

:deep(.el-cascader) {
  width: 100%;
}

.nd-input-box :deep(.el-input) {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.nd-input-bdw :deep(.el-input__inner) {
  text-align: right;
}
.nd-input-box :deep(.el-input__wrapper) {
  width: 100%;
  // padding-left: 10px;
  padding-right: 10px;
}

:deep(.el-input.is-disabled) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
:deep(.el-input.is-disabled .el-input__wrapper) {
  background: #f9f9f9;
}
</style>