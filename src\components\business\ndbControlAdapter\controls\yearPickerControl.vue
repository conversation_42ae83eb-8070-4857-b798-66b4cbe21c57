<template>
   <!-- 年月 -->
  <nd-search-more-item :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate" :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
    v-if="type == 13 && (showFlag != false) && paramKey != 'serviceFeeRule' && paramKey != 'perFeeRule' && paramKey != 'farmType' && paramKey != 'rentIncRule'">
    <nd-select @change="changeSelect"
      :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ') || (showFlag == 'noEdit') ||(projectType==1&&dictType=='LZFS')" filterable
      :placeholder="disabled==true?' ':placeholder"  width="90%" v-model="inputValue" >
      <el-option v-for="item in dataArry1" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
    </nd-select>
    <span style="margin:0 5px;font-size:14px;color:#555555;">年</span>
    <nd-select @change="changeSelect"
      :disabled="disabled || (dictType == 'YWLX' || dictType == 'JYPZ') || (showFlag == 'noEdit') ||(projectType==1&&dictType=='LZFS')" filterable
      :placeholder="disabled==true?' ':placeholder"  width="90%" v-model="inputValue2" >
      <el-option v-for="item in dataArry2" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
    </nd-select>
    <span style="margin:0 5px;font-size:14px;color:#555555;">月</span>
  </nd-search-more-item>
  <!-- 年月 -->
</template>
  
<script setup>
// 导入公共组件
import ndSelect from "@/components/ndSelect.vue";
import ndSearchMoreItem from '@/components/business/ndbControlAdapter/controlItem.vue';
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";

import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref, inject, watch, getCurrentInstance, nextTick, computed } from 'vue';
const $axios = inject("$axios");
const currentInstance = getCurrentInstance()
const props = defineProps({
    type: {// 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
        type: Number,
        default: "default",
    },
    width: {//宽度
        type: String,
        default: "",
    },
    title: {//标题
        type: String,
        default: "",
    },
    disabled: { // 只读
        type: Boolean,
        default: false,
    },
    requireFlag: { // 是否必填
        type: Number,
        default: 1,//1是0否
    },
    modelValue: {//双向绑定
        type: String,
    },
    modelValue2: {//双向绑定
    type: String,
  },
    jsonData: {
        type: Array,
        default: [],
    },
    dictType: {
        type: String,
        default: "default",
    },
    paramKey: {
        type: String,
        default: "default",
    },
    showFlag: {
        type: Boolean,
        default: true,
    },
    jibie: {
        type: Boolean,
        default: true,
    },
    jibiecun: {
        type: Boolean,
        default: true,
    },
    dataType: {
        type: Number,
        default: 1,//1文本 2整数 3小数 4日期
    },
    maxLength: {//输入长度
        type: String,
        default: '100',
    },
    paramUnit: {
        type: String,
        default: '',
    },
    unitId: {
        type: String,
        default: '',
    },
    isFlag: {
        type: Boolean,
        default: false,
    },
    LZFS: {
        type: String,
        default: '',
    }, objVal: {
        type: Object,
        default: {},
    }, projectType: {
        type: Number,
        default: '',
    }, childObj: {
        type: Object,
        default: {},
    },
    placeholder: {
    type: String,
    default: "请输入",
  },
  saveOperate: {//保存触发
      type: Boolean,
      default: false,
    },
})
const emits = defineEmits(["update:modelValue", "update:modelValue2","changeTree", "changeYXQXQ", "changeYXQGZ", "changeFWFSQGZ", "changeLYSQGZ", "changeZJDJ", "changeShowZj2", "changeShowZj", "mapClick", "changeNcp", 'changePriceUpper', "regPhone", "changeCertNo", "changeOrg"])//定义方法名
const handleIconClick = (ev) => {
    // triggerValue.val=true;
}
let dataArry1 = ref(Array.from({ length: 71 }, (_, i) => ({
  dataKey: i,
  dataValue: i
})));
let dataArry2 = ref(Array.from({ length: 12 }, (_, i) => ({
  dataKey: i,
  dataValue: i
})));
const inputValue = computed({//监听
    get() {
        return props.modelValue
    },
    set(value) {
        emits("update:modelValue", value)
    }
})
const inputValue2 = computed({//监听
    get() {
        return props.modelValue2
    },
    set(value) {
        emits("update:modelValue2", value)
    }
})
let moneyUpper = ref('')
const priceFormatTwo = (value, int = 6) => {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const priceFormat = (value, int = 8) => {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
const priceFormat2 = (value) => {
    value = value.toString();
    value = value.replace(/[^\w\.\/]/ig, '');
    return value

}

const priceFormat3 = (val) => {
    // val = val.replace(/^(([1-9]\d?)|100)/g, "");
    if (val < 0) { // 如果输入的值小于0
        return 0;
    } else if (val >= 100) { // 如果输入的值大于等于100
        return 100;
    }
    val = val.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
    val = val.replace(/^\./g, ""); //验证第一个字符是数字而不是
    val = val.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
    val = val
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".");
    switch (2) {
        case 1:
            val = val.replace(/^(\\-)*(\d+)\.(\d).*$/, "$1$2.$3"); //只能输入一个小数
            break;
        case 2:
            val = val.replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
            break;
        case 3:
            val = val.replace(/^(\\-)*(\d+)\.(\d\d\d).*$/, "$1$2.$3"); //只能输入三个小数
            break;
        case 4:
            val = val.replace(/^(\\-)*(\d+)\.(\d\d\d\d).*$/, "$1$2.$3"); //只能输入四个小数
            break;
        default:
            val = val.replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数
            break;
    }
    return val;

}
function formatMoneyWith6digts2(value) {
    let obj = value.toString();
    if (String(obj).indexOf('.') > -1) { //判断有没有输入小数点

    } else {
        var p11 = /[^\d]/g;
        var p22 = /(\d{6})\d*$/g;
        var p44 = /(\d*)\6/g; //删除当我输入第七位的整数时候进行删除
        obj = obj.replace(p11, "").replace(p22, "$1").replace(p44, "$1$9");
    }
    var p1 = /[^\d\.]/g;   // 过滤非数字及小数点 /g :所有范围中过滤
    var p2 = /(\.\d{4})\d*$/g;
    var p4 = /(\.)(\d*)\1/g;
    obj = obj.replace(p1, "").replace(p2, "$1").replace(p4, "$1$9");
    obj = obj.replace(/[^0-9.]/g, '');
    var p5 = /\.+/g;   //多个点的话只取1个点，屏蔽1....234的情况
    obj = obj.replace(p5, ".");
    var p6 = /(\.+)(\d+)(\.+)/g; //屏蔽1....234.的情况
    obj = obj.replace(p6, "$1$2")// 屏蔽最后一位的.
    props.modelValue = obj
}
let checkList = ref([])
let dataList = ref([])//乡镇
let dataList2 = ref([])//村社区
function handleCheckedCitiesChange(value) {
    checkList.value = checkList.value.filter(item => item.trim() !== "");
    emits("update:modelValue", checkList.value.join(","))
    emits('changeYXQGZ', checkList.value.join(","))
}
/** 懒加载获取树形结构*/
function getList() {
    if (props.paramKey == 'townId') {
        let params = {
            areaId: '3781d3ca21cb11ec8850f48e38bf4326',
            minLevel: '4',
            type: '1',
            useToken: '1'
        };
        $axios({
            method: "get",
            url: "/area/getAreaTree",
            data: params,
        })
            .then((response) => {
                if (response.data.code == 200) {
                    response.data.data[0].children.forEach(element => {
                        element.children = []
                    });
                    dataList.value = response.data.data[0].children;
                } else {

                }
            })
            .catch(() => {

            });

    }

}
const mapClick = () => {
    if (props.paramKey == 'addressLocation' || props.paramKey == 'storeLocation') {
        emits('mapClick')
    }
}
const nodeName = ref()//回显值
const nodeName2 = ref()//回显值
let deptId = reactive({
    id: ''
});
function handleNodeClick(node) {
    emits('changeTree', node.id, 2)
}
function handleNodeClick2(node) {
    // emits('update:modelValue', node.id);//v-model方式
    // nodeName2.value = node.name;
}
function handleChange(e) {
    emits("changeNcp", e)
}
const changeShowZj2 = inject('changeShowZj2');

function changeSelect(e) {
    if (props.dictType == 'LZFS') {
        emits('changeShowZj', e)
    }
    if (props.paramKey == 'yearNum' || props.paramKey == 'monthNum') {
        // emits('changeShowZj2', e, props.objVal)
        changeShowZj2(e, props.objVal,props.paramKey)

    }
}
function changeRadio(e) {
    if (props.dictType == 'YXQXQ') {
        emits('changeYXQXQ', e, props.objVal)
    }
    if (props.dictType == 'FWFSQGZ') {
        emits('changeFWFSQGZ', e, props.objVal)
    }
    if (props.dictType == 'LYSQGZ') {
        emits('changeLYSQGZ', e, props.objVal)
    }
    if (props.dictType == 'ZJDJ') {
        emits('changeZJDJ', e, props.objVal)
    }
}
function changeInput(e) {
    if (props.paramKey == "totalTransationPrice") {
        if (e.target) {
            emits('changePriceUpper', e.target.value)
        }
    }
    if (props.dictType == "telephone") {
        emits('regPhone', e, props.objVal)
    }
}
function changeInput2(e) { // 勿删
    if (props.paramKey == "totalTransationPrice") {
        if (e.target) {
            emits('changePriceUpper', e.target.value)
        }
    }
}
function changeMoney(e) {
    toChies(e)
}
// 大写数字过滤器
function toChies(amount) {
    // 汉字的数字
    const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    // 基本单位
    const cnIntRadice = ['', '拾', '佰', '仟'];
    // 对应整数部分扩展单位
    const cnIntUnits = ['', '万', '亿', '兆'];
    // 对应小数部分单位
    const cnDecUnits = ['角', '分', '厘', '毫'];
    // 整数金额时后面跟的字符
    const cnInteger = '整';
    // 整型完以后的单位
    const cnIntLast = '元';
    // 最大处理的数字
    const maxNum = 999999999999999.99;
    // 金额整数部分
    let integerNum;
    // 金额小数部分
    let decimalNum;
    // 输出的中文金额字符串
    let chineseStr = '';
    // 分离金额后用的数组，预定义
    let parts;
    if (amount === '') {
        return '';
    }
    amount = parseFloat(amount);
    if (amount >= maxNum) {
        // 超出最大处理数字
        return '';
    }
    if (amount === 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger;
        return chineseStr;
    }
    // 转换为字符串
    amount = amount.toString();
    if (amount.indexOf('.') === -1) {
        integerNum = amount;

        decimalNum = '';
    } else {
        parts = amount.split('.');
        integerNum = parts[0];
        decimalNum = parts[1].substr(0, 4);
    }
    // 获取整型部分转换
    if (parseInt(integerNum, 10) > 0) {
        let zeroCount = 0;
        const IntLen = integerNum.length;
        for (let i = 0; i < IntLen; i++) {
            const n = integerNum.substr(i, 1);
            const p = IntLen - i - 1;
            const q = p / 4;
            const m = p % 4;
            if (n === '0') {
                zeroCount++;
            } else {
                if (zeroCount > 0) {
                    chineseStr += cnNums[0];
                }
                // 归零
                zeroCount = 0;
                //alert(cnNums[parseInt(n)])
                chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
            }
            if (m === 0 && zeroCount < 4) {
                chineseStr += cnIntUnits[q];
            }
        }
        chineseStr += cnIntLast;
    }
    // 小数部分
    if (decimalNum !== '') {
        const decLen = decimalNum.length;
        for (let i = 0; i < decLen; i++) {
            const n = decimalNum.substr(i, 1);
            if (n !== '0') {
                chineseStr += cnNums[Number(n)] + cnDecUnits[i];
            }
        }
    }
    if (chineseStr === '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger;
    } else if (decimalNum === '') {
        chineseStr += cnInteger;
    }
    moneyUpper.value = chineseStr;
    return chineseStr;
}

const state = reactive({
    timeout: null,
    RWMC: "",
})
const handleSelect = (item) => {
    emits('changeCertNo', item)
    // console.log(item)
}
const handleSelect2 = (item) => {
    emits('changeOrg', item.deptCode)
}
const querySearchAsync2 = async (queryString, cb) => {
    $axios({
        url: "/transferor/getDept/" + props.unitId,
        method: "get",
    }).then((res) => {
        if (res.data.code === 200) {
            const restaurants = res.data.data;
            cb(restaurants)
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
//获取输入建议的方法,回调函数返回
const querySearchAsync = async (queryString, cb) => {
    clearTimeout(state.timeout);
    var results = []
    if (queryString == '') {
        cb(results);
    } else {
        //掉接口需要的参数
        // let find = {
        //   name: queryString, //上面输入框绑定的数据
        // };
        try {
            let result = []
            $axios({
                method: "get",
                url: "/tenders/getHyUser/" + queryString
            })
                .then((response) => {
                    if (response.data.code == 200) {
                        result = response.data.data.data
                        if (result) {
                            //循环放到一个远程搜索需要的数组
                            for (let i = 0; i < result.length; i++) {
                                const element = result[i];
                                results.push({
                                    // value: element.retenant +element.reletCertNo,
                                    value: element.retenant,
                                    link: element.reletCertNo,
                                    id: element.id,
                                    reletCertNo: element.reletCertNo,
                                    reletCertType: element.reletCertType,
                                })
                            }
                            cb(results);
                        } else {
                            results = []
                            cb(results);
                        }
                    } else {
                        ElMessage.error(response.data.msg)
                        results = []
                        cb(results);
                    }
                })
                .catch(() => {

                });

        } catch (error) {
            console.log(error);
        }
    }
}

const selectFun=()=>{
    if(!props.dictType) return;

    $axios({
            method: "get",
            url: "/basecode/getBaseCodeInfo?baseType="+props.dictType
        })
            .then((response) => {
                if (response.data.code == 200) {
                    dataArry.value=response.data.data
                } 
            })
            .catch(() => {

            });
}
// watch(() => props.objVal.optionSet, (newValue, oldValue) => {
//     if(props.objVal.enumType !== 0 && props.objVal.optionSet && props.objVal.optionSet.length!=0){
//         dataArry.value=props.objVal.optionSet;
//     }else{
//         selectFun();
//     }
// }, { deep: true, immediate: true },)
onMounted(() => {
    // if(props.objVal.enumType !== 0 && props.objVal.optionSet){
    //     dataArry.value=props.objVal.optionSet;
    // }else{
    //     selectFun();
    // }
    // if (props.type == '9' || props.type == 9) {
    //     if (props.modelValue == null || props.modelValue == undefined) {

    //     } else {
    //         let str = props.modelValue;
    //         checkList.value = str.split(',')
    //     }

    // }
})
</script>
  
<style lang='scss' scoped>
.set-up-class {
    :deep(.left-box) {
        span::before {
            content: "*";
            color: red;
        }
    }
}

:deep(.left-box) {
    text-align: right;
    min-width: 150px !important;
}

// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
    width: 100%;
}

:deep(.el-select) {
    width: 100%;
}

:deep(.el-autocomplete) {
    width: 100%;
}

:deep(.el-cascader) {
    width: 100%;
}

.nd-input-box :deep(.el-input) {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
    width: 100%;
    // padding-left: 10px;
    padding-right: 10px;
}

:deep(.el-input.is-disabled) {
    width: 100%;
}

:deep(.el-date-editor.el-input) {
    width: 100%;
}
</style>
  