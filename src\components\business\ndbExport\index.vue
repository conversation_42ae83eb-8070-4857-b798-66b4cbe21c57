<template>
    <div>
        <ndDialog ref="exportUploadDialog" width="1200px" height="600px" :title="data.title" append-to-body
            :before-close="hsDetailClose" align-center>
            <div class="exportUploadBox">
                <!-- 提示 -->
                <div class="remind">
                    <p>· 为保障导出效率，系统要用异步导出，即导出后台运行，用户无需一直等待，导出文件生成后再进行下载</p>
                    <p>· 您可从下方导出记录中直接【下载】已有信息，也可根据需要重新生成导出结果</p>
                </div>
                <!-- 新增导出（用于区别导出内容） -->
                <div class="rename">
                    <div class="title">
                        新增导出
                    </div>
                    <textarea class="textarea" v-model="data.remarks" placeholder="备注信息（用以区别导出内容）"></textarea>
                </div>
                <!-- 验证码 -->
                <div class="verificationCode" v-if="data.codeShow">
                    <div class="Code">
                        <el-icon>
                            <Warning />
                        </el-icon>
                        <p>操作太频繁，请输入验证码以继续</p>
                    </div>
                    <div class="verification">
                        <div style="margin-right: 5px;">验证码</div>
                        <ndInput v-model="data.code" width="100px"></ndInput>
                        <img :src="data.codeImg" alt="" style="height: 40px; margin: 0 10px; cursor: pointer;"
                            @click="getCode">
                        <p style="margin-left: 5px;">看不清楚，换一张</p>
                    </div>
                </div>
                <!-- 最近3次导出记录 -->
                <div class="derivedRecord">
                    <div class="title">最近3次导出记录</div>
                    <ndTable ref="table" :data='tableData.tableList' style="height: 100%" v-load="data.loading">
                        <el-table-column align="center" prop="updateTime" label="创建时间" />
                        <el-table-column align="center" prop="fileName" label="文件名称" show-overflow-tooltip />
                        <el-table-column align="center" prop="remarks" label="备注信息" show-overflow-tooltip />
                        <el-table-column align="center" prop="status" label="状态">
                            <template #default="scope">
                                <span v-if="scope.row.status"
                                    style="display: flex;align-items: center; justify-content: center;">
                                    <div class="circleR"></div>导出结果已生成
                                </span>
                                <span v-if="!scope.row.status"
                                    style="display: flex;align-items: center; justify-content: center;">
                                    <div class="circleW"></div>处理中
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="下载导出结果">
                            <template #default="scope">
                                <span class="operation" @click="download(scope.row.filePath)">下载</span>
                            </template>
                        </el-table-column>
                    </ndTable>
                </div>
            </div>
            <template #footer>
                <ndButton type="primary" @click="exportExcel">确定导出</ndButton>
                <ndButton @click="hsDetailClose">取消</ndButton>
            </template>
        </ndDialog>
        <downloadCenter ref="loadCenter"></downloadCenter>
    </div>
</template>

<script setup>
import ndDialog from '@/components/ndDialog.vue';
import ndInput from '@/components/ndInput.vue';
import ndButton from '@/components/ndButton.vue';
import ndTable from '@/components/ndTable.vue';
import downloadCenter from './components/downloadCenter.vue';
import { ref, inject, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus'
const $axios = inject('$axios');
// 变量
const data = reactive({
    title: "导出设置",
    codeShow: false,
    remarks: '',
    // 验证码数字
    code: '',
    // table刷新
    loading: false,
    // 验证码照片
    codeImg: '',
    // 接收父组件传过来的值
    modelQuery: {},
    type: '',
    exportForm: '',
})

// table 数据
const tableData = reactive({
    tableList: []
})
// 获取 最近3次的导出记录
const getRecord = (type) => {
    data.loading = true
    $axios({
        url: "/baseCode/queryExportRecord",
        method: "get",
        serverName: 'gq-base',
        data: { types: type }
    }).then((res) => {
        if (res.data.code === 200) {
            data.loading = false
            tableData.tableList = res.data.data
            if (tableData.tableList.length > 0 && showHideCode(tableData.tableList[0].updateTime)) {
                data.codeShow = true
                getCode() // 获取验证码
            }
        }
    })
}
// 下载
const download = (item) => {
    window.location.href = item;
}
// 获取验证码
const getCode = () => {
    $axios({
        url: "/baseCode/verificationCode",
        method: "get",
        serverName: 'gq-base',
        data: '',
        responseType: 'arraybuffer'
    }).then((res) => {
        if (res.status === 200) {
            data.codeImg = 'data:image/jpeg;base64,' + arrayBufferToBase64(res.data)
        }
    })
}
const arrayBufferToBase64 = (buffer) => {
    var binary = ''
    var bytes = new Uint8Array(buffer)
    var len = bytes.byteLength
    for (var i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
}
const exportUploadDialog = ref(null)
const loadCenter = ref(null)

/** type
 * QUEUE_EXPORT_ORG 组织查询
 * 成员查询    0 成员信息   1 户信息
 *QUEUE_EXPORT_REPEAT_MEMBER  成员查重 
 **/
/** exportForm
 * 1-组织查询  
 * 2-成员户
 * 3-成员查重
 **/
/** search
 * 父组件传过来的搜索条件  
 **/
const open = (search, type, exportForm) => {
    tableData.tableList = [],
        data.remarks = '',
        data.codeShow = false,
        // 验证码数字
        data.code = '',
        // table刷新
        data.loading = false,
        // 验证码照片
        data.codeImg = '',
        data.type = type,
        // 接收父组件传过来的值
        data.modelQuery = search,
        data.exportForm = exportForm,
        getRecord(type) // 获取导出列表数据
    exportUploadDialog.value.open()
}
const hsDetailClose = () => {
    exportUploadDialog.value.close()
}
// 导出
const exportExcel = () => {
    let params = {}
    if (data.codeShow) {
        params = {
            type: data.type,
            exportForm: data.exportForm,
            validCode: data.code,
            remarks: data.remarks,
            ...data.modelQuery
        }
    } else {
        params = {
            type: data.type,
            exportForm: data.exportForm,
            remarks: data.remarks,
            ...data.modelQuery
        }
    }

    $axios({
        url: "/baseCode/exportExcel",
        method: "post",
        serverName: 'gq-base',
        data: params,
    }).then((res) => {
        if (res.data.code === 200) {
            exportUploadDialog.value.close()
            loadCenter.value.open(data.type, res.data.data)
        } else {
            ElMessage({
                message: res.data.message,
                type: 'warning',
            })
        }

    })
}

// 判断 时间是否是当前时间 并显示或者隐藏 验证码
const showHideCode = (str) => {
    var d = new Date(str.replace(/-/g, "/"));
    var todaysDate = new Date();
    if (d.setHours(0, 0, 0, 0) == todaysDate.setHours(0, 0, 0, 0)) {
        return true
    } else {
        return false
    }
}
// 方法导出，便于父组件接收并且调用
defineExpose({
    open
})
</script>

<style lang="scss" scoped>
.circleR {
    width: 10px;
    height: 10px;
    border-radius: 50% 50%;
    background-color: #1390ff;
}

.circleW {
    width: 10px;
    height: 10px;
    border-radius: 50% 50%;
    background-color: red;
}

.operation {
    color: #0098ff;
    cursor: pointer;
    display: inline-block;
    margin-right: 8px;
}

.title {
    font-weight: bold;
    font-size: 15px;
    margin: 5px 5px;
}

.exportUploadBox {
    width: 100%;
    height: 100%;
    padding: 5px 5px;

    .remind {
        background-color: #E1F0FF;
        color: #76bffc;
        font-size: 14px;
        padding: 5px 10px;
    }

    .rename {

        .textarea {
            margin-left: 10px;
            width: calc(95% - 6px);
            height: 56px;
            resize: none;
            border: 1px solid #dce6f3;
            border-radius: 2% 2%;
            // background: #f7f7f7;
            outline: none;
            /* overflow-y:auto ; */
        }
    }

    .verificationCode {
        .Code {
            display: flex;
            align-items: center;
            margin: 5px 5px;
            font-size: 14px;
            color: red;
        }

        .verification {
            margin: 5px 10px;
            display: flex;
            align-items: center;
        }

    }
}
</style>