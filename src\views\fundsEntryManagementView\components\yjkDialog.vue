<!-- 已缴款待确认弹窗-->
<template>
  <nd-dialog ref="dialogRef" title="缴款信息" align-center :before-close="close" max-height="65vh" width="70vw">
    <el-scrollbar max-height="65vh" ref="scrollbarRef">
      <div style="padding: 0 15px">
        <div class="split-layout">
          <!-- 左侧：缴款信息 -->
          <div class="left-panel">
            <div class="wrap-container">
              <div class="moudle-box">
                <div class="moudle-title">基本信息</div>
                <div class="moudle-content">
                  <div class="table-row">
                    <div class="row-key">缴款人</div>
                    <!-- <div class="row-value">{{ detailData.signupName }}</div> -->
                    <div class="row-value">
                      <encrypt :field="detailData.signupName" :fieldEntry="detailData.signupNameEncrypt" />
                      <!-- <el-tag type="warning" v-if="detailData.payUserType == 2" style="margin-left: 5px">招标人</el-tag> -->
                    </div>
                  </div>
                  <div class="table-row" v-if="detailData.identityName">
                    <div class="row-key">缴款人身份</div>
                    <div class="row-value">
                      {{ detailData.identityName }}
                    </div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">应缴金额（元）</div>
                    <div class="row-value">{{ detailData.needTotalAmount }} （{{ convertCurrency(detailData.needTotalAmount) }}）</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">是否成交</div>
                    <div class="row-value">{{ detailData.tradeFlagName }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">费用类型</div>
                    <div class="row-value">{{ detailData.feeName || "--" }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">缴款状态</div>
                    <div class="row-value">{{ detailData.payStatusName }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">证件类型</div>
                    <div class="row-value">{{ detailData.certTypeName }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">操作人</div>
                    <div class="row-value">{{ detailData.opreateName }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">证件号码</div>
                    <!-- <div class="row-value">{{ detailData.certNo }}</div> -->
                    <div class="row-value">
                      <encrypt :field="detailData.certNo" :fieldEntry="detailData.certNoEncrypt" />
                    </div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">操作时间</div>
                    <div class="row-value">{{ detailData.opreateTime }}</div>
                  </div>
                </div>
                <div class="moudle-title">缴款信息</div>
                <div class="moudle-content">
                  <div class="table-row">
                    <div class="row-key">交易缴款人</div>
                    <!-- <div class="row-value">{{ detailData.signupName }}</div> -->
                    <div class="row-value">
                      <encrypt :field="detailData.signupName" :fieldEntry="detailData.signupNameEncrypt" />
                    </div>
                  </div>
                  <div class="table-row" v-if="[2].includes(detailData.payType)">
                    <div class="row-key">缴款账号</div>
                    <div class="row-value">{{ detailData.virtualAccount || "--" }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key">实缴金额（元）</div>
                    <!-- <div class="row-value">{{ detailData.costFeeDetailStr || '--' }}</div> -->
                    <div class="row-value">
                      {{ detailData.batchFlag == 1 && detailData.payStatus == 9 ? detailData.tureCostFeeDetailStr : detailData.trueTotalAmount }}
                    </div>
                  </div>
                  <div class="table-row" v-if="[2].includes(detailData.payType)">
                    <div class="row-key">收款银行</div>
                    <div class="row-value">{{ detailData.bankName || "--" }}</div>
                  </div>
                  <div class="table-row" v-if="[2].includes(detailData.payType)">
                    <div class="row-key">收款账号</div>
                    <div class="row-value">{{ detailData.bankAccount || "--" }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key red-start">支付方式</div>
                    <div class="row-value">
                      {{ detailData.payTypeName }}
                      <el-button v-if="detailData.payType == 2" link type="primary" @click="handleViewjktz">缴款通知单</el-button>
                      <nd-button v-if="[4].includes(detailData.payType) && detailData.payStatus == 2" link type="primary" @click="handleViewQrcode">收款码</nd-button>
                      <nd-button v-if="[3].includes(detailData.payType) && detailData.payStatus == 2" link type="primary" @click="handleViewJhPay">聚合收款码</nd-button>
                      <nd-button v-if="detailData.payType == 5 && detailData.payStatus == 2" link type="primary" @click="handleViewPos">POS机收款码</nd-button>
                      <nd-button v-if="detailData.payType == 6 && detailData.payStatus == 2" link type="primary" @click="handleViewSzrmb">数字人民币收款码</nd-button>
                    </div>
                  </div>
                  <div class="table-row" v-if="['0', '1', '7'].includes(detailData.payType + '')" style="min-width: 100%">
                    <div class="row-key">缴款凭证</div>
                    <div class="row-value" style="min-width: calc(100% - 175px)">
                      <ndbUpload2 v-model="jkpzFileList" disabled />
                    </div>
                  </div>
                  <div class="table-row" v-if="[2].includes(detailData.payStatus)">
                    <div class="row-key red-start">收款账号</div>
                    <div class="row-value">{{ detailData.bankAccount || "--" }}</div>
                  </div>
                  <div class="table-row">
                    <div class="row-key red-start">到账时间</div>
                    <div class="row-value">{{ detailData.secondTime || "--" }}</div>
                  </div>

                  <div class="table-row" style="min-width: 100%">
                    <div class="row-key">备注说明</div>
                    <div class="row-value">
                      {{ detailData.remark || "--" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：确认收款 -->
          <div class="right-panel">
            <div class="wrap-container">
              <div class="moudle-box">
                <div class="moudle-title" v-if="detailData.surePayAuth && pageType == 1">确认收款</div>

                <!-- 收款意见 -->
                <div v-if="detailData.surePayAuth && pageType == 1" class="moudle-content" style="border: none">
                  <div class="table-row" style="padding-right: 0; border-radius: 0; border: none; width: 100%">
                    <el-form ref="formRef" style="width: 100%" :model="validateForm" label-width="auto" class="demo-ruleForm">
                      <el-form-item style="width: 100%" label="" prop="suggest" :rules="[{ required: validateForm.required, message: '收款意见不能为空', trigger: 'blur' }]">
                        <el-input type="textarea" :autosize="{ minRows: 5, maxRows: 5 }" maxlength="250" v-model="validateForm.suggest" placeholder="请输入收款意见"></el-input>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 移动到这里的底部按钮 -->
                  <div class="action-buttons" v-if="pageType == 1 && detailData.surePayAuth">
                    <nd-button type="primary" icon="Check" @click="handleSubmit">确认收款</nd-button>
                    <nd-button icon="Close" @click="notReceived">未收到款</nd-button>
                  </div>
                </div>

                <!-- 收款流程 -->
                <div class="moudle-title">收款流程</div>
                <div class="moudle-content">
                  <div v-if="step.length">
                    <div class="table-row" :style="{ height: step.length * 100 + 'px' }">
                      <el-steps direction="vertical" :active="step.length">
                        <el-step :space="200" v-for="(item, index) in step" :key="index" :icon="CircleCheck">
                          <template #title>
                            <div style="font-size: 14px; color: #444; font-weight: 400">{{ opreateTypeMap[item.opreateType] + "人 : " + item.opreateName }}</div>
                          </template>
                          <template #description>
                            <div style="font-size: 13px; color: #909399; font-weight: 400">{{ opreateTypeMap[item.opreateType] }}时间：{{ item.opreateTime }}</div>
                            <div style="font-size: 13px; color: #909399; font-weight: 400" v-if="item.opreateType == 2">
                              确认结果：{{ item.opreateResult == 0 ? "不通过" : "通过" }}
                            </div>
                            <div style="font-size: 13px; color: #909399; font-weight: 400" v-if="item.opreateType == 2">收款意见：{{ item.suggest }}</div>
                          </template>
                        </el-step>
                      </el-steps>
                    </div>
                  </div>
                  <div v-else style="display: flex; justify-content: center;width: 100%;">
                    <img src="@/assets/images/empty.png" class="empty-img" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div>
        <nd-button icon="Close" @click="close">关闭</nd-button>
      </div>
    </template>
  </nd-dialog>

  <jktzdDialog ref="jktzdDialogRef" @refreshType="getCostPayDetail" />
  <jhPayDialog ref="jhPayDialogRef" @refreshType="getCostPayDetail" />
  <codePayDialog ref="codePayDialogRef" @refreshType="getCostPayDetail" />
  <posPayDialog ref="posPayDialogRef" @refreshType="getCostPayDetail" />
  <szrmbPayDialog ref="szrmbPayDialogRef" @refreshType="getCostPayDetail" />
</template>
<script setup>
import { ref, watch, inject, onMounted, h, reactive, nextTick } from "vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTabs from "@/components/ndTabs.vue";

import ndbUpload2 from "@/components/business/ndbUpload2/index.vue";
import jktzdDialog from "./jktzdDialog.vue";
import jhPayDialog from "./jhPayDialog.vue";
import codePayDialog from "./codePayDialog.vue";
import posPayDialog from "./posPayDialog.vue";
import szrmbPayDialog from "./szrmbPayDialog.vue";
import encrypt from "./encrypt.vue";

import { ElMessage, ElMessageBox } from "element-plus";
import { CircleCheck } from "@element-plus/icons-vue";

const $axios = inject("$axios");

const emits = defineEmits(["refreshData"]);

let pageType = ref(1); // 1：编辑 2：查看

let activeName = ref(1);

const formRef = ref(null);
const validateForm = reactive({
  suggest: "",
  required: true,
});

const opreateTypeMap = {
  0: "--",
  1: "提交",
  2: "确认",
};

onMounted(() => {
  // open()
});

const detailData = ref({});
const step = ref([]);

// const suggest = ref('')
const payType = ref("");

const dialogRef = ref(null);

let preParams = {};

const jkpzFileList = ref([]);

// 查询附件
watch(detailData, (val) => {
  $axios({
    url: "/file/getFileCommon",
    method: "get",
    data: {
      busId: val.feeIds || "xzx",
      configFileId: "RJPZ",
    },
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    jkpzFileList.value = res.data.data || [];
  });
});

// 查询提交后的详情
function getCostPayDetail() {
  $axios({
    url: "/bankIncome/costPayDetail",
    method: "get",
    data: { feeId: preParams.feeId, proId: preParams.proId },
    // headers: {
    //     showStyle: 1,
    // },
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    // res.data.data.surePayAuth = 1
    // alert(res.data.data.surePayAuth)
    detailData.value = res.data.data || {};
  });
}
// 查询收款流程
function getInComeFlow({ feeId }) {
  $axios({
    url: "/bankIncome/getInComeFlow",
    method: "get",
    data: { feeId },
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    step.value = res.data.data || {};
  });
}

/**
 * @description:
 * @param {*} params 表格行数据
 * @param {*} type   1：待确认收款  2：已确认收款（查看页面）
 * @return {*}
 */
function open(params, type) {
  pageType.value = type || 1;

  preParams = params;
  dialogRef.value.open();

  getCostPayDetail(params);
  getInComeFlow(params);
}

function copyToClip(content, message) {
  var aux = document.createElement("input");
  aux.setAttribute("value", content);
  document.body.appendChild(aux);
  aux.select();
  document.execCommand("copy");
  document.body.removeChild(aux);
  if (message == null) {
    ElMessage.success("复制成功");
  } else {
    ElMessage.success(message);
  }
}

function close() {
  validateForm.suggest = "";

  dialogRef.value.close();
  emits("refreshData");
}

const scrollbarRef = ref(null);
function notReceived() {
  formRef.value.validate((valid) => {
    if (!valid) {
      // nextTick(() => {
      //     scrollbarRef.value.scrollTo({ top: scrollbarRef.value.wrapRef.scrollHeight, behavior: 'smooth' });
      // })
      return;
    }

    let params = {
      feeId: detailData.value.feeId,
      opreateResult: 0,
      suggest: validateForm.suggest,
    };

    // if (!params.suggest) return ElMessage.warning('请填写收款意见！')

    let orderId = detailData.value.orderId;

    $axios({
      url: "/bankIncome/getOrderInfo",
      method: "get",
      data: { orderId },
    }).then((res) => {
      if (res.data.code == 200) {
        // 如果是银行支付直接弹窗
        if (["2", "3", "4", "5", "6"].includes(res.data.data.payType + "")) {
          ElMessageBox.confirm(
            h("p", { style: "min-width:200px" }, [
              h("p", null, "该费用已到账，请确认收款!"),
              h("span", null, `银行交易订单号:` + res.data.data.dealNumberFromBank),
              h(
                "span",
                {
                  style: "cursor:pointer;color:#79bbff;margin-left:10px",
                  onClick: () => {
                    copyToClip(res.data.data.dealNumberFromBank);
                  },
                },
                "复制"
              ),
            ]),
            "温馨提示",
            {
              center: true,
              confirmButtonText: "确认收款",
              cancelButtonText: "取消",
            }
          ).then(() => {
            handleSubmit();
          });
        } else {
          ElMessageBox({
            title: "温馨提示",
            message: h("p", { style: "width:580px" }, [
              h("b", null, "不确认收款，会回到待缴款状态，需要用户重新缴费！"),
              h("br", null, ""),
              h("b", null, "为避免重复缴款请与缴款人进行确认后再操作!"),
            ]),
            showCancelButton: true,
            distinguishCancelAndClose: true,
            confirmButtonText: "确定收款",
            cancelButtonText: "未收到款",
            appendTo: ".xz-box",
          })
            .then((res) => {
              handleSubmit();
            })
            .catch((action) => {
              if (action == "cancel") {
                $axios({
                  url: "/bankIncome/paySure",
                  method: "post",
                  data: params,
                }).then((res) => {
                  if (res.data.code != 200) return ElMessage.warning(res.data.msg);
                  ElMessage.success("已确认未收到该款项！");
                  close();
                });
              } else {
                ElMessageBox.close(false);
              }
            });
        }
      }
    });
  });
}

function handleSubmit() {
  let params = {
    feeId: detailData.value.feeId,
    opreateResult: 1,
    suggest: validateForm.suggest || "同意",
  };

  $axios({
    url: "/bankIncome/paySure",
    method: "post",
    data: params,
  }).then((res) => {
    if (res.data.code != 200) return ElMessage.warning(res.data.msg);
    ElMessage.success("收款成功！");
    close();
  });
}

const jktzdDialogRef = ref();
// 查看缴款通知单
function handleViewjktz() {
  jktzdDialogRef.value.open(detailData.value);
}

const jhPayDialogRef = ref();
// 查看聚合支付码
function handleViewjhsk() {
  jhPayDialogRef.value.open(detailData.value);
}

const codePayDialogRef = ref();
// 查看付款码
function handleViewQrcode() {
  codePayDialogRef.value.open(detailData.value);
}

const posPayDialogRef = ref();
// 查看pos机收款码
function handleViewPos() {
  posPayDialogRef.value.open(detailData.value);
}

const szrmbPayDialogRef = ref();
// 查看数字人民币付款码
function handleViewSzrmb() {
  szrmbPayDialogRef.value.open(detailData.value);
}

//数字转大写
function convertCurrency(money) {
  // 汉字的数字
  let cnNums = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
  // 基本单位
  let cnIntRadice = ["", "拾", "佰", "仟"];
  // 对应整数部分扩展单位
  let cnIntUnits = ["", "万", "亿", "兆"];
  // 对应小数部分单位
  let cnDecUnits = ["角", "分", "毫", "厘"];
  // 整数金额时后面跟的字符
  let cnInteger = "整";
  // 整型完以后的单位
  let cnIntLast = "元";
  // 最大处理的数字
  let maxNum = 999999999999.9999;

  // 输出的中文金额字符串
  let chineseStr = "";

  if (money === "") {
    return "";
  }

  money = parseFloat(money);

  if (money >= maxNum) {
    // 超出最大处理数字
    return "";
  }

  if (money === 0) {
    return cnNums[0] + cnIntLast + cnInteger;
  }

  // 转换为字符串
  money = money.toString();
  let [integerNum, decimalNum] = money.split(".");

  // 处理整数部分
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;

    for (let i = 0; i < IntLen; i++) {
      let n = integerNum.charAt(i);
      let p = IntLen - i - 1;
      let q = Math.floor(p / 4);
      let m = p % 4;

      if (n === "0") {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0]; // 添加零
        }
        zeroCount = 0; // 归零
        chineseStr += cnNums[parseInt(n, 10)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast; // 添加元
  }

  // 处理小数部分
  if (decimalNum) {
    const decLen = decimalNum.length > 4 ? 4 : decimalNum.length; // 限制小数位数最多4位
    for (let i = 0; i < decLen; i++) {
      let n = decimalNum.charAt(i);
      if (n !== "0") {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }

  // 处理没有小数部分的情况
  if (chineseStr === "") {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === undefined || decimalNum === "") {
    chineseStr += cnInteger; // 添加整
  }

  return chineseStr;
}

defineExpose({
  open,
});
</script>
<style>
.xz-box .el-message-box {
  max-width: 580px !important;
}
</style>
<style lang="scss" scoped>
:deep(.el-textarea .el-textarea__inner) {
  // 去掉文本域拉伸
  resize: none;
}

// 左右分栏布局样式
.split-layout {
  display: flex;
  gap: 20px;
  
  .left-panel {
    flex: 7;
    min-width: 0;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: -10px;
      width: 1px;
      height: 100%;
      background-color: #EBEEF5;
    }
  }
  
  .right-panel {
    flex: 3;
    min-width: 0;
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 10px;
}

.empty-img {
  width: 100%;
  height: 300px;
  object-fit: contain;
}

.wrap-container {
  width: 100%;
  height: 100%;
  min-height: 400px;

  .moudle-box {
    padding: 0 5px;

    .moudle-title {
      font-size: 14px;
      color: #606266;
      margin: 10px 0;
      font-weight: 700;
    }

    .moudle-content {
      color: #555;
      font-size: 14px;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      border: 1px solid #eee;
      border-bottom: none;
      border-right: none;

      .table-row {
        display: flex;
        min-height: 34px;
        border-bottom: 1px solid #eee;
        border-right: 1px solid #eee;
        min-width: 50%;
        flex: 1;
        position: relative;
        padding-right: 30px;

        .row-key,
        .row-value {
          height: 100%;
          min-height: inherit;
          display: flex;
          align-items: center;
          padding: 8px;
          color: #444;
          word-break: break-all;
        }

        .row-key {
          color: #606266;
          text-align: right;
          justify-content: end;
          border-right: 1px solid #eee;
          background: #f9f9f9;
          min-width: 135px;
        }
      }
    }
  }
}

// 右侧面板特殊样式
.right-panel {
  .moudle-content {
    border: none !important;
  }
  
  .table-row {
    border: none !important;
    width: 100% !important;
    min-width: 100% !important;
    padding-right: 0 !important;
  }
  
  // 收款意见表单样式
  .demo-ruleForm {
    width: 100%;
    
    .el-form-item {
      width: 100%;
      margin-bottom: 0;
    }
  }
}
</style>
