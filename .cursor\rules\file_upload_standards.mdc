---
description: 
globs: 
alwaysApply: true
---
# 文件上传组件规范

## 基础组件说明

### ndbUpload2 组件
- **位置**: `src/components/business/ndbUpload2/index.vue`
- **功能**: 用于文件的上传、预览和管理
- **特点**: 支持多种上传方式，包括本地上传、手机扫码上传、高拍仪上传和扫描仪上传

## 组件使用方式

### 基本使用
```vue
<template>
  <ndbUpload v-model="fileList" :uploadParams="uploadParams" />
</template>

<script setup>
import ndbUpload from "@/components/business/ndbUpload2/index.vue";
import { ref } from "vue";

const fileList = ref([]);
const uploadParams = ref({
  busId: "业务ID",     // 必传，业务ID，用于关联文件
  configFileId: "配置ID" // 必传，文件配置ID
});
</script>
```

### 禁用状态（仅预览）
```vue
<ndbUpload v-model="fileList" :uploadParams="uploadParams" disabled />
```

### 限制上传数量
```vue
<ndbUpload v-model="fileList" :uploadParams="uploadParams" :limits="5" />
```

### 招投标场景支持（允许上传压缩包和文档）
```vue
<ndbUpload v-model="fileList" :uploadParams="uploadParams" :tenderFlag="true" />
```

## 支持的文件类型

### 默认情况
- 图片: jpg、png、bmp、jpeg
- PDF: pdf、PDF
- 大小限制: 
  - 图片不超过50M
  - PDF不超过20M

### 招投标场景 (tenderFlag=true)
- 图片: jpg、png、bmp、jpeg
- PDF: pdf、PDF
- 压缩包: zip、rar
- 文档: word、doc、docx
- 大小限制: 
  - 图片不超过50M
  - PDF不超过20M
  - 其他格式遵循相同限制

## 上传方式

### 本地上传
- 通过点击或拖拽方式上传本地文件
- 支持多文件批量上传

### 手机上传
- 通过微信/支付宝扫码，使用手机上传图片
- 二维码有效期5分钟，过期可刷新
- 手机端支持批量上传，与电脑端实时同步

### 高拍仪上传
- 支持使用高拍仪设备直接拍摄并上传文档

### 扫描仪上传
- 支持连接扫描仪设备扫描并上传文件

## 文件管理功能

### 预览
- 图片支持在线预览和放大
- PDF支持在新窗口打开查看
- 其他类型文件显示对应图标

### 删除
- 在非禁用状态下可删除已上传文件
- 删除会调用后台接口

### 全部清空
- 清空当前所有已上传文件

### 全部下载
- 下载当前所有已上传文件

### 重命名
- 支持对上传的文件进行重命名

### 调整顺序
- 支持调整文件的显示顺序

## 事件和回调

### 组件事件
- `update:modelValue`: 文件列表更新事件
- `getOcrData`: OCR识别数据回调
- `successCallback`: 文件操作成功回调

### 文件数据结构
```js
// 文件对象结构
{
  id: "文件ID",
  fileName: "文件名称",
  fileSuffix: "文件后缀",
  fileUrl: "文件预览地址",
  // 其他文件属性
}
```

## 注意事项
- 组件内置了对文件类型和大小的检验
- 上传前会自动对图片进行压缩处理
- 涉密文件禁止上传到非涉密平台
- 未保存前，文件上传仅为临时存储，需要配合业务保存接口一起使用

