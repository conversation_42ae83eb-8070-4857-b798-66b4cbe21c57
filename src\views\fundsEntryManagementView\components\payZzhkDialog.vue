<!-- 缴款弹窗-->
<template>
    <nd-dialog ref="dialogRef" title="缴款信息" align-center destroy-on-close :before-close="close" max-height="70vh"
        :width="showFlowPanel ? '70vw' : '60vw'">
        <el-scrollbar height="100%">
            <div style="padding: 20px; padding-top: 0; min-height: 400px" class="xz-box">
                <div class="split-layout">
                    <!-- 左侧：缴款信息 -->
                    <div class="left-panel">
                        <div class="wrap-container">
                            <div class="moudle-box">
                                <div class="moudle-content2"
                                    style="background: #f0f9ff;border-radius: 5px;padding: 10px 10px;">
                                    <div>若发生无法缴款的情况，可能是以下原因导致：</div>
                                    <div>1. 当前缴款人非平台注册用户，或已注册但未实名认证。</div>
                                    <div>2. 当前缴款人在项目收款银行中，无已备案认证的银行卡。</div>
                                </div>
                                <div class="moudle-title">基本信息</div>
                                <div class="moudle-content">
                                    <div class="table-row">
                                        <div class="row-key">缴款人</div>
                                        <!-- <div class="row-value">{{ detailData.signupName }}</div> -->
                                        <div class="row-value">
                                            <encrypt :field="detailData.signupName"
                                                :fieldEntry="detailData.signupNameEncrypt" />
                                            <!-- <el-tag type="warning" v-if="detailData.payUserType == 2" style="margin-left: 5px">招标人</el-tag> -->
                                        </div>
                                    </div>
                                    <div class="table-row" v-if="detailData.identityName">
                                        <div class="row-key">缴款人身份</div>
                                        <div class="row-value">
                                            {{ detailData.identityName }}
                                        </div>
                                    </div>
                                    <!-- <div class="table-row" v-if="isSingle != '2'"> -->
                                    <div class="table-row">
                                        <!-- 只有专场项目的投标保证金 才显示合计缴纳 -->
                                        <div class="row-key">{{ detailData.busType == 3 && detailData.marginNum ? '合计缴纳'
                                            :
                                            '应缴金额' }}</div>
                                        <div class="row-value">{{ detailData.needTotalAmount }}{{detailData.needTotalAmount?'（'+convertCurrency(detailData.needTotalAmount)+'）':''}} </div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">是否成交</div>
                                        <div class="row-value">{{ detailData.tradeFlagName }}</div>
                                    </div>
                                    <!-- v-if="isSingle===1" -->
                                    <div class="table-row" v-if="isSingle === 1 || isSingle === 3">
                                        <div class="row-key">费用类型</div>
                                        <div class="row-value">{{ detailData.feeName || '--' }}</div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">缴款状态</div>
                                        <div class="row-value">{{ detailData.payStatusName }}</div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">证件类型</div>
                                        <div class="row-value">{{ detailData.certTypeName }}</div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">操作人</div>
                                        <div class="row-value">{{ detailData.opreateName || '--' }}</div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">证件号码</div>
                                        <!-- <div class="row-value">{{ detailData.certNo }}</div> -->
                                        <div class="row-value">
                                            <encrypt :field="detailData.certNo"
                                                :fieldEntry="detailData.certNoEncrypt" />
                                        </div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">操作时间</div>
                                        <div class="row-value">{{ detailData.opreateTime || '--' }}</div>
                                    </div>
                                    <!-- 专场项目显示 -->
                                    <div class="table-row" v-if="detailData.busType == 3 && detailData.marginNum">
                                        <div class="row-key">单笔投标保证金（元）</div>
                                        <div class="row-value">{{ detailData.oneMarginAmount }}</div>
                                    </div>
                                    <div class="table-row" v-if="detailData.busType == 3 && detailData.marginNum">
                                        <div class="row-key">报名标段数量</div>
                                        <div class="row-value">{{ detailData.marginNum }}份保证金</div>
                                    </div>
                                </div>
                                <div class="moudle-title">缴款信息</div>
                                <!-- 待缴款信息显示 -->
                                <el-form ref="formRef" style="max-width: 100%" :model="validateForm" label-width="auto"
                                    class="demo-ruleForm" :validate-on-rule-change="false">
                                    <div class="moudle-content" v-if="detailData.payStatus == 1">
                                        <div class="table-row" style="min-width: 100%" v-if="'2'.includes(isSingle)">
                                            <div class="row-key">费用类型</div>
                                            <el-form-item label="" prop="checkList"
                                                style="margin-top: 0; margin-bottom: 0"
                                                :rules="[{ required: validateForm.required, validator: validateForm.validCheckList, message: '请选择要缴款的费用！', trigger: 'blur' }]">
                                                <div class="row-value">
                                                    <div style="min-width: 100px;margin-right: 6px">
                                                        合计 <span style="color: red">{{ totalM }}</span> 元
                                                    </div>

                                                    <el-checkbox-group size="small" v-model="checkList">
                                                        <el-checkbox :label="item.feeId"
                                                            v-for="(item, index) in detailData.costInFeeDetailVoList">
                                                            {{ item.feeTypeName + (item.marginNum ? item.marginNum +
                                                                '份，共' : '')
                                                                + item.needPayAmount + '元' }}
                                                        </el-checkbox>
                                                    </el-checkbox-group>
                                                </div>
                                            </el-form-item>
                                        </div>
                                        <div class="table-row" style="min-width: 100%">
                                            <div class="row-key red-start">支付方式</div>
                                            <el-form-item label="" prop="payType"
                                                style="margin-top: 0; margin-bottom: 0"
                                                rules="[{ required: true, message: '请选择支付方式！', trigger: 'change' }]">
                                                <div class="row-value">
                                                    <nd-select v-model="detailData.payType" @change="payTypeChange">
                                                        <el-option v-for="(item, index) in payTypes"
                                                            :disabled="isCheckPayType(item)"
                                                            :label="item.name + (item.amount != null ? `（余额：${item.amount}）` : '')"
                                                            :value="item.value" />
                                                    </nd-select>
                                                </div>
                                            </el-form-item>
                                        </div>
                                        <div class="table-row" style="min-width: 100%"
                                            v-if="['0', '1', '7'].includes(detailData.payType + '')">
                                            <div class="row-key red-start">缴款凭证</div>
                                            <el-form-item label="" prop="jkpzFileList"
                                                :rules="[{ required: validateForm.required2, validator: validateForm.validCheckjkpzFileList, message: '请上传缴款凭证！', trigger: 'change' }]">
                                                <div class="row-value" style="min-width: calc(100% - 175px)">
                                                    <ndbUpload2 v-model="jkpzFileList" 
                                                    :uploadParams="{
                                                        busId: isSingle == 2 ? checkList.sort().join(',') :
                                                            (isSingle == 3 && detailData.batchFlag == 1) ? detailData.feeIds :
                                                                detailData.feeId,
                                                        configFileId: 'RJPZ'
                                                    }" />
                                                </div>
                                            </el-form-item>
                                        </div>
                                        <div class="table-row" style="min-width: 100%">
                                            <div class="row-key">备注说明</div>
                                            <div class="row-value" style="min-width: calc(100% - 175px)">
                                                <el-input type="textarea" :autosize="false" maxlength="250"
                                                    v-model="detailData.remarks" placeholder="请输入"></el-input>
                                            </div>
                                        </div>
                                    </div>
                                </el-form>
                                <!-- 缴款入账中信息显示 -->
                                <div class="moudle-content" v-if="[2].includes(detailData.payStatus)">
                                    <!-- 是否批量缴款 1是 -->
                                    <div class="table-row" style="min-width: 100%" v-if="detailData.batchFlag == 1">
                                        <div class="row-key">费用类型</div>
                                        <div class="row-value">
                                            <span>{{ detailData.costFeeDetailStr }} </span>
                                        </div>
                                    </div>
                                    <div class="table-row" style="min-width: 100%">
                                        <div class="row-key">支付方式</div>
                                        <div class="row-value">
                                            {{ detailData.payTypeName }}
                                            <!-- 缴款入账中展示二维码 -->
                                            <nd-button v-if="[4].includes(detailData.payType)" link type="primary"
                                                @click="handleViewQrcode">收款码</nd-button>
                                            <nd-button v-if="[3].includes(detailData.payType)" link type="primary"
                                                @click="handleViewJhPay">聚合收款码</nd-button>
                                            <nd-button v-if="detailData.payType == 5" link type="primary"
                                                @click="handleViewPos">POS机收款码</nd-button>
                                            <nd-button v-if="detailData.payType == 6" link type="primary"
                                                @click="handleViewSzrmb">数字人民币收款码</nd-button>
                                        </div>
                                    </div>
                                    <div class="table-row" style="min-width: 100%" v-if="detailData.payType == 2">
                                        <div class="row-key">缴款通知单</div>
                                        <div class="row-value" style="min-width: calc(100% - 175px)">
                                            <div
                                                style="display: flex; justify-content: space-between; flex: 1; align-items: center">
                                                <el-button link type="primary" @click="handleViewjktz">
                                                    缴款通知单</el-button>
                                                <div>
                                                    <!-- <el-button type="primary" plain size="small" @click="handlePostMessage"> 发送短信 </el-button> -->
                                                    <el-button type="primary" plain size="small" @click="handlePrint">
                                                        打印</el-button>
                                                    <el-button type="primary" plain size="small"
                                                        @click="handleViewjktz">
                                                        预览</el-button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-row" style="min-width: 100%">
                                        <div class="row-key">备注说明</div>
                                        <div class="row-value">
                                            {{ detailData.remark }}
                                        </div>
                                    </div>
                                </div>
                                <!-- 已缴款信息显示 -->
                                <div class="moudle-content" v-if="[9].includes(detailData.payStatus)">
                                    <div class="table-row">
                                        <div class="row-key">交易缴款人</div>
                                        <div class="row-value">{{ detailData.signupName }}</div>
                                    </div>
                                    <div class="table-row" v-if="['0', '1', '7'].includes(detailData.payType + '')"
                                        style="min-width: 100%">
                                        <div class="row-key">缴款凭证</div>
                                        <div class="row-value" style="min-width: calc(100% - 175px)">
                                            <ndbUpload2 v-model="jkpzFileList" :disabled="true"
                                                :uploadParams="{ busId: detailData.batchFlag == 1 ? checkList.sort().join(',') : detailData.feeId, configFileId: 'RJPZ' }" />
                                        </div>
                                    </div>
                                    <!-- 是否批量缴款 1是 -->
                                    <div class="table-row" style="min-width: 100%" v-if="detailData.batchFlag == 1">
                                        <div class="row-key">费用类型</div>
                                        <div class="row-value">
                                            <span>{{ detailData.costFeeDetailStr }} </span>
                                        </div>
                                    </div>
                                    <div class="table-row" style="min-width: 100%">
                                        <div class="row-key">实缴金额（元）</div>
                                        <div class="row-value">
                                            {{ detailData.trueTotalAmount }}
                                            <!-- <span v-if="detailData.batchFlag == 1" v-for="(item, index) in detailData.costInFeeDetailVoList"> {{ item.feeTypeName + item.needPayAmount + '元' }}</span> -->
                                        </div>
                                    </div>

                                    <div class="table-row">
                                        <div class="row-key">支付方式</div>
                                        <div class="row-value">
                                            {{ detailData.payTypeName }}
                                        </div>
                                    </div>
                                    <div class="table-row">
                                        <div class="row-key">到账时间</div>
                                        <div class="row-value">{{ detailData.secondTime }}</div>
                                    </div>
                                    <div class="table-row" v-if="[2].includes(detailData.payType)">
                                        <div class="row-key">缴款账号</div>
                                        <div class="row-value">{{ detailData.virtualAccount }}</div>
                                    </div>
                                    <div class="table-row" v-if="[2].includes(detailData.payType)">
                                        <div class="row-key">收款银行</div>
                                        <div class="row-value">{{ detailData.bankName }}</div>
                                    </div>
                                    <div class="table-row" v-if="[2].includes(detailData.payType)">
                                        <div class="row-key">收款账号</div>
                                        <div class="row-value">{{ detailData.bankAccount }}</div>
                                    </div>
                                    <div class="table-row" style="min-width: 100%">
                                        <div class="row-key">备注说明</div>
                                        <div class="row-value">
                                            {{ detailData.remark }}
                                        </div>
                                    </div>
                                </div>
                                <!-- 过期未缴款信息展示 -->
                                <div class="moudle-content" v-if="[10].includes(detailData.payStatus)">
                                    <div class="table-row">
                                        <div class="row-key">缴款截止时间</div>
                                        <div class="row-value">{{ detailData.latePayTime || '--' }}</div>
                                    </div>
                                    <div v-if="detailData.orderId" class="table-row" style="min-width: 100%">
                                        <div class="row-key">已选支付方式</div>
                                        <div class="row-value">
                                            {{ detailData.payTypeName }}
                                        </div>
                                    </div>
                                    <div v-if="detailData.orderId && detailData.payType == 2" class="table-row" style="min-width: 100%">
                                        <div class="row-key">缴款通知单</div>
                                        <div class="row-value" style="min-width: calc(100% - 175px)">
                                            <div
                                                style="display: flex; justify-content: space-between; flex: 1; align-items: center">
                                                <el-button link type="primary" @click="handleViewjktz">
                                                    缴款通知单</el-button>
                                                <div>
                                                    <el-button type="primary" plain size="small" @click="handlePrint">
                                                        打印</el-button>
                                                    <el-button type="primary" plain size="small"
                                                        @click="handleViewjktz">
                                                        预览</el-button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="detailData.orderId" class="table-row" style="min-width: 100%">
                                        <div class="row-key">选择支付方式时间</div>
                                        <div class="row-value">
                                            {{ detailData.opreateTime }}
                                        </div>
                                    </div>
                                    <div v-if="detailData.orderId" class="table-row" style="min-width: 100%">
                                        <div class="row-key">备注说明</div>
                                        <div class="row-value">
                                            {{ detailData.remark }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 右侧：收款流程 -->
                    <div class="right-panel" v-if="showFlowPanel">
                        <div class="flow-container">
                            <payment-flow-component ref="paymentFlowRef"
                                :fee-id="isSingle === 2 ? checkList.join(',') : detailData.feeId"></payment-flow-component>
                        </div>
                    </div>
                </div>
            </div>
        </el-scrollbar>
        <template #footer>
            <!-- 交款入账中才显示【更换缴款方式】按钮 -->
            <nd-button type="primary" icon="Switch" @click="handleReplace"
                v-if="[2].includes(detailData.payStatus)">更换缴款方式</nd-button>
            <!--待缴款状态才显示【确认】按钮 -->
            <nd-button type="primary" icon="Check" @click="handleSubmit"
                v-if="[1].includes(detailData.payStatus) && !isReplacePay">确认</nd-button>
            <nd-button icon="Close" @click="close"
                v-if="[1, 2].includes(detailData.payStatus) && !isReplacePay">关闭</nd-button>
            <!-- 更换缴款方式的操作 -->
            <nd-button type="primary" icon="Check" @click="handleReplaceSubmit" v-if="isReplacePay">确认</nd-button>
            <nd-button icon="Close" @click="handleReplaceClose" v-if="isReplacePay">关闭</nd-button>
        </template>
    </nd-dialog>
    <jktzdDialog ref="jktzdDialogRef" @refreshType="refreshType" />
    <jhPayDialog ref="jhPayDialogRef" @refreshType="refreshType" />
    <codePayDialog ref="codePayDialogRef" @refreshType="refreshType" />
    <posPayDialog ref="posPayDialogRef" @refreshType="refreshType" />
    <szrmbPayDialog ref="szrmbPayDialogRef" @refreshType="refreshType" />
</template>
<script setup>
import { ref, watch, inject, onMounted, computed, nextTick, toRaw, h, reactive } from 'vue'
import ndDialog from '@/components/ndDialog.vue'
import ndButton from '@/components/ndButton.vue'
import ndSelect from '@/components/ndSelect.vue'
import ndbUpload2 from '@/components/business/ndbUpload2/index.vue'

import jktzdDialog from './jktzdDialog.vue'
import jhPayDialog from './jhPayDialog.vue'
import codePayDialog from './codePayDialog.vue'
import posPayDialog from './posPayDialog.vue'
import szrmbPayDialog from './szrmbPayDialog.vue'
import encrypt from './encrypt.vue'
import paymentFlowComponent from './paymentFlowComponent.vue'

import { ElMessage, ElMessageBox } from 'element-plus'

const $axios = inject('$axios')

const pageType = 2 // 1: 待缴款 2：缴款入账中
const isSingle = ref(1) // 1: 单笔缴款 2：批量缴款(此状态用于未生成订单状态的区分)
const isReplacePay = ref(false) //是否是更换缴款页面状态
const showFlowPanel = ref(false) //是否显示右侧收款流程面板
const paymentFlowRef = ref(null) // 流程组件引用

const detailData = ref({ costInFeeDetailVoList: {} }) //页面信息

const jkpzFileList = ref([]) //附件

const emits = defineEmits(['refreshData'])

// 查询附件
watch(detailData, (val) => {
    $axios({
        url: '/file/getFileCommon',
        method: 'get',
        data: {
            // busId: val.payVoucher || 'xzx',
            busId: val.feeIds || 'xzx',
            configFileId: 'RJPZ',
        },
    }).then((res) => {
        if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        jkpzFileList.value = res.data.data || []
    })
})

let preParams = {}

onMounted(() => {
    // open()
})

const checkList = ref([]) //批量缴款选中的费用

watch(
    checkList,
    (val) => {
        if (val.length) {
            $axios({
                url: '/file/getFileCommon',
                method: 'get',
                data: {
                    busId: val.sort().join(',') || 'xzxz',
                    configFileId: 'RJPZ',
                },
            }).then((res) => {
                if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                jkpzFileList.value = res.data.data || []
            })

            let count = 0
            detailData.value.costInFeeDetailVoList.forEach((item) => {
                val.forEach((citem) => {
                    if (item.feeId == citem) {
                        count += item.needPayAmount
                    }
                })
            })
            groupPayfee.value = count

            // 在checkList变化时，使用当前勾选的费用ID调用getNoPayFlow接口
            if (isSingle.value === 2) {
                checkFlowData(val.join(','));
            }
        }
        //批量缴款 // 每勾选一次就重新获取支付方式
        let params = {
            feeId: val.join(','),
            proId: preParams.proId,
        }

        $axios({
            url: '/bankIncome/getPayTypeByParam',
            method: 'get',
            data: params,
        }).then((res) => {
            // if (res.data.code != 200) return ElMessage.warning(res.data.msg)
            payTypes.value = res.data.data || []
        })

        validateForm.checkList = val || []
    },
    { deep: true }
)

const dialogRef = ref(null)
const formRef = ref(null)

// @params type=1 单笔缴款  type=2 批量缴款 type=3 查看状态
function open(params, type = 1) {
    isSingle.value = type
    dialogRef.value.open()
    if (type == 1) getProData(params)
    if (type == 2) {
        detailData.value = params
        checkList.value.push(params.costInFeeDetailVoList[0].feeId) //批量缴款默认选中第一个
        // 不需要额外调用checkFlowData，因为checkList的变化会触发watch监听器
    }
    if (type == 3) getCostPayDetail(params)

    getPayTypes(params)

    // 检查是否有流程数据（单笔缴款时）
    if (type !== 2) {
        checkFlowData(params.feeId)
    }

    preParams = JSON.parse(JSON.stringify(params)) //拷贝上个页面的原始值备用

    nextTick(() => {
        setTimeout(() => {
            formRef.value.resetFields()
        }, 150)
    })
}

function refreshType() {
    // 确保使用正确的参数
    const params = {
        ...preParams,
        feeId: isSingle.value == 2 ? checkList.value.join(',') : preParams.feeId
    }
    console.log('刷新参数:', params)

    getCostPayDetail(params)
}

function close() {
    formRef.value.resetFields()

    isReplacePay.value = false
    checkList.value = []

    dialogRef.value.close()

    emits('refreshData')
}

// 计算批量缴款用户选择缴款项总金额
const totalM = computed(() => {
    let count = 0
    detailData.value.costInFeeDetailVoList.forEach((item) => {
        checkList.value.forEach((citem) => {
            if (citem == item.feeId) {
                count += item.needPayAmount - 0
            }
        })
    })
    return formatNum(count)
})

// 处理金额保存两位小数，不足补0
function formatNum(num) {
    var value = Math.round(parseFloat(num) * 100) / 100
    var arrayNum = value.toString().split('.')
    if (arrayNum.length == 1) {
        return value.toString() + '.00'
    }
    if (arrayNum.length > 1) {
        if (arrayNum[1].length < 2) {
            return value.toString() + '0'
        }
        return value
    }
}

// 支付下拉是否可选
let groupPayfee = ref(0)

function isCheckPayType(item) {
    if (!item.amount) return false
    //区分批量和单笔
    if (isSingle.value == 1) {
        //单笔
        if (item.amount - 0 < detailData.value.needTotalAmount - 0) return true
    }
    if (isSingle.value == 2) {
        //批量
        if (item.amount - 0 < groupPayfee.value - 0) return true
    }
    return false
}

// 支付方式名称映射
const payNameMap = {
    0: '备用缴款方式',
    1: '现金支付',
    2: '转账汇款',
    3: '聚合支付',
    4: '扫码支付',
    5: 'POS机刷卡',
    6: '数字人民币支付',
    7: '交易方自行结算',
    8: '投标保证金转入',
    9: '紫金保险购入',
    10: '履约保证金转入',
}

const jhPayDialogRef = ref()
const codePayDialogRef = ref()
const posPayDialogRef = ref()
const szrmbPayDialogRef = ref()

const validateForm = reactive({
    checkList: [],
    payType: '',
    required: isSingle.value == 2 ? true : false, //批量支付的时候才需要勾选费用类型，单笔缴纳不需要勾选，也不做费用类型字段校验
    validCheckList: (rule, value, callback) => {
        if (!value || !value.length) callback(new Error('请选择要缴款的费用！'))
        else callback()
    },
    jkpzFileList: [],
    required2: ['0', '1', '7'].includes(detailData.value.payType + ''),
    validCheckjkpzFileList: (rule, value, callback) => {
        if (!value || !value.length) callback(new Error('请上传缴款凭证！'))
        else callback()
    },
})

function payTypeChange(val) {
    validateForm.payType = val || ''
}

watch(
    () => jkpzFileList.value,
    (val) => {
        validateForm.jkpzFileList = val || []
    },
    {
        deep: true,
        immediate: true,
    }
)

// 提交需要区分单笔还是批量
function handleSubmit() {
    // 批量支付需要组装选中的 feeid
    let feeId = isSingle.value == 2 ? checkList.value.join(',') : detailData.value.feeId

    // 调试打印，确认使用的是勾选的ID
    console.log('提交使用的feeId:', feeId, '勾选的ID:', checkList.value)
    formRef.value.validate((valid) => {
        if (!valid) return
        // 校验必填项
        if (!feeId) return ElMessage.warning('请选择要缴款的费用！')
        if (!detailData.value.payType) return ElMessage.warning('请选择支付方式！')
        if (['0', '1', '7'].includes(detailData.value.payType + '')) {
            if (!jkpzFileList.value.length) return ElMessage.warning('请上传缴款凭证！')
        }

        let params = {
            feeId: feeId,
            payType: detailData.value.payType,
            paymentVoucher: '',
            remark: detailData.value.remarks || '',
            proId: detailData.value.proId,
        }

        //非银行支付方式需要二次确认后再调支付接口(其他支付方式：0、现金支付:1、交易方自行结算:7、投标保证金转入:8、履约保证金转入:10)
        if (['0', '1', '7', '8', '10'].includes(detailData.value.payType)) {
            // ElMessageBox.confirm(`是否确认使用[${payNameMap[detailData.value.payType]}]?确认后缴款状态会变成已缴款!`, '提示', {
            //     confirmButtonText: '确认',
            //     cancelButtonText: '取消',
            // })
            ElMessageBox({
                title: '提示',
                message: h('p', { style: 'width:580px' }, [h('span', { style: 'color:#000' }, `是否确认使用【${payNameMap[detailData.value.payType]}】?确认后缴款状态会变成已缴款!`)]),
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                appendTo: '.xz-box',
            }).then(() => {
                $axios({
                    url: '/bankIncome/paySubmit',
                    method: 'post',
                    data: params,
                }).then((res) => {
                    if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                    getCostPayDetail(params)
                })
            })
        } else if (['2'].includes(detailData.value.payType)) {
            //转账汇款
            $axios({
                url: '/bankIncome/paySubmit',
                method: 'post',
                data: params,
            }).then((res) => {
                if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                getCostPayDetail(params, true)
            })
        } else {
            //  3聚合支付 4扫码支付 5POS机刷卡 6数字人民币支付  9紫金保险购入
            if (detailData.value.payType == 4) {
                $axios({
                    url: '/bankIncome/paySubmit',
                    method: 'post',
                    data: params,
                }).then((res) => {
                    if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                    codePayDialogRef.value.open(res.data.data)
                })
            } else if (detailData.value.payType == 3) {
                $axios({
                    url: '/bankIncome/paySubmit',
                    method: 'post',
                    data: params,
                }).then((res) => {
                    if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                    jhPayDialogRef.value.open(res.data.data)
                })
            } else if (detailData.value.payType == 6) {
                $axios({
                    url: '/bankIncome/paySubmit',
                    method: 'post',
                    data: params,
                }).then((res) => {
                    if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                    szrmbPayDialogRef.value.open(res.data.data)
                })
            } else if (detailData.value.payType == 5) {
                $axios({
                    url: '/bankIncome/paySubmit',
                    method: 'post',
                    data: params,
                }).then((res) => {
                    if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                    posPayDialog.value.open(res.data.data)
                })
            }
        }
    })
}

const jktzdDialogRef = ref()
// 查看缴款通知单
function handleViewjktz() {
    jktzdDialogRef.value.open(detailData.value)
}

function handlePrint() {
    // jktzdDialogRef.value.open(detailData.value)
    jktzdDialogRef.value.handlePrint(detailData.value)
}

// 查询提交后的详情
//  @params isShowTzd  是否需要弹出缴款通知单
function getCostPayDetail({ feeId, proId }, isShowTzd) {
    // feeId需要区分批量还是单笔
    const requestFeeId = isSingle.value === 2 ? checkList.value.join(',') : feeId;

    $axios({
        url: '/bankIncome/costPayDetail',
        method: 'get',
        data: { feeId: requestFeeId, proId },
    }).then((res) => {
        if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        detailData.value = res.data.data || {}

        // 检查是否有流程数据
        checkFlowData(requestFeeId)

        if (isShowTzd) handleViewjktz()
    })
}

// 检查是否有流程数据
// 获取所有费用ID
function getAllFeeIds() {
    if (detailData.value && detailData.value.costInFeeDetailVoList) {
        return detailData.value.costInFeeDetailVoList.map(item => item.feeId).join(',');
    }
    return '';
}

function checkFlowData(feeId) {
    if (!feeId) {
        showFlowPanel.value = false;
        return;
    }

    // 调用API检查是否有流程数据，决定是否显示右侧面板
    $axios({
        url: '/bankIncome/getNoPayFlow',
        method: 'get',
        data: { feeId: feeId },
    }).then((res) => {
        if (res.data.code == 200) {
            // 只有存在数据时才显示面板
            showFlowPanel.value = res.data.data && Array.isArray(res.data.data) && res.data.data.length > 0;
        } else {
            showFlowPanel.value = false;
        }
    }).catch(() => {
        showFlowPanel.value = false;
    });
}

// 获取单个缴款详情
function getProData({ feeId, proId }) {
    $axios({
        url: '/bankIncome/costPayOneDetail',
        method: 'get',
        data: { feeId, proId },
    }).then((res) => {
        if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        detailData.value = res.data.data || {}
    })
}

const payTypes = ref([])

// 获取支付方式
function getPayTypes({ feeId, payType, proId, costInFeeDetailVoList }) {
    // 区分批量缴款和单个缴款
    let feeList = costInFeeDetailVoList
    let params = {}

    if (!feeList) {
        params = {
            feeId,
            // oldPayType: payType,
            proId,
        }
    } else {
        // 批量缴款
        params = {
            feeId: checkList.value.join(','), // 使用已勾选的费用ID
            proId,
        }
    }

    $axios({
        url: '/bankIncome/getPayTypeByParam',
        method: 'get',
        data: params,
    }).then((res) => {
        // if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        payTypes.value = res.data.data || []
    })
}

function handlePostMessage() {
    let params = {
        orderId: detailData.value.orderId,
        signupId: detailData.value.signupId,
        type: '8',
    }

    $axios({
        url: '/bankIncome/sendPhoneMessage',
        method: 'post',
        data: params,
    }).then((res) => {
        if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        // detailData.value = res.data.data || {}
        ElMessage.success('短信发送成功！')
    })
}

// 更换缴款方式
function handleReplace() {
    // 切换页面状态
    detailData.value.payType = ''
    detailData.value.payStatus = 1
    isReplacePay.value = true
    // 重新加载缴款下拉列表
    let params = {
        oldPayType: detailData.value.payType,
        proId: detailData.value.proId,
        feeId: detailData.value.feeId,
    }

    $axios({
        url: '/bankIncome/getPayTypeByParam',
        method: 'get',
        data: params,
    }).then((res) => {
        // if (res.data.code != 200) return ElMessage.warning(res.data.msg)
        payTypes.value = res.data.data || []
        detailData.value.payType = ''
    })
}

//"feeId": detailData.value.feeId,
//"payType": detailData.value.payType,
//"paymentVoucher": "",
//"remark": detailData.value.remarks
// 补丁：添加表单校验

// 更换缴款方式后的提交
function handleReplaceSubmit() {
    // 批量支付需要组装选中的 feeid
    let feeId = isSingle.value == 2 ? checkList.value.join(',') : detailData.value.feeId

    formRef.value.validate((valid) => {
        console.log(valid)
        if (!valid) return

        // 校验必填项
        if (!feeId) return ElMessage.warning('请选择要缴款的费用！')
        if (!detailData.value.payType) return ElMessage.warning('请选择支付方式！')
        if (['0', '1', '7'].includes(detailData.value.payType + '')) {
            if (!jkpzFileList.value.length) return ElMessage.warning('请上传缴款凭证！')
        }

        let params = {
            feeId: feeId,
            payType: detailData.value.payType,
            paymentVoucher: '',
            remark: detailData.value.remarks || '',
            proId: detailData.value.proId,
        }

        // 弹窗提示
        ElMessageBox({
            title: '温馨提示',
            message: h('p', { style: 'width:580px' }, [
                h('span', { style: 'color:#000' }, '为防止重复缴费，请先与缴款人沟通确认其是否已进行转账操作！'),
                h('br', null, ''),
                h('span', { style: 'color:#000' }, '若其已进行转账 (但资金暂未到账)，请勿变更缴款方式，否则将导致重复缴款且无法退款!'),
                h('br', { style: 'color:#000' }, ''),
                h('span', { style: 'color:#000' }, '若已与缴款人确认需变更缴款方式，点击【确定】继续操作，否则请点击【取消】'),
            ]),
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            appendTo: '.xz-box',
        }).then(() => {
            //非银行支付方式需要二次确认后再调支付接口(现金支付:1、交易方自行结算:7、投标保证金转入:8、履约保证金转入:10)
            if (['0', '1', '7', '8', '10'].includes(detailData.value.payType)) {
                // ElMessageBox.confirm(`是否确认使用[${payNameMap[detailData.value.payType]}]?确认后缴款状态会变成已缴款!`, '提示', {
                //     confirmButtonText: '确认',
                //     cancelButtonText: '取消',
                // })
                ElMessageBox({
                    title: '提示',
                    message: h('p', { style: 'width:580px' }, [h('span', { style: 'color:#000' }, `是否确认使用【${payNameMap[detailData.value.payType]}】?确认后缴款状态会变成已缴款!`)]),
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    appendTo: '.xz-box',
                }).then(() => {
                    $axios({
                        url: '/bankIncome/changePayTypeSubmit',
                        method: 'post',
                        data: params,
                    }).then((res) => {
                        if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                        getCostPayDetail(params)
                    })
                })
            } else {
                $axios({
                    url: '/bankIncome/changePayTypeSubmit',
                    method: 'post',
                    data: params,
                }).then((res) => {
                    if (res.data.code != 200) return ElMessage.warning(res.data.msg)
                    if (['2'].includes(detailData.value.payType)) {
                        getCostPayDetail(params, true)
                        isReplacePay.value = false
                    } else {
                        isReplacePay.value = false
                        //  3聚合支付 4扫码支付 5POS机刷卡 6数字人民币支付  9紫金保险购入
                        if (detailData.value.payType == 4) {
                            codePayDialogRef.value.open(res.data.data)
                        } else if (detailData.value.payType == 3) {
                            // console.log('聚合支付')
                            // console.log(res.data.data)
                            jhPayDialogRef.value.open(res.data.data)
                        } else if (detailData.value.payType == 6) {
                            szrmbPayDialogRef.value.open(res.data.data)
                        } else if (detailData.value.payType == 5) {
                            posPayDialog.value.open(res.data.data)
                        }
                    }
                })
            }
        })
    })
}
// 更换缴款方式后的取消
function handleReplaceClose() {
    isReplacePay.value = false
    detailData.value.payStatus = 2
    detailData.value.payType = preParams.payType + ''
    // open(preParams.value)
}

// 查看付款码
function handleViewQrcode() {
    codePayDialogRef.value.open(detailData.value)
}
// 查看聚合支付详情
function handleViewJhPay() {
    jhPayDialogRef.value.open(detailData.value)
}

// 查看pos机收款码
function handleViewPos() {
    posPayDialogRef.value.open(detailData.value)
}

// 查看数字人民币付款码
function handleViewSzrmb() {
    szrmbPayDialogRef.value.open(detailData.value)
}

//数字转大写
function convertCurrency(money) {
    // 汉字的数字
    let cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
    // 基本单位
    let cnIntRadice = ['', '拾', '佰', '仟']
    // 对应整数部分扩展单位
    let cnIntUnits = ['', '万', '亿', '兆']
    // 对应小数部分单位
    let cnDecUnits = ['角', '分', '毫', '厘']
    // 整数金额时后面跟的字符
    let cnInteger = '整'
    // 整型完以后的单位
    let cnIntLast = '元'
    // 最大处理的数字
    let maxNum = 999999999999.9999

    // 输出的中文金额字符串
    let chineseStr = ''

    if (money === '') {
        return ''
    }

    money = parseFloat(money)

    if (money >= maxNum) {
        // 超出最大处理数字
        return ''
    }

    if (money === 0) {
        return cnNums[0] + cnIntLast + cnInteger
    }

    // 转换为字符串
    money = money.toString()
    let [integerNum, decimalNum] = money.split('.')

    // 处理整数部分
    if (parseInt(integerNum, 10) > 0) {
        let zeroCount = 0
        const IntLen = integerNum.length

        for (let i = 0; i < IntLen; i++) {
            let n = integerNum.charAt(i)
            let p = IntLen - i - 1
            let q = Math.floor(p / 4)
            let m = p % 4

            if (n === '0') {
                zeroCount++
            } else {
                if (zeroCount > 0) {
                    chineseStr += cnNums[0] // 添加零
                }
                zeroCount = 0 // 归零
                chineseStr += cnNums[parseInt(n, 10)] + cnIntRadice[m]
            }
            if (m === 0 && zeroCount < 4) {
                chineseStr += cnIntUnits[q]
            }
        }
        chineseStr += cnIntLast // 添加元
    }

    // 处理小数部分
    if (decimalNum) {
        const decLen = decimalNum.length > 4 ? 4 : decimalNum.length // 限制小数位数最多4位
        for (let i = 0; i < decLen; i++) {
            let n = decimalNum.charAt(i)
            if (n !== '0') {
                chineseStr += cnNums[Number(n)] + cnDecUnits[i]
            }
        }
    }

    // 处理没有小数部分的情况
    if (chineseStr === '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger
    } else if (decimalNum === undefined || decimalNum === '') {
        chineseStr += cnInteger // 添加整
    }

    return chineseStr
}
defineExpose({
    open,
})
</script>
<style lang="scss" scoped>
:deep(.xz-box .el-message-box) {
    max-width: 580px !important;
}

:deep(.el-textarea .el-textarea__inner) {
    // 然后找到对应的类名，在这里将拉伸去掉即可
    resize: none;
}

:deep(.el-form-item) {
    margin-top: 12px;
    margin-bottom: 12px;
}

// 左右分栏布局样式
.split-layout {
    display: flex;
    flex-direction: row;
    gap: 20px;
    width: 100%;

    .left-panel {
        flex: 7;
        min-width: 0;
        position: relative;
    }

    .right-panel {
        flex: 3;
        min-width: 0;
        border-left: 1px solid #EBEEF5;
        padding-left: 20px;
        margin-top: 10px;
        max-height: calc(65vh - 80px);
        /* 限制高度，保留页眉页脚空间 */
        overflow-y: auto;
        /* 内容超出时显示滚动条 */

        .flow-container {
            padding-right: 10px;
            /* 为滚动条预留空间 */
        }
    }
}

.red-start::before {
    content: '*';
    position: absolute;
    color: red;
    display: inline-block;
    width: 10px;
    height: 10px;
    right: 38%;
    top: 50%;
    transform: translateY(-6px);
}

.wrap-container {
    width: 100%;
    height: 100%;

    .moudle-box {
        padding: 0 5px;

        .moudle-title {
            font-size: 14px;
            color: #0b8df1;
            margin: 10px 0;
            font-weight: 700;
        }

        .moudle-content {
            color: #555;
            font-size: 14px;
            display: flex;
            flex-wrap: wrap;
            width: 100%;
            border: 1px solid #eee;
            border-bottom: none;
            border-right: none;

            .table-row {
                display: flex;
                min-height: 34px;
                border-bottom: 1px solid #eee;
                border-right: 1px solid #eee;
                min-width: 50%;
                flex: 1;
                position: relative;
                padding-right: 30px;

                .row-key,
                .row-value {
                    height: 100%;
                    min-height: inherit;
                    display: flex;
                    align-items: center;
                    padding: 8px;
                    color: #444;
                    word-break: break-all;
                }

                .row-key {
                    color: #606266;
                    text-align: right;
                    justify-content: end;
                    border-right: 1px solid #eee;
                    background: #f9f9f9;
                    min-width: 175px;
                    position: relative;
                }
            }
        }
    }
}
</style>
