<template>
  <div class="nd-search-more-item-box">
    <div class="left-box"  :class="letterSpacing ? 'letter-spacing' : ''">
    <!-- <div class="left-box" :style="{ width: width }" :class="letterSpacing ? 'letter-spacing' : ''"> -->
      <el-icon v-if="assembly == true" :size="16">
        <Box />
      </el-icon>
      <span :class="requires ? 'require' : ''">{{ title }}</span>
      <i  style="font-size: 14px; color: #0B8DF1;cursor:pointer;font-style: normal;margin-left: 10px;" @click="toggle"  v-if="paramKey=='name' && titleBtn&&title=='项目名称'">{{projectButtonTitle}}</i> <span v-if="title=='支付方式'" style="display: flex;align-items: center;font-size: 12px;color: #666; padding-left: 3px;">（会员web端仅支持转账汇款、扫码支付和数字人民币支付；会员移动端仅支持转账汇款）</span>
    </div>
    <div class="right-box" :style="{ width: 'calc(100% - ' + width + ')' }">
      <slot></slot>
    </div>
    <div class="bottom-box" v-if="requires==1 &&(!itemValue&&itemValue!==0) &&saveOperate">{{title}}不能为空</div>
    <!-- <div class="bottom-box" v-if="requires==1 &&!itemValue &&saveOperate">{{title}}不能为空</div> -->
    <div class="bottom-box" v-if="title2&&saveOperate&&itemValue">{{dictType=='telephone'?title2+'格式错误':'证件号码格式不正确，请检查证件号码与证件类型是否一致'}}</div>
    <!-- <div class="bottom-box" v-if="title2&&saveOperate">{{title2}}格式错误</div> -->
    <!-- <div class="bottom-box" v-if="telRuleTip">{{title}}格式错误</div> -->
    <!-- <div class="bottom-box" v-if="requires==1 &&!itemValue &&saveOperate">{{title}}不能为空</div> -->
  </div>
</template>

<script  setup>
import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
let props = defineProps({
  dictType: {
    type: String,
    default: "default",
  },
  title: {
      type: String,
      default: "未命名",
    },
    paramKey: {
      type: String,
      default: "未命名",
    },
    titleBtn: {
    type: Boolean,
    default: false,
  },
    title2: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "20px",
    },
    requires: {
      type: Boolean,
      default: false,
    },
    assembly: {
      type: Boolean,
      default: false,
    },
    itemValue: {
      type: String,
      default: null,
    },
    saveOperate: {//保存触发
      type: Boolean,
      default: false,
    },
});
let projectButtonTitle=ref('自定义项目名称')
const setProjectName = inject("setProjectName");
const toggleFun = inject("toggle");

let toggle=()=>{
  toggleFun()
  projectButtonTitle.value=projectButtonTitle.value=='自动生成项目名称'?'自定义项目名称':'自动生成项目名称'
  // if(projectButtonTitle.value=='自动生成项目名称'){
  //   setProjectName(1)
  // }else{
  //   setProjectName(2)
  // }
}
// const setProjectName = inject("setProjectName");
// export default {
//   props: {
//     title: {
//       type: String,
//       default: "未命名",
//     },
//     paramKey: {
//       type: String,
//       default: "未命名",
//     },
//     title2: {
//       type: String,
//       default: "",
//     },
//     width: {
//       type: String,
//       default: "20px",
//     },
//     requires: {
//       type: Boolean,
//       default: false,
//     },
//     assembly: {
//       type: Boolean,
//       default: false,
//     },
//     itemValue: {
//       type: String,
//       default: null,
//     },
//     saveOperate: {//保存触发
//       type: Boolean,
//       default: false,
//     },
//   },
//   data() {
//     return {
//       letterSpacing: false,
//       projectButtonTitle:'自定义项目名称'
//     };
//   },
//   methods: {
//     toggle(){
//       this.projectButtonTitle=this.projectButtonTitle=='自动生成项目名称'?'自定义项目名称':'自动生成项目名称'
//       if(this.projectButtonTitle=='自动生成项目名称'){
//         // setProjectName(1)
//       }else{
//         // setProjectName(2)
//       }
//     }
//   }
// };
</script>

<style scoped lang="scss">
.nd-search-more-item-box {
  width: 33.33%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  // align-items: center;
  margin-bottom: 10px;

  .left-box {
    // width: 58px;
    height: 100%;
    min-width: 120px;
    display: flex;
    flex-direction: row;
    // justify-content: flex-end;
    align-items: center;
    padding-right: 10px;
    font-size: 14px;
    color: #555555;
  }

  .letter-spacing {
    letter-spacing: -1.03px;
  }

  .right-box {
    // width: calc(100% - 58px);
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    margin-top: 5px;

    :deep(.nd-input-box) {
      width: 100% !important;
    }

    :deep(.nd-select-box) {
      width: 100% !important;
    }

    :deep(.nd-date-picker-box) {
      width: 100% !important;
    }

    :deep(.nd-tree-select-box) {
      width: 100% !important;
    }
  }
  .bottom-box{
    color: red;
    font-size: 12px;
  }
  // 是否必填
  .require::before {
    content: "* ";
    color: red;
  }
}
</style>