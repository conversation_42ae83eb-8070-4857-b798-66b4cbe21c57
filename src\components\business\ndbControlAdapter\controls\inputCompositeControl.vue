<template>
    <!-- input 复合型文本类型 -->

    <nd-search-more-item :requires="requireFlag"
    :itemValue="inputValue" :saveOperate="saveOperate" :class="requireFlag == 1 ? 'set-up-class' : ''" :title="title" :style="{ width: width }"
        v-if="type == 11">
        <!-- <nd-input :maxLength="maxLength" :disabled="disabled" v-model="inputValue" :placeholder="disabled == true ? '' : placeholder" width="90%"
            clearable></nd-input> -->
            <ndInput
            :maxLength="maxLength" @input="changeInp" v-if="dataType == 2" :formatter="priceFormat2" :parser="priceFormat2" :disabled="disabled" v-model="inputValue" :placeholder="disabled == true ? '' : placeholder" width="90%"
            clearable
            >
            </ndInput>
            <ndInput
            :maxLength="maxLength" @input="changeInp" v-if="dataType == 3" :formatter="priceFormat" :parser="priceFormat" :disabled="disabled" v-model="inputValue" :placeholder="disabled == true ? '' : placeholder" width="90%"
            clearable
            >
            </ndInput>
            <ndInput
            :maxLength="maxLength" v-if="dataType != 2 &&dataType != 3"  :disabled="disabled" v-model="inputValue" :placeholder="disabled == true ? '' : placeholder" width="90%"
            clearable
            >
            </ndInput>
            <nd-select :teleported="true" @change="changeSelect" style="margin-left: 5px;"
            :disabled="disabled" filterable
            placeholder=""  width="90%" v-model="inputValue2" >
            <el-option v-for="item in dataArry" :key="item.dataKey" :label="item.dataValue" :value="item.dataKey" />
            </nd-select>
    </nd-search-more-item>
    <!-- input 复合型文本类型 -->
</template>
  
<script setup>
// 导入公共组件
import ndInput from "@/components/ndInput.vue";
import ndSearchMoreItem from '@/components/business/ndbControlAdapter/controlItem.vue';
import ndSelect from "@/components/ndSelect.vue";
import ndbInputThousandsSeparator from "@/components/business/ndbInputThousandsSeparator.vue";

import { ElMessage, ElMessageBox } from 'element-plus'

import { onMounted, reactive, ref, inject, watch, getCurrentInstance, nextTick, computed } from 'vue';
const $axios = inject("$axios");
const currentInstance = getCurrentInstance()
const props = defineProps({
    type: {// 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
        type: Number,
        default: "default",
    },
    width: {//宽度
        type: String,
        default: "",
    },
    title: {//标题
        type: String,
        default: "",
    },
    disabled: { // 只读
        type: Boolean,
        default: false,
    },
    requireFlag: { // 是否必填
        type: Number,
        default: 1,//1是0否
    },
    modelValue: {//双向绑定
        type: String,
    },
    modelValue2: {//双向绑定
        type: String,
    },
    jsonData: {
        type: Array,
        default: [],
    },
    dictType: {
        type: String,
        default: "default",
    },
    paramKey: {
        type: String,
        default: "default",
    },
    showFlag: {
        type: Boolean,
        default: true,
    },
    jibie: {
        type: Boolean,
        default: true,
    },
    jibiecun: {
        type: Boolean,
        default: true,
    },
    dataType: {
        type: Number,
        default: 1,//1文本 2整数 3小数 4日期
    },
    maxLength: {//输入长度
        type: String,
        default: '100',
    },
    paramUnit: {
        type: String,
        default: '',
    },
    unitId: {
        type: String,
        default: '',
    },
    isFlag: {
        type: Boolean,
        default: false,
    },
    LZFS: {
        type: String,
        default: '',
    }, objVal: {
        type: Object,
        default: {},
    }, objVal2: {
        type: Object,
        default: {},
    }, projectType: {
        type: Number,
        default: '',
    }, childObj: {
        type: Object,
        default: {},
    },
    placeholder: {
    type: String,
    default: "请输入",
  },
  saveOperate: {//保存触发
      type: Boolean,
      default: false,
    },
})
const emits = defineEmits(["update:modelValue", "update:modelValue2"])//定义方法名

const inputValue = computed({//监听
    get() {
        return props.modelValue
    },
    set(value) {
        emits("update:modelValue", value)
    }
})
const inputValue2 = computed({//监听
    get() {
        return props.modelValue2
    },
    set(value) {
        emits("update:modelValue2", value)
    }
})
const getTree3 = inject("getTree3");
let changeSelect=(e)=>{
    if(props.paramKey=='tradeCount'){
        dataArry.value.forEach(item=>{
            if(e==item.dataKey){
                let val;
                if(props.modelValue==null ||!props.modelValue||props.modelValue=='null'){
                     val=''
                }else{
                    val=props.modelValue
                }
                getTree3(val+item.dataValue)
            }
        })
    }
}
let changeInp=(e)=>{
    if(props.paramKey=='tradeArea'||props.paramKey=='tradeCount'){
        // console.log(e)
        // console.log(props.modelValue2)
        // console.log(dataArry.value)
        dataArry.value.forEach(item=>{
            if(props.modelValue2==item.dataKey){
                // console.log(item.dataValue)
                getTree3(e+item.dataValue)
            }
            // else{
            //     getTree3('')
            // }
        })
    }
}
const selectFun=()=>{
    if(!props.dictType) return;
    $axios({
            method: "get",
            url: "/basecode/getBaseCodeInfo?baseType="+props.dictType
        })
            .then((response) => {
                if (response.data.code == 200) {
                    dataArry.value=response.data.data
                } 
            })
            .catch(() => {

            });
}
const priceFormat = (value, int = 8) => {
    if(props.objVal2.dataLength==12 ||props.objVal2.dataLength=='12'){
        value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < 12 ? index : 12) + value.slice(index)
    } else {
        value = value.slice(0, 12)
    }
    return value
    }else{
        value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // 保证只能输入4个小数  
    value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // 只能8位整数
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
    }
    // value = value.toString();
    // // 先把非数字的都替换掉，除了数字和小数点
    // value = value.replace(/[^\d.]/g, "");
    // // 必须保证第一个为数字而不是小数点
    // value = value.replace(/^\./g, "");
    // // 保证只有出现一个小数点而没有多个小数点
    // value = value.replace(/\.{2,}/g, ".");
    // // 保证小数点只出现一次，而不能出现两次以上
    // value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    // // 保证只能输入4个小数  
    // value = value.replace(/^(\d+)\.(\d{0,4}).*$/, '$1.$2');
    // // 只能8位整数
    // let index = value.indexOf('.')
    // if (index > -1) {
    //     value = value.slice(0, index < int ? index : int) + value.slice(index)
    // } else {
    //     value = value.slice(0, int)
    // }
    // return value
}
const priceFormat2 = (value) => {
    value = value.toString();
    value = value.replace(/[^\w\.\/]/ig, '');
    return value

}
let dataArry=ref([])
watch(() => props.objVal2.optionSet, (newValue, oldValue) => {
    // if(!Array.isArray(props.objVal.optionSet)){
    //     if(props.objVal.optionSet){
    //         props.objVal.optionSet = JSON.parse(props.objVal.optionSet)
    //     }
    // }

    if(props.objVal2.enumType !== 0 && props.objVal2.optionSet){
        if (Array.isArray(props.objVal2.optionSet)){
            dataArry.value=props.objVal2.optionSet
        }else{
            dataArry.value=JSON.parse(props.objVal2.optionSet);
        }


    }else{
        if(props.dictType &&props.type==11){
        selectFun();
      }
    }
}, { deep: true, immediate: true },)

onMounted(() => {
   
})
</script>
  
<style lang='scss' scoped>
.set-up-class {
    :deep(.left-box) {
        span::before {
            content: "*";
            color: red;
        }
    }
}

:deep(.left-box) {
    text-align: right;
    min-width: 150px !important;
}

// :deep(.left-box) {
//   white-space: nowrap; /* 防止文本换行 */
//   overflow: hidden;  /* 超出部分隐藏 */
//   text-overflow: ellipsis; /* 超出部分以省略号表示 */

//   }

:deep(.el-input__wrapper) {
    width: 100%;
}

:deep(.el-select) {
    width: 100%;
}

:deep(.el-autocomplete) {
    width: 100%;
}

:deep(.el-cascader) {
    width: 100%;
}

.nd-input-box :deep(.el-input) {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
    width: 100%;
    // padding-left: 10px;
    padding-right: 10px;
}

:deep(.el-input.is-disabled) {
    width: 100%;
}

:deep(.el-date-editor.el-input) {
    width: 100%;
}
</style>
  