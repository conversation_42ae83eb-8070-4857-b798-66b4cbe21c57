<template>
  <!-- 标的物组件 -->
  <nd-search-more-item
    :class="requireFlag == 1 ? 'set-up-class' : ''"
    :title="title"
    :style="{ width: width }"
    v-if="items.matterFlag == 1"
  >
    <nd-table style="height: 350px; margin-top: 15px" :data="tenderData">
      <el-table-column
        align="center"
        prop="code"
        label="标段编号"
        width="100"
      />
      <el-table-column
        fixed="right"
        align="center"
        prop="status"
        width="150"
        label="操作"
      >
        <template #default="{ row }">
          <el-tooltip effect="light" content="删除">
            <el-button link @click.stop="deleteTender(row)">
              <img
                src="@/assets/images/transactionManageImage/delete.png"
                alt=""
              />
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </nd-table>
  </nd-search-more-item>
  <!-- 标的物组件 -->
</template>
  
<script setup>
// 导入公共组件
import ndSearchMoreItem from "@/components/business/ndbControlAdapter/controlItem.vue";
import ndTable from "@/components/ndTable.vue";

import { ElMessage, ElMessageBox } from "element-plus";

import {
  onMounted,
  reactive,
  ref,
  inject,
  watch,
  getCurrentInstance,
  nextTick,
  computed,
} from "vue";
const $axios = inject("$axios");
const currentInstance = getCurrentInstance();
const props = defineProps({
  type: {
    // 1 文本 ；2 文本域；3 日期控件；4 时间控件；5下拉单选框；6 下拉复选框 7 下拉树； 8 单选框； 9 复选框； 10 文件上传
    type: Number,
    default: "default",
  },
  width: {
    //宽度
    type: String,
    default: "",
  },
  title: {
    //标题
    type: String,
    default: "",
  },
  disabled: {
    // 只读
    type: Boolean,
    default: false,
  },
  requireFlag: {
    // 是否必填
    type: Number,
    default: 1, //1是0否
  },
  modelValue: {
    //双向绑定
    type: String,
  },
  jsonData: {
    type: Array,
    default: [],
  },
  dictType: {
    type: String,
    default: "default",
  },
  paramKey: {
    type: String,
    default: "default",
  },
  showFlag: {
    type: Boolean,
    default: true,
  },
  jibie: {
    type: Boolean,
    default: true,
  },
  jibiecun: {
    type: Boolean,
    default: true,
  },
  dataType: {
    type: Number,
    default: 1, //1文本 2整数 3小数 4日期
  },
  maxLength: {
    //输入长度
    type: String,
    default: "100",
  },
  paramUnit: {
    type: String,
    default: "",
  },
  unitId: {
    type: String,
    default: "",
  },
  isFlag: {
    type: Boolean,
    default: false,
  },
  LZFS: {
    type: String,
    default: "",
  },
  objVal: {
    type: Object,
    default: {},
  },
  projectType: {
    type: Number,
    default: "",
  },
  childObj: {
    type: Object,
    default: {},
  },
  objVal2: {
    type: Object,
    default: {},
  }, items: {
    type: Object,
    default: {},
  }
});
const emits = defineEmits(["update:modelValue"]); //定义方法名
let tenderData=ref([])
onMounted(() => {
    console.log(props.items)
});
</script>
  
<style lang='scss' scoped>
.set-up-class {
  :deep(.left-box) {
    span::before {
      content: "*";
      color: red;
    }
  }
}

:deep(.left-box) {
  text-align: right;
  min-width: 150px !important;
}

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-autocomplete) {
  width: 100%;
}

:deep(.el-cascader) {
  width: 100%;
}

.nd-input-box :deep(.el-input) {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}

.nd-input-box :deep(.el-input__wrapper) {
  width: 100%;
  // padding-left: 10px;
  padding-right: 10px;
}

:deep(.el-input.is-disabled) {
  width: 100%;
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>
  