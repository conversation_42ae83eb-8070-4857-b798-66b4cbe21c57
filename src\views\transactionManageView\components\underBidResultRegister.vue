<template>
    <!-- 结果登记 -->
    <div v-if="show.mianShow" class="result-main-box">
        <div style="padding:15px;">
            <div class="result-box">
                <div>交易结果：</div>
                <div colspan="3">
                    <el-radio-group v-model="resultForm.tradeFlag">
                        <el-radio :label="1">成功</el-radio>
                        <el-radio :label="2">失败</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <el-form ref="formRef" :model="resultForm" :rules="rules">
                <table v-if="resultForm.tradeFlag == 1">
                    <tr>
                        <td>项目名称</td>
                        <td colspan="3">{{ resData.projectName }}</td>
                    </tr>
                    <tr>
                        <td>项目编号</td>
                        <td>
                            {{ resData.projectCode }}
                        </td>
                        <td>标段编号</td>
                        <td>{{ resData.tenderCode }}</td>
                    </tr>
                    <tr>
                        <td>交易底价</td>
                        <td>
                            <span style="margin-right: 70px;">{{ resData.floorPrice + resData.floorPriceUnit}}</span>
                            大写&nbsp;&nbsp;{{ resData.maxFloorPrice }}
                        </td>
                        <td>竞价方式</td>
                        <td>{{ resData.bidTypeName }}</td>
                    </tr>
                    <tr>
                        <td>交易场所</td>
                        <td colspan="3">{{ resData.tradeAddress }}</td>
                    </tr>
                    <tr>
                        <td class="require special">流入方最终有效交易表</td>
                        <td colspan="3" style="padding-top: 10px;padding-bottom: 10px;padding-right: 0;">
                            <nd-table :data="resData.tradeSignupTradeVoList ? resData.tradeSignupTradeVoList : []" height="200" style="width: 99.5%;">
                                <el-table-column align="center" label="序号" type="index" :index="index=>index+1" width="55" />
                                <el-table-column align="center" label="流入方" prop="signupName" />
                                <el-table-column align="center" :label="lastPriceUnit">
                                    <!-- prop="bidPrice" -->
                                    <template #default="{row}">
                                        <nd-input v-if="flag.price" style="width: 100%;"
                                        v-model.trim="row.bidPrice" 
                                        @change="tradeSignupTradeChange(row)"
                                        @blur="inflowChange02(resultForm.signupId,row.bidPrice,row.signupId)"
                                        @input="inflowChange02(resultForm.signupId,row.bidPrice,row.signupId)"
                                        :formatter="paymentListBlur02" :parser="paymentListBlur02"/>
                                        <span v-else>{{ row.bidPrice }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="大写">
                                    <template #default="{row}">
                                        {{ row.bidPriceMax }}
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" label="成交方顺序" prop="sort" />
                            </nd-table>
                        </td>
                    </tr>
                    <tr>
                        <td class="require">成交方</td>
                        <td>
                            <el-form-item prop="signupId">
                                <nd-select v-model="resultForm.signupId" @change="inflowChange" width="100%">
                                    <el-option v-for="item in resData.tradeSignupTradeVoList" 
                                    :label="item.signupName" :value="item.signupId" :key="item.signupId" />
                                </nd-select>
                            </el-form-item>
                        </td>
                        <td  class="require">成交价（{{resData.floorPriceUnit}}）</td>
                        <td colspan="3">
                            <div class="list-box">
                                <nd-input v-model.trim="cjbidPrice" width="30%" disabled/>
                                <div style="padding-left: 10px;">{{bidPriceMax}}</div>
                            </div>
                        </td>
                    </tr>
                     <tr>
                        <td class="require">成交日期</td>
                        <td>
                            <el-form-item prop="tradeTime">
                                <nd-date-picker 
                                style="width: 100%;"
                                type="date"
                                placeholder="请选择日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                                v-model="resultForm.tradeTime"
                                />
                            </el-form-item>
                        </td>
                    </tr>
                    <tr>
                        <td class="require">成交总金额（元）</td>
                        <td>
                            <el-form-item prop="tradeAmount">
                                <nd-input v-model.trim="resultForm.tradeAmount" 
                                :formatter="paymentListBlur02" :parser="paymentListBlur02" 
                                @blur="getServieFee" @change="changePercent"
                                width="100%" />
                            </el-form-item>
                        </td>
                        <td>溢价总金额（元）</td>
                        <td>
                            <nd-input v-model.trim="resultForm.overflowAmount" width="100%" 
                            :formatter="paymentListBlur03" :parser="paymentListBlur03" @change="changeAmount"/>
                        </td>
                    </tr>
                    <!-- <tr>
                        <td>成交价（元）</td>
                        <td colspan="3">
                            <div class="list-box">
                                <nd-input v-model.trim="bidPrice" width="30%" disabled/>
                                <div style="padding-left: 10px;">{{bidPriceMax}}</div>
                            </div>
                        </td>
                    </tr> -->
                    <tr>
                        <td class="require">溢价率（%）</td>
                        <td colspan="3">
                            <div class="list-box">
                                <nd-input v-model.trim="resultForm.overflowScale" width="30%" disabled/>
                                <div class="list-text">已为您自动计算当前项目溢价率，溢价率=溢价总金额/挂牌价总金额*100%</div>
                            </div>
                        </td>
                    </tr>
                    <tr v-if="resData.serviceFeeFlag == 1 && resData.serviceFeeType != 1 && resData.serviceFeeType">
                        <td>服务费收取规则</td>
                        <td>
                            <div style="display: flex;align-items: center;">
                                <span v-if="resData.serviceFeeType == 2 && resData.tradeType == 1" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">按{{resData.serviceFeeRule}}年成交价收取</span>
                                <span v-else-if="resData.serviceFeeType == 2 && resData.tradeType != 1" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">按成交价收取</span>
                                <span v-else-if="resData.serviceFeeType == 3" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">服务费固定金额</span>
    
                                <nd-input v-if="resData.serviceFeeType == 3" v-model.trim="resultForm.serviceFeeRule" width="100%" disabled />
                                
                                <span v-if="resData.serviceFeeType == 3" style="margin: 0 0 0 10px;color: #606266;">元</span>
                            </div>
                        </td>
                        <td class="require">服务费（元）</td>
                        <td>
                            <el-form-item prop="serviceFee">
                                <nd-input v-model.trim="resultForm.serviceFee" width="100%"
                                :formatter="paymentListBlur02" :parser="paymentListBlur02" @change="checkDestinationOptions"/>
                            </el-form-item>
                        </td>
                    </tr>
                    <tr v-if="(resData.serviceFeeFlag == 1 && resData.serviceFeeType != 1 && resData.serviceFeeType) || (resData.perFeeFlag == 1 && resData.perFeeType != 1 && resData.perFeeType)">
                        <template v-if="resData.serviceFeeFlag == 1 && resData.serviceFeeType != 1 && resData.serviceFeeType">
                            <td>服务费截止缴纳时间</td>
                            <td>
                                <el-form-item>
                                    <nd-date-picker 
                                    style="width: 100%;"
                                    type="datetime"
                                    placeholder="请选择时间"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    v-model="resultForm.serviceFeeTime" />
                                </el-form-item>
                            </td>
                        </template>
                        <template v-if="resData.perFeeFlag == 1 && resData.perFeeType != 1 && resData.perFeeType">
                            <td>履约保证金收取规则</td>
                            <td>
                                <div style="display: flex;align-items: center;">
                                    <span v-if="resData.perFeeType == 2 && resData.tradeType == 1" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">收取首年租金的</span>
                                    <span v-if="resData.perFeeType == 2 && resData.tradeType != 1" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">收取成交价的</span>
                                    <span v-else-if="resData.perFeeType == 3" style="margin: 0 10px 0 0;white-space: nowrap;color: #606266;">履约金固定金额</span>
                                    <nd-input v-model.trim="resultForm.perFeeRule" width="100%" disabled />
                                    <span v-if="resData.perFeeType == 2" style="margin: 0 0 0 10px;">%</span>
                                    <span v-else-if="resData.perFeeType == 3" style="margin:0 0 0 10px;">元</span>
                                </div>
                            </td>
                        </template>
                    </tr>
                    <tr v-if="resData.perFeeFlag == 1 && resData.perFeeType != 1 && resData.perFeeType">
                        <td class="require">履约保证金（元）</td>
                        <td>
                            <el-form-item prop="perFee">
                                <nd-input v-model.trim="resultForm.perFee" width="100%" 
                                :formatter="paymentListBlur02" :parser="paymentListBlur02" @change="checkDestinationOptions"/>
                            </el-form-item>
                        </td>
                        <td>履约保证金截止缴纳时间</td>
                        <td>
                            <el-form-item>
                                <nd-date-picker 
                                style="width: 100%;"
                                type="datetime"
                                placeholder="请选择时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                v-model="resultForm.perFeeTime" />
                            </el-form-item>
                        </td>
                    </tr>
                    <tr>
                        <td :class="{'require': resultForm.tradeFlag == 1 && (resData && resData.marginFee > 0)}">投标保证金去向</td>
                        <td colspan="3">
                            <div class="list-box">
                                <el-form-item prop="tenderDepositDestination" style="margin-bottom: 0;">
                                    <nd-radio-group v-model="resultForm.tenderDepositDestination" @change="handleDestinationChange" :disabled="!resData || Number(resData.marginFee ?? 0) <= 0">
                                        <nd-radio :label="0">退款</nd-radio>
                                        <nd-radio :label="1">转为剩余应缴款</nd-radio>
                                    </nd-radio-group>
                                </el-form-item>
                                <div v-if="!resData || Number(resData.marginFee ?? 0) <= 0" class="list-text">投标保证金为0元，不可设置去向！</div>
                                <div v-if="resultForm.tenderDepositDestination === 1" class="list-text" style="display: flex; align-items: center; margin-left: 15px;">
                                    <el-checkbox-group v-model="destinationTypes" @change="handleDestinationTypeChange" style="display: flex; flex-direction: row; flex-wrap: wrap;">
                                        <nd-checkbox v-for="item in destinationTypeList" :key="item.type" :label="item.type" :disabled="item.type !== '4' ? isDestinationDisabled(item.type) : false" style="width: 150px; margin-right: 8px; padding: 0;">
                                            {{item.name}}
                                        </nd-checkbox>
                                    </el-checkbox-group>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr v-if="resData.downFeeFlag == 1">
                        <td>首期款（元）</td>
                        <!-- <td class="require">首期款（元）</td> -->
                        <td>
                            <el-form-item prop="downFee">
                                <nd-input v-model.trim="resultForm.downFee" width="100%"
                                :formatter="paymentListBlur022" :parser="paymentListBlur022" @change="checkDestinationOptions"/>
                            </el-form-item>
                            <!-- <el-form-item prop="downFee">
                                <nd-input v-model.trim="resultForm.downFee" width="100%"
                                :formatter="paymentListBlur02" :parser="paymentListBlur02"/>
                            </el-form-item> -->
                        </td>
                        <td>首期款截止缴纳时间</td>
                        <!-- <td class="require">首期款截止缴纳时间</td> -->
                        <td>
                            <el-form-item>
                                <nd-date-picker
                                style="width: 100%;"
                                type="datetime"
                                placeholder="请选择时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                v-model="resultForm.downFeeTime" />
                            </el-form-item>
                        </td>
                    </tr>
                    <tr>
                        <td class="require">项目款划分比例</td>
                        <td colspan="3" style="padding: 10px;padding-right: 0;">
                            <nd-table :data="resultForm.paymentList ? resultForm.paymentList : []" height="200" style="width: 99.5%;">
                                <el-table-column align="center" label="序号" type="index" :index="index=>index+1" width="55" />
                                <el-table-column align="center" :label="paymentListName" prop="name" />
                                <el-table-column align="center" label="开户名称" prop="accountName"  />
                                <el-table-column align="center" label="项目款占比(%)" prop="ratio">
                                    <template #default="{row}">
                                        <div style="display: flex;justify-content: center;">
                                            <nd-input v-model.trim="row.ratio" 
                                            v-if="flag.ratio"
                                            :formatter="paymentListBlur" :parser="paymentListBlur" />
                                            <span v-else>{{ row.ratio }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                            </nd-table>
                        </td>
                    </tr>
                    <tr>
                        <td>补充说明</td>
                        <td style="padding: 10px;" colspan="3">
                            <nd-input 
                            type="textarea" 
                            v-model.trim="resultForm.remark" 
                            width="100%" 
                            maxlength="500"
                            :autosize="{ minRows: 2, maxRows: 4 }"/>
                        </td>
                    </tr>
                </table>
                <table v-else class="fail-table">
                    <tr>
                        <td>项目名称</td>
                        <td colspan="3">{{ resData.projectName }}</td>
                    </tr>
                    <tr>
                        <td>项目编号</td>
                        <td colspan="3">{{ resData.projectCode }}</td>
                    </tr>
                    <tr>
                        <td>标段编号</td>
                        <td colspan="3">{{ resData.tenderCode }}</td>
                    </tr>
                    <tr>
                        <td class="require">未成功原因</td>
                        <td>
                            <el-radio-group v-model="reasonSingle" @change="getDefaultEnum">
                                <el-radio v-for="item in radioGrounp" :label="item.id" :key="item.id">
                                    {{ item.name }}
                                </el-radio>
                            </el-radio-group>
                        </td>
                    </tr>
                    <tr v-if="emumFlag">
                        <td class="require">违约人</td>
                        <td>
                            <nd-select v-model="emumkey" width="100%" @change="changeEnum" :multiple="isMultipleSelect">
                                <el-option v-for="item in enumList"
                                :label="item.label" :value="item.defaultId" :key="item.defaultId" />
                            </nd-select>
                        </td>
                    </tr>
                    <tr>
                        <td>备注</td>
                        <td style="padding: 10px;" colspan="3">
                            <nd-input 
                            type="textarea" 
                            v-model.trim="resultForm.remark" 
                            width="100%" 
                            maxlength="500"
                            :autosize="{ minRows: 2, maxRows: 4 }"/>
                        </td>
                    </tr>
                </table>
            </el-form>
        </div>
        <div v-if="resultForm.tradeFlag === 1" style="width: 100%;height: 15px;background: #f7f7f7;"></div>
        <!-- 文书 -->
        <div class="form-box" id="wsxx" v-if="resultForm.tradeFlag === 1">
            <div class="form-box-title">
                <div>
                    <!-- <img src="@/assets/images/projectRegistration/icon4.png" alt=""> -->
                    <span>文书信息</span>
                </div>
            </div>
            <div>
                <div style="border: 1px solid #EEEEEE;margin-top: 15px;">
                    <nd-table style="height: 100%;" :data="getDocList.arry">
                        <el-table-column align="center" type="index" min-width="40" label="序号" />
                        <!-- <el-table-column align="center" prop="modelName" label="文书名称" /> -->
                        <el-table-column align="center" prop="modelName" label="文书名称">
                        <template #default="{ row }">
                            <span style="cursor: pointer; color: #0098FF;font-size:14px;margin-right:15px;" class="operateBtn" 
                                    @click.stop="pushFlag(2, row,'预览文书')">{{ row.modelName }}</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column align="center" prop="genFlag" label="文书状态" /> -->
                        <el-table-column fixed="right" align="center" prop="genFlag" label="文书状态">
                            <template #default="{ row }">
                                <span class="operateBtn">{{ (row.dtbId == null || !row.dtbId) ? '未生成' : row.dtbId ? '已生成' : '-'
}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" align="center" prop="genFlag" label="是否必须生成文书">
                            <template #default="{ row }">
                                <span class="operateBtn">{{ row.genFlag == 0 ? '否' : row.genFlag == 1 ? '是' : '-'
                                }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" align="center" prop="pushStatus" label="推送状态">
                            <template #default="{ row }">
                                <span v-if="row.pushFlag == 1 && row.pushStatus == 1">已推送</span>
                                <span v-else-if="row.pushFlag == 1">未推送</span>
                                <span v-else>--</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column align="center" prop="pushStatus" label="推送状态" /> -->
                        <el-table-column fixed="right" align="center" prop="status" label="操作">
                            <template #default="{ row }">
                                <span style="cursor: pointer; color: #0098FF;font-size:14px;margin-right:15px;" class="operateBtn" v-if="row.dtbId==null ||row.dtbId==''" 
                                    @click.stop="pushFlag(1, row,'生成文书')">生成文书</span>
                                    <span style="cursor: pointer; color: #0098FF;font-size:14px;margin-right:15px;" class="operateBtn" v-else
                                    @click.stop="pushFlag(1, row,'重新生成')">重新生成</span>
                                    <!-- <span class="operateBtn" 
                                    @click.stop="pushFlag(1, row,'重新生成')">重新生成</span> -->
                                    <span style="cursor: pointer; color: #0098FF;font-size:14px" class="operateBtn" v-if="row.pushFlag == 1"
                                    @click.stop="pushFlag(2, row)">推送文书</span>
                                <!-- <span class="operateBtn" 
                                    @click.stop="pushFlag(1, row)">生产文书</span>
                                    <span class="operateBtn" 
                                    @click.stop="pushFlag(2, row)">推送文书</span> -->
                            </template>
                        </el-table-column>
                    </nd-table>
                    <!-- <div class="upload-box" v-for="(item, index) in formRespons.uploadList" :key="index">
                        
                    </div> -->
                </div>
            </div>
        </div>

        <!-- 附件 -->
        <div class="form-box" id="fjxx" v-if="resultForm.tradeFlag === 1">
            <div class="form-box-title">
                <div>
                    <!-- <img src="@/assets/images/projectRegistration/icon2.png" alt=""> -->
                    <span>附件信息</span>
                </div>
            </div>
            <div>
                <div style="border: 1px solid #EEEEEE;margin-top: 15px;">
                    <div class="upload-box" v-for="(item, index) in formRespons.uploadList2" :key="index">
                        <div class="upload-box-title" :class="item.needChoose == 1 ? 'set-class' : ''">
                        {{ item.fileName }}
                        </div>
                        <div class="upload-box-img">
                            <div class="upload-box-img-box">
                                <ndb-upload v-model="item.fileList" :uploadParams="{ busId: resData.tempId, configFileId: item.id }"></ndb-upload>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="width: 100%;height: 15px;background: #f7f7f7;"></div>
        <!-- btn -->
        <div class="btn-box">
            <nd-button type="primary" color="#0b8df1" icon="Finished" @click="saveFn(2)">提&nbsp;交</nd-button>
            <nd-button icon="Folder" @click="saveFn(1)">保&nbsp;存</nd-button>
            <nd-button icon="Back" @click="close">返&nbsp;回</nd-button>
        </div>
        <div v-if="resultForm.tradeFlag === 1" style="width: 100%;height: 15px;background: #f7f7f7;"></div>
        <div v-else style="width: 100%;height: 75%;background: #f7f7f7;"></div>
        <!-- 文书弹框 -->
        <documentDialog ref="documentModifyView" @refresh="getDocListFun" />
    </div>
</template>

<script setup>
// 组件
import ndButton from "@/components/ndButton.vue";
import ndInput from "@/components/ndInput.vue";
import ndTable from "@/components/ndTable.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue";
import ndRadio from "@/components/ndRadio.vue";
import ndCheckbox from "@/components/ndCheckbox.vue";
import documentDialog from "@/components/business/ndbWordDocument/index.vue";
import ndbUpload from "@/components/business/ndbUpload2/index.vue";
// 导入element-plus方法
import { ElMessage, ElMessageBox } from "element-plus";
// mock
import { useMock } from "./mockData.js"
// vue
import { inject, reactive, shallowRef, ref, watch, computed } from "vue"
// regexp
const reg_four = /^[1-9]{1}\d{0,9}(\.\d{1,2})?$/ //两位小数
// axios
const $axios = inject("$axios")
const emit = defineEmits(["update"])
const { mock, page, tagsData } = useMock()  //mock
// const radio = shallowRef(3)
const resData = ref({})
// 流入方最终有效交易表 最终有效出价
const lastPriceUnit = computed(()=>{
    if(resData.value.floorPriceUnit){
        return `最终有效出价（${resData.value.floorPriceUnit}）`
    }
    else{
        return `最终有效出价`
    }
})

// 判断违约人下拉框是否为多选：转入方违约(id=2)时为单选，其他为多选
const isMultipleSelect = computed(() => {
    return reasonSingle.value !== "2"
})
// flag
const flag = reactive({
    ratio: false,   // 项目款划分比例
    price: false,   // 最终有效出价
})
// 项目款划分比例 名称
let paymentListName = ref("出让方名称")
const textForm = ref({})
// table
// 
let tenderId = ref("")
let cjbidPrice = ref("")
let bidPriceMax = ref("")
// 不再需要在组件挂载时获取枚举值，改为在open方法中的getData调用时获取

function getData() {
    // 获取投标保证金去向费用类型枚举值
    getTenderDepositDestinationEnum()
    
    $axios({
        // url: `/result/getResultInfo?tenderId=686d4a43a1a94598b6ef2e94190c5aa2`,
        url: `/result/getResultInfo?tenderId=${tenderId.value}`,
        method: "get"
    }).then(res => {
        if(res.data.code !== 200) return msgWarning(res.data.msg)
        cjbidPrice.value= res.data.data.bidPrice
        bidPriceMax.value= res.data.data.bidPriceMax
        resData.value = res.data.data
        console.log(resData.value.marginFee,'resData.value.marginFee')
        console.log('marginFee类型:', resData.value.marginFee === null ? 'null' : typeof resData.value.marginFee)
        // paymentListName
        switch (resData.value.transferorsType) {
            case 1:
                paymentListName.value = "出让方名称"
                break;
            case 2:
                paymentListName.value = "采购方名称"
                break;
            case 3:
                paymentListName.value = "建设方名称"
                break;
            case 4:
                paymentListName.value = "销售方名称"
                break;
        }
        if(resData.value.reason){
            // 兼容旧数据：如果是多个值，取第一个；如果是单个值，直接使用
            const reasonArray = resData.value.reason.split(",")
            reasonSingle.value = reasonArray[0] || ""
            reason.value = reasonArray // 保留用于兼容
            // 初始 违约人列表
            getDefaultEnum(reasonSingle.value)
        }
        // 违约人回显
        if(resData.value.tradeFlag === 2 && resData.value.tradeResultDefaultVos){
            if(resData.value.tradeResultDefaultVos[0].defaultId){
                // 根据未成功原因类型设置违约人数据
                if(reasonSingle.value === "2") {
                    // 转入方违约：单选，取第一个
                    emumkey.value = resData.value.tradeResultDefaultVos[0].defaultId
                } else {
                    // 转出方违约或其他：多选，取所有
                    emumkey.value = resData.value.tradeResultDefaultVos.map(item=> item.defaultId)
                }
                resultForm.resultdefaultList = resData.value.tradeResultDefaultVos
            }
        }
        resultForm.id = resData.value.id ? resData.value.id : ""
        resultForm.tempId = resData.value.tempId 
        resultForm.projectId = resData.value.projectId
        resultForm.paymentList = resData.value.paymentList
        resultForm.traderContactPerson = resData.value.traderContactPerson
        flag.price = resData.value.tradeMode === 2 ? true : false   // 线下的金额可以修改
        
        // 回显投标保证金去向
        if (resData.value.depositDestinationType !== undefined) {
            const destinationValue = resData.value.depositDestinationType

            if (destinationValue === "0") {
                // 接口返回"0"表示退款，设置单选框为数字0
                resultForm.tenderDepositDestination = 0
                destinationTypes.value = []
            } else if (destinationValue) {
                // 接口返回非"0"值（如"1,2,3"），设置单选框为数字1（转为剩余应交款）
                resultForm.tenderDepositDestination = 1
                // 将具体的费用类型字符串分割后回显到多选框
                destinationTypes.value = destinationValue.split(',').filter(item => item.trim() !== '')
            }
        }
        
        flag.ratio = resultForm.paymentList.length > 1 ? true : false
        for (const key in resultForm) {
            if(resData.value[key] || resData.value[key] === 0){
                resultForm[key] = resData.value[key]
            }
            if(resData.value[key] && typeof(resData.value[key]) === "number" && (key === 'tradeAmount' || key === 'overflowAmount' || key === 'serviceFeeRule' || key === 'serviceFee' || key === 'perFeeRule' || key === 'perFee' || key === 'downFee')){
                resultForm[key] = resData.value[key].toFixed(2)
            }
        }
        // inflow.inflowId = resData.value.signupId
    }).then(()=>{
        getDocListFun()
        getUploadList2()
        computedFn()
        changeAmount()
        // tradePrice 赋值
        let arr = resData.value.tradeSignupTradeVoList.filter(item=>{
            return item.signupId === resultForm.signupId
        })
        if(arr.length > 0){
            resultForm.tradePrice = arr[0].bidPrice
        }
        
        // 检查投标保证金去向的费用类型可选状态
        checkDestinationOptions()
    })
}
// 流入方最终有效出价
function tradeSignupTradeChange(row) {
     // tradePrice 赋值
    let arr = resData.value.tradeSignupTradeVoList.filter(item=>{
        return item.signupId === resultForm.signupId
    })
    row.bidPriceMax = toChies(row.bidPrice)
    if(arr.length < 1) return
    resultForm.tradePrice = arr[0].bidPrice
}
function paymentListBlur(value, int = 3) {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.-]/g, "");
    value = value.replace(!/^-?\d+(\.\d+)?$/, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    value = value.replace("-", "$#$").replace(/\-/g, "").replace("$#$", "-");
    // 保证只能输入2个小数  
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
    // 只能（int）位整数，自定义
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
function paymentListBlur02(value, int = 13) {
// function paymentListBlur02(value, int = 12) {
    value = value.toString();
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.]/g, "");
    // value = value.replace(/[^\d.-]/g, "");
    // value = value.replace(!/^-?\d+(\.\d+)?$/, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 负号只能出现在首位
    // value = value.replace(/(\d+|\.)-/g, '$1')
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    value = value.replace("-", "$#$").replace(/\-/g, "").replace("$#$", "-");
    // 保证只能输入2个小数  
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
    // 只能（int）位整数，自定义
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
function paymentListBlur022(value, int = 12) {
    value = value.toString();
    value =value.replace(/-/g,"");
    // 先把非数字的都替换掉，除了数字和小数点
    value = value.replace(/[^\d.-]/g, "");
    value = value.replace(!/^-?\d+(\.\d+)?$/, "");
    // 必须保证第一个为数字而不是小数点
    value = value.replace(/^\./g, "");
    // 负号只能出现在首位
    value = value.replace(/(\d+|\.)-/g, '$1')
    // 保证只有出现一个小数点而没有多个小数点
    value = value.replace(/\.{2,}/g, ".");
    // 保证小数点只出现一次，而不能出现两次以上
    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
    value = value.replace("-", "$#$").replace(/\-/g, "").replace("$#$", "-");
    // 保证只能输入2个小数  
    value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
    // 只能（int）位整数，自定义
    let index = value.indexOf('.')
    if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index)
    } else {
        value = value.slice(0, int)
    }
    return value
}
function paymentListBlur03(value, int = 12) {
    let val = value
        .replace(/^0[0-9]+/, (val) => val[1])
        .replace(/^(\.)+/, '')
        .replace(/[^\d.]/g, '')
        .replace(/^(\d{12})\d*(\.\d{0,2})?$/, '$1$2')
        .replace(/\.+/, '.')
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
        .replace(/^(\d+)\.(\d\d).*$/, '$1.$2');
        // 保证只能输入2个小数  
        val = val.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
        // 只能（int）位整数，自定义
        let index = val.indexOf('.')
        if (index > -1) {
            val = val.slice(0, index < int ? index : int) + val.slice(index)
        } else {
            val = val.slice(0, int)
        }
        return val
}
// 成交人id 成交方下拉框切换校验
const inflow = reactive({
    inflowId:""
})
let inflowRes = ref({})
function inflowChange() {
    // 清空 投标保证金选项 单选和多选
    // resultForm.tenderDepositDestination = ""
    destinationTypes.value = []


    if(!resultForm.signupId) return
    let arr = resData.value.tradeSignupTradeVoList.filter(item=>{
        return item.signupId === resultForm.signupId
    })
    resultForm.tradePrice = arr[0].bidPrice // tradePrice 赋值
    $axios({
        method:"get",
        url:`/result/chooseTrade?signupId=${arr[0].signupId}&bidPrice=${arr[0].bidPrice}&tenderId=${tenderId.value}`
    }).then(res=>{
        if(res.data.code === 200){
            inflowRes.value = res.data.data
            cjbidPrice.value= res.data.data.bidPrice
        bidPriceMax.value= res.data.data.bidPriceMax
            for (const key in resultForm) {
                if(inflowRes.value[key] || inflowRes.value[key] == 0){
                    resultForm[key] = inflowRes.value[key]
                }else if(key==="tradeAmount" || key==="tradeTime" || key==="overflowAmount" || key==="serviceFee" || key==="perFee"){
                    resultForm[key] = ""
                }
            }
            resData.value.floorTradeAmount=inflowRes.value.floorTradeAmount
            changeAmount()
        }else{
            msgWarning(res.data.msg)
        }
    })
}
function inflowChange02(val,bidPrice, signupId) {
    if(!resultForm.signupId) return
    // 与成交方相关联
    if(val != signupId) return
    $axios({
        method:"get",
        url:`/result/chooseTrade?signupId=${val}&bidPrice=${bidPrice}&tenderId=${tenderId.value}`
    }).then(res=>{
        inflowRes.value = res.data.data
        for (const key in resultForm) {
            if(inflowRes.value[key] || inflowRes.value[key] == 0){
                resultForm[key] = inflowRes.value[key]
            }
        }
        cjbidPrice.value= res.data.data.bidPrice
        bidPriceMax.value= res.data.data.bidPriceMax
        resData.value.floorTradeAmount=inflowRes.value.floorTradeAmount
        changeAmount()
    })
}
// 成交总金额  获取 服务费
function getServieFee() {
    // resultForm.tradeAmount
    if(!resultForm.signupId) return
    let arr = resData.value.tradeSignupTradeVoList.filter(item=>{
        return item.signupId === resultForm.signupId
    })
    $axios({
        method:"get",
        url:`/result/getServiceFee`,
        data:{
            bidPrice: arr[0].bidPrice,
            tenderId: tenderId.value,
            tradeAmount: resultForm.tradeAmount,
        }
    }).then(res=>{
        const { data } = res.data
        resultForm.perFee = data.perFee
        resultForm.serviceFee = data.serviceFee
        console.log(resultForm.perFee,resultForm.serviceFee)
    })
}
// before submit 校验
/**
 * @params {number} 为 1 正常提交，2为文书生成 
 */
function beforeSubmit(params) {
    let flag = false
    if(resultForm.tradeFlag == 1){
        // if(!resultForm.signupId) return msgWarning("请选择成交方!")
        // if(!resultForm.tradeTime) return msgWarning("请选择成交日期!")
        // if(!resultForm.tradeAmount && resultForm.tradeAmount !== 0) return msgWarning("请输入成交总金额!")
        // if(resData.value.serviceFeeFlag == 1 && !resultForm.serviceFee && resultForm.serviceFee!==0 && resData.value.serviceFeeType && resData.value.serviceFeeType != 1) return msgWarning("请输入服务费!")
        // if(resData.value.serviceFeeFlag == 1 && !resultForm.serviceFeeTime && resData.value.serviceFeeType && resData.value.serviceFeeType != 1) return msgWarning("请选择服务费截止缴纳时间!")
        // if(resData.value.perFeeFlag==1 && !resultForm.perFee && resultForm.perFee!==0 && resData.value.perFeeType != 1 && resData.value.perFeeType) return msgWarning("请输入履约保证金!")
        // if(resData.value.perFeeFlag==1 && !resultForm.perFeeTime && resData.value.perFeeType != 1 && resData.value.perFeeType) return msgWarning("请选择履约保证金截止缴纳时间!")
        // if(resData.value.downFeeFlag == 1 && !resultForm.downFee && resultForm.downFee !== 0) return msgWarning("请输入首期款!")
        // if(resData.value.downFeeFlag == 1 && resultForm.downFee == 0) return msgWarning("首期款仅允许输入大于0的数字!")
        // if(resData.value.downFeeFlag == 1 && !resultForm.downFeeTime) return msgWarning("请选择首期款截止缴纳时间!")
        
        // 投标保证金去向校验
        if (!validateDestination()) {
            return false
        }
        if(resultForm.paymentList.length > 1) {
            let payFlag = false
            for (let i = 0; i < resultForm.paymentList.length; i++) {
                if(resultForm.paymentList[i].ratio === "" || Number(resultForm.paymentList[i].ratio) <= 0){
                    payFlag = false
                    flag=false
                    msgWarning("项目款分成占比允许录入大于或等于0的数字，保留两位小数!")
                    break
                }else{
                    payFlag = true
                }
            }
            if(!payFlag) return
            let sum = resultForm.paymentList.reduce((accumulator,currentValue)=>{
                return +accumulator + +currentValue.ratio
            },0)
            if(sum != 100) return msgWarning("项目款分成占比录入不正确!")
        }
        if(params==1){
            for (let i = 0; i <= formRespons.uploadList2.length - 1; i++) {
                if(formRespons.uploadList2[i].needChoose === "1" && formRespons.uploadList2[i].fileList.length === 0){
                    return msgWarning("请上传附件!")
                }
            }
        }
    }
    else if(resultForm.tradeFlag == 2){
        if(!resultForm.reason) return msgWarning("请选择未成功原因!")
        // 单选其他就不出现违约人下拉框
        if(reasonSingle.value != "3"){
            // 转入方违约时为单选校验，其他为多选校验
            if(reasonSingle.value === "2") {
                if(!emumkey.value) return msgWarning("请选择违约人!")
            } else {
                if(!Array.isArray(emumkey.value) || emumkey.value.length === 0) return msgWarning("请选择违约人!")
            }
        }
    }
    flag = true
    return flag
}
let reason = ref([])    //未成功原因 中间量 (保留用于兼容)
let reasonSingle = ref("")    //未成功原因 单选
// 投标保证金去向费用类型列表
const destinationTypeList = ref([])
// 选中的投标保证金去向费用类型
const destinationTypes = ref([])



const resultForm = reactive({
    tempId:"",
    paymentList: [],    //项目款比例划分列表
    tradeSignupTradeDtoList: [], //
    reason:"",  //未成功原因    1,2,3
    remark:"",  //补充说明(未成功)/(成功)
    tradeMode: "",  //1是线上=2线下，线下的金额可以修改
    
    // divisionRatio:"",   //项目款项划分比例
    downFee:"",  //首期款（分）
    downFeeTime: "",    //首期款截止缴纳时间
    flowStatus: 0,  //交易结果小状态
    id: "", //
    overflowAmount: "0",  //溢出总金额（分）
    overflowScale:'',  //溢价率
    perFee: "",  //
    perFeeTime: "", //履约保证金截止缴纳时间
    perFeeRule:"",  //履约保证金收取规则
    projectId: "",  //项目id
    queryFlag: 0,   //是否质疑（0 否；1 是）
    serviceFee: "",  // 服务费（分）
    serviceFeeRule:"",  //服务费收取规则
    serviceFeeTime: "", //服务费截止缴纳时间
    signupId: "",   //成交方ID（报名人ID）
    status: 0,      //交易结果状态（1 交易结果登记； 2 交易结果提交待审核； 3 交易结果审核中； 4 交易结果审核不通过； 5 交易结果审核通过待公示; 6 公示中；7 公示结束）
    tenderId: "",   //标段id
    tradeAmount: "", //成交总金额(分)
    tradeFlag: 1,   //	是否成功（1 成功；2 失败）
    tradePrice: "",  //成交单价（分）
    tradeTime: "",  //成交日期
    tradeUnit: "",  //	成交单价单位
    traderCertNo: "",   //成交人证件号码
    traderCertType: 0,  //	成交人证件类型（1：企业营业执照，2：身份证，3：台胞证,4:港澳证件,5:护照,6:军官证,7:组织机构代码证件8:其他证件）
    traderPerson: "",   //	成交人
    traderPhone: "",    //成交人联系电话
    traderType: 0,  //	成交方类型（1 个人；2 企业；3 家庭农场；4 合作社；5 其他）
    unitPath: "",    //单位路径（以|拼接）

    serviceFeeFlag:"",
    perFeeFlag:"",
    downFeeFlag:"",

    resultdefaultList: [],  //违约方信息array
    traderContactPerson:'',
    tenderDepositDestination: '', // 投标保证金去向：退款0 转为剩余应交款1
})
const formRef = ref()
function validateDownFee(rule, value, callback) {
    // if (value === '') {
    //     callback(new Error('请输入首期款'))
    // } 
    // else if (Number(value) < 0) {
    //     callback(new Error("首期款仅允许输入大于等于0的数字"))
    // } 
    // else {
    //     callback()
    // }
    // if(resultForm.downFee && !resultForm.downFeeTime){
    //     callback(new Error('填写了首期款必须填写首期款截止时间'))
    // }
    // else if(!resultForm.downFee && resultForm.downFeeTime){
    //     callback(new Error('填写了首期款截止时间必须填写首期款'))
    // }
    // else {
    //     callback()
    // }
    callback()
}
// 验证投标保证金去向
function validateTenderDepositDestination(rule, value, callback) {
    if (resData.value && Number(resData.value.marginFee ?? 0) > 0 && value === '') {
        callback(new Error('请选择投标保证金去向'))
    } else {
        callback()
    }
}

const rules = {
    signupId:[{ required: true, message: '请选择成交方', trigger: 'change' }],
    tradeTime:[{ required: true, message: '请选择成交日期', trigger: 'blur' }],
    tradeAmount:[{ required: true, message: '请输入成交总金额', trigger: 'blur' }],
    serviceFee:[{ required: true, message: '请输入服务费', trigger: 'blur' }],
    perFee:[{ required: true, message: '请输入履约保证金', trigger: 'blur' }],
    downFee:[
        { validator: validateDownFee, trigger: ['blur','change'] }
    ],
    downFeeTime:[
        { validator: validateDownFee, trigger: ['blur','change'] }
    ],
    tenderDepositDestination: [
        { validator: validateTenderDepositDestination, trigger: ['change'] }
    ],
    // downFeeTime:[
    //     { required: true, message: '请选择首期款截止缴纳时间', trigger: ['blur','change'] }
    // ],
    ratio:[{ required: true, message: '请输入项目款占比', trigger: 'blur' }],
}
const radioGrounp = [
    { name:"转出方违约", id:"1" },
    { name:"转入方违约", id:"2" },
    { name:"其他", id:"3" },
]
// 新增	违约登记优化
let enumList = ref([])
let emumkey = ref([])
let emumFlag = ref("")
// 获取投标保证金去向费用类型枚举值
const getTenderDepositDestinationEnum = () => {
    // 防御性检查，确保tenderId有值
    if (!tenderId.value) {
        console.error("获取投标保证金去向枚举值时tenderId为空")
        return
    }
    
    $axios({
        url: "/result/getTenderDepositDestinationEnum",
        method: "get",
        data:{
            tenderId: tenderId.value
        }
    }).then(res => {
        if (res.data.code === 200) {
            destinationTypeList.value = res.data.data || []
        } else {
            msgWarning(res.data.msg)
        }
    }).catch(() => {
        destinationTypeList.value = []
    })
}

// 转换投标保证金去向数据为提交格式
const convertDestinationData = () => {
    if (resultForm.tenderDepositDestination === 0) {
        return "0" // 退款
    } else if (resultForm.tenderDepositDestination === 1) {
        return destinationTypes.value.join(',') // 转为剩余应交款
    } else {
        return '' // 未设置
    }
}

// 验证投标保证金去向
const validateDestination = () => {
    if (resData.value && Number(resData.value.marginFee ?? 0) > 0) {
        if (!resultForm.tenderDepositDestination && resultForm.tenderDepositDestination !== 0) {
            msgWarning("请选择投标保证金去向!")
            return false
        }

        // 如果选择了转为剩余应交款，但没有选择费用类型
        if (resultForm.tenderDepositDestination === 1 && (!destinationTypes.value || destinationTypes.value.length === 0)) {
            msgWarning("请至少选择一个费用类型!")
            return false
        }
    }
    return true
}

// 投标保证金去向变更处理
const handleDestinationChange = (val) => {
    if (val !== 1) {
        // 如果不是转为剩余应交款，清空费用类型选择
        destinationTypes.value = []
    } else {
        // 如果是转为剩余应交款，检查是否有可选的费用类型
        checkDestinationOptions()
    }
}

// 计算选中费用类型的总金额（不包括合同尾款）
const calculateSelectedFeeTotal = (types = destinationTypes.value) => {
    let total = 0
    types.forEach(type => {
        if (type === "1" && resultForm.serviceFee) {
            total += Number(resultForm.serviceFee) || 0
        } else if (type === "2" && resultForm.downFee) {
            total += Number(resultForm.downFee) || 0
        } else if (type === "3" && resultForm.perFee) {
            total += Number(resultForm.perFee) || 0
        }
    })
    return total
}

// 检查投标保证金去向的费用类型可选状态
const checkDestinationOptions = () => {
    debugger
    // 防御性检查
    if (!resultForm || resultForm.tenderDepositDestination !== 1) return
    if (!destinationTypes.value) {
        destinationTypes.value = []
        return
    }

    try {
        // 重新检查已选中的费用类型是否还符合条件
        const validTypes = destinationTypes.value.filter(type => !isDestinationDisabled(type))
        if (validTypes.length !== destinationTypes.value.length) {
            destinationTypes.value = validTypes
            if (validTypes.length === 0) {
                ElMessage.warning("由于金额变更，已清空不符合条件的选项")
            }
        }

        // 检查总金额是否超过投标保证金
        if (resData.value && resData.value.marginFee != null && destinationTypes.value.length > 0) {
            const totalSelectedFee = calculateSelectedFeeTotal()

            // 如果总金额大于投标保证金金额，取消所有选项
            if (totalSelectedFee > Number(resData.value.marginFee ?? 0)) {
                destinationTypes.value = []
                ElMessage.warning("投标保证金小于剩余应缴款，不可选中，请重新选择！")
            }
            // 如果总金额等于投标保证金金额并且此时 转为合同尾款选项是被勾选状态，取消所有选项
            console.log(destinationTypes.value,'destinationTypes.value')
            if (totalSelectedFee == Number(resData.value.marginFee ?? 0)&&destinationTypes.value.includes("4")) {
                destinationTypes.value = []
                ElMessage.warning("投标保证金已经用完了，不可以再转合同尾款了")
            }
        }
    } catch (error) {
        destinationTypes.value = []
    }
}

// 判断费用类型是否禁用
const isDestinationDisabled = (type) => {
    // 服务费
    if (type === "1") {
        if (!resultForm.serviceFee || Number(resultForm.serviceFee) <= 0) {
            return true
        }
        return false
    }
    // 首期款
    else if (type === "2") {
        if (!resultForm.downFee || Number(resultForm.downFee) <= 0) {
            return true
        }
        return false
    }
    // 履约保证金
    else if (type === "3") {
        if (!resultForm.perFee || Number(resultForm.perFee) <= 0) {
            return true
        }
        return false
    }
    // 合同尾款：不再禁用，在选择时进行判断
    else if (type === "4") {
        return false
    }

    return false
}

// 处理费用类型选择变更
const handleDestinationTypeChange = (val) => {
    // 防御性检查
    if (!val) return
    if (!resData.value || resData.value.marginFee == null) return

    // 至少选择一个
    if (val.length === 0 && resultForm.tenderDepositDestination === 1) {
        ElMessage.warning("请至少选择一个费用类型")
        return
    }

    try {
        const totalSelectedFee = calculateSelectedFeeTotal(val)
        const marginFee = Number(resData.value.marginFee ?? 0)

        // 检查总金额是否超过投标保证金
        if (totalSelectedFee > marginFee) {
            ElMessage.warning("投标保证金小于剩余应缴款，不可选中，请重新选择！")

            // 这里不是清空所有，而是清除最近的一次选中；所以删除数组最后插入的元素就可以
            destinationTypes.value.pop()
            return
        }

        // 检查是否选中了合同尾款
        if (val.includes("4") && totalSelectedFee >= marginFee) {
            ElMessage.warning("投标保证金已经用完了，不可以再转合同尾款了！")
            destinationTypes.value = destinationTypes.value.filter(type => type !== "4")
            return
        }
    } catch (error) {
        // 错误处理
    }
}

// 
watch(()=>resultForm.tradeAmount,()=>{
    computedFn()
})
function computedFn() {
    // 服务费（元
    if(resData.value.serviceFeeFlag == 1){        //serviceFeeFlag 是否显示
        if(resData.value.serviceFeeType == 3){
            if(!resultForm.serviceFee){
                resultForm.serviceFee = resultForm.serviceFeeRule
            }
        }
    }
    // 履约保证金（元）
    if(resData.value.perFeeFlag == 1){        //perFeeFlag 是否显示
        if(resData.value.perFeeType == 3){
            if(!resultForm.perFee){
                resultForm.perFee = resultForm.perFeeRule
            }
        }
    }
}
function msgSuccess(params) {
    ElMessage({
        type: 'success',
        message: params,
    })
}
function msgWarning(params) {
    ElMessage({
        type: 'warning',
        message: params,
    })
}
function checkAllFieldsValid(arr, key) {
  return !arr.some(item => !item[key]); // 如果任意 item[key] 为空，返回 false
}
// 
function saveFn(params) {
    
    // add/edit
    params == 1 ? resultForm.flowStatus = 75 : resultForm.flowStatus = 81
    let temp = "add"
    if(resData.value.id) {
        temp = "edit"
    }
    resultForm.tradeSignupTradeDtoList = resData.value.tradeSignupTradeVoList
    resultForm.reason = reasonSingle.value || ""

    // 先进行校验，再处理数据
    let beforeSubmitFlag = beforeSubmit(1)
    resultForm.overflowScale=Number(resultForm.overflowScale)
    if(resultForm.tradeFlag == 1){
        if(resultForm.overflowScale<0) return msgWarning("溢价率不能为负数")
        if(!cjbidPrice.value) return  msgWarning('成交价不能为空')
        // const result = checkAllFieldsValid(resData.value.tradeSignupTradeVoList, 'bidPrice');
        // if(!result&&flag.price) return  msgWarning('最终有效出价不能为空')
    }
    // 首期款和首期款截止缴纳时间
    if(!resultForm.downFee && resultForm.downFeeTime) {
        return msgWarning("首期款不能为空")
    }

    if(!beforeSubmitFlag) return

    // 校验通过后，处理投标保证金去向费用类型数据
    resultForm.depositDestinationType = convertDestinationData()
    // resultForm.tenderDepositDestination = null // 清空临时字段
    formRef.value.validate((valid, fields) => {
        if (valid) {
            console.log('submit!')
            $axios({
                url:`/result/${temp}`,
                method:"post",
                data:resultForm
            }).then(res=>{
                if(res.data.code===200){
                    params == 1 ? msgSuccess("保存成功") :  msgSuccess("提交成功")
                    close()
                    emit("update")
                }else{
                    msgWarning(res.data.msg)
                }
                console.log(res,"提交结果登记")
            })
        } else {
            console.log('error submit!', fields)
            formRef.value.scrollToField(Object.keys(fields)[0] || "")
        }
    })
}
// 生成文书之前，调用保存接口
function beforePushFlag(params) {
    return new Promise((reslove,reject)=>{
        // 保存界面字段的值，避免在转换过程中丢失
        const tempTenderDepositDestination = resultForm.tenderDepositDestination

        formRef.value.validate((valid, fields) => {
            if (valid) {
                console.log('submit!')
                params == 1 ? resultForm.flowStatus = 75 : resultForm.flowStatus = 81
                let temp = "add"
                if(resData.value.id) {
                    temp = "edit"
                }
                resultForm.tradeSignupTradeDtoList = resData.value.tradeSignupTradeVoList
                resultForm.reason = reason.value.join(",")

                // 处理投标保证金去向费用类型
                resultForm.depositDestinationType = convertDestinationData()
                
                let beforeSubmitFlag = beforeSubmit(2)

                if(!beforeSubmitFlag) {
                    // 校验失败时也要恢复界面字段的值
                    resultForm.tenderDepositDestination = tempTenderDepositDestination
                    return
                }
                $axios({
                    url:`/result/${temp}`,
                    method:"post",
                    data:resultForm
                }).then(res=>{
                    if(res.data.code===200){
                        // params == 1 ? msgSuccess("保存成功") :  msgSuccess("提交成功")
                        msgSuccess("已为您自动保存当前页面信息！")

                        // 保存成功后，恢复界面字段的值，避免用户重新选择
                        resultForm.tenderDepositDestination = tempTenderDepositDestination

                        reslove(true)
                    }else{
                        msgWarning(res.data.msg)

                        // 保存失败时也要恢复界面字段的值
                        resultForm.tenderDepositDestination = tempTenderDepositDestination
                    }
                    console.log(res,"提交结果登记")
                }).catch(error => {
                    // 网络请求失败时也要恢复界面字段的值
                    resultForm.tenderDepositDestination = tempTenderDepositDestination
                    msgWarning("网络请求失败，请重试")
                    console.error("保存失败:", error)
                })
            }
            else {
                console.log('error submit!', fields)

                // 表单验证失败时也要恢复界面字段的值
                resultForm.tenderDepositDestination = tempTenderDepositDestination

                formRef.value.scrollToField(Object.keys(fields)[0] || "")
            }
        })

        
    })
}
// 
const show = reactive({
    mianShow: false
})

// dialog
function open(data) {
    if (!data) {
        return
    }
    
    resultForm.tenderId = tenderId.value = data.tenderId || ""
    textForm.value = data
    // resultForm.flowStatus = flowStatus
    // resultForm.flowStatus = 75
    show.mianShow = true
    // 违约相关
    enumList.value = []
    emumFlag.value = false
    emumkey.value = [] // 初始化为数组，会在getDefaultEnum中根据选择的原因类型调整
    reasonSingle.value = ""
    // 重置投标保证金去向相关数据
    destinationTypes.value = []
    resultForm.tenderDepositDestination = ""
    
    // 初始化resData
    resData.value = resData.value || {}
    
    getData()
}
function close() {
    // reset-form
    for (const key in resultForm) {
        resultForm[key] = ""
    }
    resultForm.resultdefaultList = []
    resultForm.paymentList = []
    resultForm.reason = []
    resultForm.tradeFlag = 1
    // 未成功原因 temp 中间变量
    reason.value = []
    reasonSingle.value = ""
    // 重置违约人相关
    emumkey.value = []
    emumFlag.value = false
    enumList.value = []
    // 重置投标保证金去向相关数据
    destinationTypes.value = []
    // dialog-show
    show.mianShow = false
}
// 文书
const getDocList = reactive({
    arry:[]
})
const documentModifyView = ref();
const pushFlag = (th, item) => {//文书操作
    if (th == 1) {
        // documentModifyView.value.open(projectInfor.id, item.id, "0", 1, '生成文书');
        beforePushFlag(1).then(res=>{
            if(res){
                documentModifyView.value.open(tenderId.value, item.id, "1", 1, '生成文书');
            }
        })
    } else {
        // documentModifyView.value.open(resData.value.projectId, item.dtbId, "0", 2, '生成文书');
        if (item.dtbId) {
            beforePushFlag(1).then(res=>{
                documentModifyView.value.open(tenderId.value, item.dtbId, "1", 2, '生成文书');
            })
        } else {
            ElMessage.error('文书未生成,无法预览')
        }
    }
}
let getDocListFun = () => {// 初始文书列表 ===
    $axios({
        url: "/docInfo/getDocList",
        method: "post",
        data:{
            bizId: tenderId.value,
            unitId: textForm.value.villageId ? textForm.value.villageId : textForm.value.townId ? textForm.value.townId : textForm.value.countyId,
            varietyKey: textForm.value.childTradeVariety,
            flowBaseCode: '7',
            type:"1"
        }
    }).then((res) => {
        if (res.data.code === 200) {
            getDocList.arry = res.data.data;
        }
    })
}
// 附件
const formRespons = reactive({
    uploadList2:[]
})
const getUploadList2 = () => {// 初始附件信息列表 ===
    $axios({
        url: "/file/getTheAttachmentTypeList",
        method: "post",
        data: {
            busId: resData.value.tempId,
            flowBasecode: '7',
            projectId: resData.value.projectId
        }
    }).then((res) => {
        if (res.data.code === 200) {
            // let url = '/file/uploadFile?busId=' + projectInfor.id + '&configFileId=' + res.data.data[0].id;
            res.data.data[0].fileList.forEach(item => {
                let obj = {};
                obj.name = item.fileName;
                obj.url = item.fileUrl;
                obj.id = item.id;
            })
            formRespons.uploadList2 = res.data.data;
        }
    })
}
// // 大写数字过滤器
function toChies(amount) {
  // 汉字的数字
  const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
  // 基本单位
  const cnIntRadice = ['', '拾', '佰', '仟'];
  // 对应整数部分扩展单位
  const cnIntUnits = ['', '万', '亿', '兆'];
  // 对应小数部分单位
  const cnDecUnits = ['角', '分'];
  // 整数金额时后面跟的字符
  const cnInteger = '整';
  // 整型完以后的单位
  const cnIntLast = '元';
  // 最大处理的数字
  const maxNum = 999999999999999.99;
  // 金额整数部分
  let integerNum;
  // 金额小数部分
  let decimalNum;
  // 输出的中文金额字符串
  let chineseStr = '';
  // 分离金额后用的数组，预定义
  let parts;
  if (amount === '') {
    return '';
  }
  amount = parseFloat(amount);
  if (amount >= maxNum) {
    // 超出最大处理数字
    return '';
  }
  if (amount === 0) {
    chineseStr = cnNums[0] + cnIntLast + cnInteger;
    return chineseStr;
  }
  // 转换为字符串
  amount = amount.toString();
  if (amount.indexOf('.') === -1) {
    integerNum = amount;

    decimalNum = '';
  } else {
    parts = amount.split('.');
    integerNum = parts[0];
    decimalNum = parts[1].substr(0, 4);
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0;
    const IntLen = integerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === '0') {
        zeroCount++;
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0];
        }
        // 归零
        zeroCount = 0;
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q];
      }
    }
    chineseStr += cnIntLast;
  }
  // 小数部分
  if (decimalNum !== '') {
    const decLen = decimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1);
      if (n !== '0') {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (chineseStr === '') {
    chineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (decimalNum === '') {
    chineseStr += cnInteger;
  }
  return chineseStr;
}
// 交易结果失败 违约人枚举
const getDefaultEnum = (val) => {
    let defaultFlag = val || ""
    // 切换未成功原因必须清空违约人
    // 根据选择的原因类型初始化emumkey：转入方违约为单选(字符串)，其他为多选(数组)
    if(defaultFlag == "2") {
        emumkey.value = "" // 转入方违约：单选，使用字符串
    } else {
        emumkey.value = [] // 转出方违约或其他：多选，使用数组
    }

    // 单选其他就不出现违约人下拉框
    if(defaultFlag == "3"){
        enumList.value = []
        resultForm.resultdefaultList = []
        emumFlag.value = false
        return
    }else{
        emumFlag.value = true
    }
    $axios({
        url: "/result/getDefaultEnum",
        method: "GET",
        data: {
            defaultFlag,
            tenderId: tenderId.value,
        }
    }).then(res=>{
        if(res.data.code !== 200) return
        enumList.value = res.data.data
        enumList.value.forEach(item=>{
            if(item.certNo){
                item.label = item.name + '（' + item.certNo.slice(-4) + '）'
            }else{
                item.label = item.name
            }
        })
    })
}
const changeEnum = (val) => {
    resultForm.resultdefaultList = []

    // 转入方违约时为单选，其他为多选
    if(reasonSingle.value === "2") {
        // 单选情况：val是字符串
        if(val) {
            const selectedItem = enumList.value.find(item => item.defaultId === val)
            if(selectedItem) {
                resultForm.resultdefaultList = [selectedItem]
            }
        }
    } else {
        // 多选情况：val是数组
        if(Array.isArray(val) && val.length > 0) {
            let temp = enumList.value.map(item=>{
                if(val.find(element => item.defaultId === element)) {
                    return item
                }
            })
            temp = temp.filter((item)=>{
                if(item) return item
            })
            resultForm.resultdefaultList = temp
        }
    }
}
const changePercent=()=>{
    if (resultForm.tradeAmount !== null && resultForm.tradeAmount !== '') {
        // 自动计算溢价总金额
        // resultForm.overflowAmount=(resultForm.tradeAmount*100-resData.value.floorTradeAmount*100)/100
        // resultForm.overflowAmount=(Math.abs(resultForm.tradeAmount*100-resData.value.floorTradeAmount*100))/100
        if(resData.value.tradeDirection===0){
            // 正向
            resultForm.overflowAmount=(resultForm.tradeAmount*100-resData.value.floorTradeAmount*100)/100
        }else{
            // 反向
            resultForm.overflowAmount=(resData.value.floorTradeAmount*100-resultForm.tradeAmount*100)/100
        }
        
      } else {
        resultForm.overflowAmount = '0';
    }
    changeAmount()
}
const changeAmount=()=>{
    if (resultForm.tradeAmount !== null && resultForm.tradeAmount !== '') {
        // 自动计算溢价率
        if(resData.value.floorTradeAmount){
            resultForm.overflowScale = (((resultForm.overflowAmount * 100) / (resData.value.floorTradeAmount * 100) ) *100).toFixed(2);
        }else{
            resultForm.overflowScale='0.00'
        }
        
      } else {
        resultForm.overflowScale = '';
      }
}
defineExpose({ open, close })
</script>

<style lang="scss" scoped>
$vh: calc(100/1080);
$vw: calc(100/1920);
.result-main-box {
    :deep(.el-form-item){
        margin: 0;
    }
    width: 100%;
    height: 100%;
    z-index: 3;
    // padding: 15px;
    background: #fff;
    font-size: 14px;
    overflow: auto;
    .result-box{
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        font-size: 16px;
        // font-weight: 700;
        line-height: 1;
        color: #606266;
    }
    .btn-box{
        display: flex;
        justify-content: center;
        // margin-top: 30px;
        background: #f7f7f7;
    }
}

table {
    width: 100%;
}

table,tr,td {
    border-collapse: collapse;
    border: 1px solid #eee;
}
tr {
    // height: 42px;
    width: 50%;
    td{
        padding: 8px 0;
        // padding: 6px 0;
        :deep(.el-form-item__error){
            padding-top:0px;
        }
    }

    td:nth-child(2n-1) {
        padding-left: 10px;
        padding-right: 6px;
        width: 9%;
        text-align: right;
        background: #F9F9F9;
        color: #606266;
    }

    td:nth-child(2n) {
        width: 41%;
        padding-right: 10px;
        text-align: left;
        padding-left: 10px;
        color: #444444;
    }

    // td.special{
    //     width: 50px;
    // }
}
table tr td:nth-child(2n-1){
    min-width: 150px;
}
table.fail-table{
    td:nth-child(2n-1) {
        padding-left: 10px;
        padding-right: 6px;
        width: 150px;
    }

    td:nth-child(2n) {
        width: calc(100% - 150px);
        padding-right: 10px;
    }
}
.require::before {
    content: "* ";
    color: red;
}
.list-box{
    display: flex;
    align-items: center;
    .list-text{
        padding: 0 10px;
        color:red;
    }
}
.contract-tail-disabled {
    opacity: 0.5;
    pointer-events: none;
}
.form-box {
    width: 100%;
    height: auto;
    padding: 15px 15px;
    background: #fff;
    // margin-top: 15px;

    .upload-box {
        width: 90%;
        margin: 0 auto;

        .upload-box-title {
            font-size: 14px;
            color: #0B8DF1;
            margin-top: 15px;
        }

        .set-class::before {
            content: '*';
            color: red;
        }

        .upload-box-img {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            margin-top: 15px;

            .upload-box-img-box {
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: #909399;
                cursor: pointer;

                span {
                    padding: 10px 0px;
                }

                span::before {
                    content: '*';
                    color: red;
                }

                :deep(.el-upload-list__item) {
                    width: 80px;
                    height: 80px;
                }

                :deep(.el-upload--picture-card) {
                    width: 80px;
                    height: 80px;
                }

            }

        }
    }

    .active-class {
        margin-top: 15px;
    }

    .form-box-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        img {
            width: 17px;
            height: 15px;
        }

        span {
            font-size: 16px;
            color: #606266;
            // margin-left: 15px;
            font-weight: bold;
        }
    }

    .form-box-title-lines {
        width: 100%;
        height: 32px;
        background: #F9F9F9;
        margin-top: 15px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 15px;

        img {
            margin-right: 15px;
            cursor: pointer;
        }
    }

    .form-box-content {
        width: 90%;
        margin: 0 auto;
        margin-top: 15px;
        display: flex;
        flex-wrap: wrap;
        // justify-content: center;
        padding-left: 5%;

        :deep(.nd-search-more-box) {
            border: none;
        }
    }
}
</style>